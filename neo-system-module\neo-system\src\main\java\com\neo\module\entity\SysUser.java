package com.neo.module.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.neo.base.BaseEntity;
import com.neo.constant.CommonConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024-07-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@Accessors(chain = true)
@TableName("sys_user")
public class SysUser extends BaseEntity {

    /**
     * 用户账号
     */
    @TableField("user_name")
    private String userName;

    /**
     * 用户昵称
     */
    @TableField("nick_name")
    private String nickName;

    /**
     * 密码
     */
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    @TableField("password")
    private String password;

    /**
     * 用户类型（sys系统用户）
     */
    @TableField("user_type")
    private String userType;

    /**
     * 头像地址
     */
    @TableField("avatar")
    private String avatar;

    /**
     * 部门ID
     */
    @TableField("dept_id")
    private String deptId;

    /**
     * 用户邮箱
     */
    @TableField("email")
    private String email;

    /**
     * 手机号码
     */
    @TableField("phone_number")
    private String phoneNumber;

    /**
     * 用户性别（1男 2女 0未知）
     */
    @TableField("gender")
    private String gender;

    /**
     * 帐号状态（0停用 1正常）
     */
    @TableField("status")
    private String status;

    public boolean isAdmin(){
        return isAdmin(getId());
    }

    private boolean isAdmin(String userId) {
        return CommonConstant.ADMIN_USER_ID.equals(userId);
    }

}
