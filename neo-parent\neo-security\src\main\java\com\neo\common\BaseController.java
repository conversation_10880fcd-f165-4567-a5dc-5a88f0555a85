package com.neo.common;

import com.neo.service.SysUserDetail;
import com.neo.utils.SecurityUtils;
import com.neo.utils.ServletUtils;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Slf4j
public class BaseController {

    protected String getUserId(){
        return SecurityUtils.getUserId();
    }

    protected String getUserName(){
        return SecurityUtils.getUsername();
    }

    protected SysUserDetail getLoginUser(){
        return SecurityUtils.getLoginUser();
    }

    protected HttpServletRequest getHttpRequest(){
        return ServletUtils.getRequest();
    }

    protected HttpServletResponse getHttpResponse(){
        return ServletUtils.getResponse();
    }

    protected String getParameter(String name){
        return ServletUtils.getParameter(name);
    }

}
