package com.neo.module.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.common.QueryGenerator;
import com.neo.module.entity.SysDictItem;
import com.neo.module.mapper.SysDictItemMapper;
import com.neo.module.service.SysDictItemService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 数据字典项 服务实现类
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
@Service
public class SysDictItemServiceImpl extends ServiceImpl<SysDictItemMapper, SysDictItem> implements SysDictItemService {

    @Resource
    private SysDictItemMapper sysDictItemMapper;

    @Override
    public String selectByDictTable(String dictCode, String value) {
        return sysDictItemMapper.selectByDictTable(dictCode,value);
    }

    @Override
    public String selectByOtherTable(String dictCode, String dictText, String dictTable, String value) {
        return sysDictItemMapper.selectByOtherTable(dictCode,dictText,dictTable,value);
    }

    @Override
    public IPage<SysDictItem> page(Map<String, String> params, QueryWrapper<SysDictItem> queryWrapper) {
        String value;
        queryWrapper
                .eq(StringUtils.isNotBlank((value = params.get("dictId"))), "dict_id", value)
                .eq(StringUtils.isNotBlank((value = params.get("dictCode"))), "dict_code", value)
                .eq(StringUtils.isNotBlank((value = params.get("itemText"))), "item_text", value)
                .eq(StringUtils.isNotBlank((value = params.get("itemValue"))), "item_value", value)
                .eq(StringUtils.isNotBlank((value = params.get("description"))), "description", value)
                .eq(StringUtils.isNotBlank((value = params.get("sort"))), "sort", value)
                .eq(StringUtils.isNotBlank((value = params.get("status"))), "status", value)
        ;
        IPage<SysDictItem> page = sysDictItemMapper.selectPage(QueryGenerator.initPage(params), queryWrapper);
        return page;
    }

    @Override
    public List<SysDictItem> list(SysDictItem sysDictItem) {
        QueryWrapper<SysDictItem> queryWrapper = new QueryWrapper<>(sysDictItem);
        List<SysDictItem> sysDictItems = sysDictItemMapper.selectList(queryWrapper);
        return sysDictItems;
    }

}
