server:
  port: 4444

spring:
  application:
    name: neo-auth
  profiles:
    active: @profiles.active@
  cloud:
    nacos:
      server-addr: @nacos.server@
      discovery:
        group: @nacos.discovery.group@
        namespace: ${spring.profiles.active}
      config:
        group: @nacos.config.group@
        namespace: ${spring.profiles.active}
  config:
    import:
      - optional:nacos:application-common.yml
      - optional:nacos:${spring.application.name}.yml
