package com.neo.module.event;

import com.neo.module.dto.SysLoginLogDTO;
import com.neo.module.rpc.SysUserRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class LoginLogEvent {

    @DubboReference
    private SysUserRpcService sysUserRpcService;

    @EventListener
    @Async("asyncServiceExecutor")
    public void saveLog(SysLoginLogDTO sysLoginLogDTO){
        sysUserRpcService.saveLog(sysLoginLogDTO);
    }

}
