package com.neo.config;

import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

@Component
@EnableAsync
public class AsyncConfig {

    @Bean("asyncServiceExecutor")
    public Executor asyncServiceExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 1. 核心线程数（默认线程数）
        // CPU密集型任务可以设置为 N+1 (N为CPU核心数)
        // IO密集型任务可以设置为 2N
        executor.setCorePoolSize(5);
        // 2. 最大线程数
        executor.setMaxPoolSize(10);
        // 3. 缓冲队列容量
        // 当核心线程数都在忙，新任务会进入队列
        executor.setQueueCapacity(1000);
        // 4. 线程空闲后的最大存活时间
        executor.setKeepAliveSeconds(60);
        // 5. 线程名称前缀，方便排查问题
        executor.setThreadNamePrefix("async-thread-");
        // 6. 拒绝策略：当队列和线程池都满了之后的处理策略
        // AbortPolicy: 直接抛出异常，是默认策略。
        // CallerRunsPolicy: 让提交任务的线程自己来执行该任务。这是一个很好的调节机制，可以减缓新任务的提交速度。
        // DiscardPolicy: 直接丢弃任务，不处理也不抛异常。
        // DiscardOldestPolicy: 丢弃队列中最老的一个任务，把当前任务加入队列。
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 初始化
        executor.initialize();

        return executor;
    }

}
