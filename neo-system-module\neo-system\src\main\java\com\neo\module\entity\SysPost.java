package com.neo.module.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.neo.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

;

/**
 * 岗位
 *
 * <AUTHOR>
 * @since 2023-05-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@Accessors(chain = true)
@TableName("sys_post")
public class SysPost extends BaseEntity {

    /**
     * 岗位代码
     */
    @TableField("code")
    private String code;

    /**
     * 岗位名称
     */
    @TableField("name")
    private String name;

    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 状态
     */
    @TableField("status")
    private String status;

}
