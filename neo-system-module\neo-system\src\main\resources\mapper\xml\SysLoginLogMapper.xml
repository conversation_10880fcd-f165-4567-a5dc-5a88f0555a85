<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.neo.module.mapper.SysLoginLogMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.neo.module.entity.SysLoginLog">

                    <result column="user_id" property="userId"/>
                    <result column="username" property="username"/>
                    <result column="ip_address" property="ipAddress"/>
                    <result column="browser" property="browser"/>
                    <result column="browser_version" property="browserVersion"/>
                    <result column="engine" property="engine"/>
                    <result column="engine_version" property="engineVersion"/>
                    <result column="os" property="os"/>
                    <result column="os_name" property="osName"/>
                    <result column="is_mobile" property="isMobile"/>
                    <result column="status" property="status"/>
                    <result column="message" property="message"/>
                <result column="id" property="id"/>
                <result column="is_deleted" property="deleted"/>
                <result column="create_time" property="createTime"/>
                <result column="create_by" property="createBy"/>
                <result column="update_time" property="updateTime"/>
                <result column="update_by" property="updateBy"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
id,is_deleted,create_time,create_by,update_time,update_by,user_id, username, ip_address, browser, browser_version, engine, engine_version, os, os_name, is_mobile, status, message
        </sql>

</mapper>
