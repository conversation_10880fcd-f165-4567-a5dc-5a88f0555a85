package com.neo.service;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import cn.hutool.core.date.DateUnit;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.neo.module.rpc.SysDictRpcService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

@Service
public class DictService {

    @DubboReference
    private SysDictRpcService sysDictRpcService;

    //本地缓存，过期时间2小时
    private static final TimedCache<String, String> timedCache = CacheUtil.newTimedCache(DateUnit.HOUR.getMillis() * 2);

    public String selectDict(String dictCode,String dictText,String dictTable,String key){
        String cacheKey;
        if (StringUtils.isBlank(dictTable)){
            cacheKey = dictCode + "_" + key;
        }else{
            cacheKey = dictCode + "_" + dictText + "_" + dictTable + "_" + key;
        }
        //先从本地缓存取
        if (timedCache.containsKey(cacheKey)){
            return timedCache.get(cacheKey);
        }
        synchronized (cacheKey.intern()){
            if (timedCache.containsKey(cacheKey)){
                return timedCache.get(cacheKey);
            }
            String value = sysDictRpcService.selectDict(dictCode,dictText,dictTable,key);
            timedCache.put(cacheKey,value);
            return value;
        }
    }

}
