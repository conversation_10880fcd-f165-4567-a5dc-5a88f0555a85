<template>
  <div class="config-management-container">
    <!-- 筛选表单 -->
    <t-card class="filter-card" :bordered="false">
      <t-form :data="searchFormState" :label-width="80" colon @reset="onReset" @submit="onSubmit">
        <t-row>
          <t-col :span="10">
            <t-row :gutter="[24, 24]">
              <t-col :span="4">
                <t-form-item label="配置名称" name="configName">
                  <t-input
                    v-model="searchFormState.configName"
                    class="form-item-content"
                    type="search"
                    placeholder="请输入配置名称"
                    clearable
                  />
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="配置键" name="configKey">
                  <t-input
                    v-model="searchFormState.configKey"
                    class="form-item-content"
                    type="search"
                    placeholder="请输入配置键"
                    clearable
                  />
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="配置类型" name="configType">
                  <t-select
                    v-model="searchFormState.configType"
                    class="form-item-content"
                    :options="configTypeOptions"
                    placeholder="请选择配置类型"
                    clearable
                  />
                </t-form-item>
              </t-col>
            </t-row>
          </t-col>

          <t-col :span="2" class="operation-container">
            <t-button theme="primary" type="submit" :style="{ marginLeft: 'var(--td-comp-margin-s)' }">
              查询
            </t-button>
            <t-button type="reset" variant="base" theme="default">
              重置
            </t-button>
          </t-col>
        </t-row>
      </t-form>
    </t-card>

    <!-- 表格容器 -->
    <t-card class="table-card" :bordered="false">
      <div class="table-header">
        <div class="left-operation-container">
          <t-button theme="primary" @click="handleAdd">
            新增配置
          </t-button>
          <t-button theme="danger" :disabled="!selectedRowKeys.length" @click="handleBatchDelete">
            删除配置
          </t-button>
        </div>
      </div>

      <t-table
        :data="dataSource"
        :columns="columns"
        :row-key="rowKey"
        :selected-row-keys="selectedRowKeys"
        vertical-align="top"
        :hover="true"
        :pagination="pagination"
        :loading="loading"
        @page-change="handlePageChange"
        @select-change="onSelectChange"
      >
        <template #configValue="{ row }">
          <!-- 布尔类型显示开关 -->
          <t-switch
            v-if="row.configType === 'boolean'"
            :model-value="String(row.configValue)"
            :custom-value="['true', 'false']"
            size="small"
            @change="(value: any) => handleBooleanChange(row, String(value))"
          />
          <!-- 其他类型显示文本 -->
          <span v-else>{{ row.configValue }}</span>
        </template>
        
        <template #configType="{ row }">
          <t-tag v-if="row.configType === 'text'" theme="default" variant="light">
            文本
          </t-tag>
          <t-tag v-else-if="row.configType === 'number'" theme="primary" variant="light">
            数字
          </t-tag>
          <t-tag v-else-if="row.configType === 'boolean'" theme="success" variant="light">
            布尔值
          </t-tag>
          <t-tag v-else-if="row.configType === 'json'" theme="warning" variant="light">
            JSON
          </t-tag>
          <t-tag v-else theme="default" variant="light">
            {{ row.configType }}
          </t-tag>
        </template>
        
        <template #op="slotProps">
          <t-space>
            <t-link theme="primary" @click="handleEdit(slotProps.row)">编辑</t-link>
            <t-popconfirm 
              content="确定要删除吗？" 
              @confirm="handleDelete(slotProps.row)"
            >
              <t-link theme="danger">删除</t-link>
            </t-popconfirm>
          </t-space>
        </template>
      </t-table>
    </t-card>

    <!-- 系统配置编辑弹窗 -->
    <EditModel 
      v-model:visible="formVisible" 
      :is-edit="isEdit" 
      :config-data="editConfigData"
      @success="onFormSuccess"
    />
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue';
  import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
  import type { PageInfo, PrimaryTableCol } from 'tdesign-vue-next';
  import {
    getConfigPage,
    deleteConfig,
    updateConfig,
  } from '@/api/config';
  import { SysConfig, ConfigPageParams, ConfigListResult } from '@/api/model/configModel';
  import EditModel from './components/EditModel.vue';

  // 配置类型选项
  const configTypeOptions = [
    { value: 'text', label: '文本' },
    { value: 'number', label: '数字' },
    { value: 'boolean', label: '布尔值' },
    { value: 'json', label: 'JSON' },
  ];

  // 定义表格列
  const columns: PrimaryTableCol[] = [
    { colKey: 'row-select', type: 'multiple' as const, width: 60, fixed: 'left' as const },
    {
      title: '配置名称',
      colKey: 'configName',
      width: 200,
      ellipsis: true,
    },
    {
      title: '配置键',
      colKey: 'configKey',
      width: 200,
      ellipsis: true,
    },
    {
      title: '配置值',
      colKey: 'configValue',
      width: 250,
      ellipsis: true,
    },
    {
      title: '配置类型',
      colKey: 'configType',
      width: 120,
    },
    {
      title: '备注',
      colKey: 'remark',
      width: 200,
      ellipsis: true,
    },
    {
      title: '创建时间',
      colKey: 'createTime',
      width: 180,
    },
    {
      title: '操作',
      colKey: 'op',
      width: 150,
      fixed: 'right' as const,
    },
  ];

  // 表格相关数据
  const dataSource = ref<SysConfig[]>([]);
  const loading = ref(false);
  const selectedRowKeys = ref<string[]>([]);
  const rowKey = 'id';
  
  const pagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 搜索表单
  const searchFormState = reactive<ConfigPageParams>({
    page: 1,
    pageSize: 10,
    configName: '',
    configKey: '',
    configType: '',
  });

  // 对话框相关
  const formVisible = ref(false);
  const isEdit = ref(false);
  const editConfigData = ref<SysConfig>();

  // 获取数据
  const loadData = async (params: ConfigPageParams) => {
    loading.value = true;
    try {
      const res: ConfigListResult = await getConfigPage(params);
      dataSource.value = res.records;
      pagination.total = res.total;
      pagination.current = res.current;
    } catch (error) {
      console.error('获取系统配置列表失败:', error);
      MessagePlugin.error('获取系统配置列表失败');
    } finally {
      loading.value = false;
    }
  };

  // 查询
  const searchQuery = () => {
    searchFormState.page = 1;
    pagination.current = 1;
    loadData(searchFormState);
  };

  // 表单提交
  const onSubmit = () => {
    searchQuery();
  };

  // 表单重置
  const onReset = () => {
    searchFormState.configName = '';
    searchFormState.configKey = '';
    searchFormState.configType = '';
    searchQuery();
  };

  // 表格分页变化
  const handlePageChange = (curr: any) => {
    pagination.current = curr.current;
    pagination.pageSize = curr.pageSize;
    searchFormState.page = curr.current;
    searchFormState.pageSize = curr.pageSize;
    loadData(searchFormState);
  };

  // 选择变化
  const onSelectChange = (selectedKeys: (string | number)[]) => {
    selectedRowKeys.value = selectedKeys as string[];
  };

  // 新增
  const handleAdd = () => {
    isEdit.value = false;
    editConfigData.value = undefined;
    formVisible.value = true;
  };

  // 编辑
  const handleEdit = (record: SysConfig) => {
    isEdit.value = true;
    editConfigData.value = record;
    formVisible.value = true;
  };

  // 删除
  const handleDelete = async (record: SysConfig) => {
    try {
      await deleteConfig([record.id || '']);
      MessagePlugin.success('删除成功');
      searchQuery();
    } catch (error) {
      console.error('删除失败:', error);
      MessagePlugin.error('删除失败');
    }
  };

  // 批量删除
  const handleBatchDelete = async () => {
    try {
      await deleteConfig(selectedRowKeys.value);
      MessagePlugin.success('删除系统配置成功');
      selectedRowKeys.value = [];
      searchQuery();
    } catch (error) {
      console.error('删除失败:', error);
      MessagePlugin.error('删除系统配置失败');
    }
  };

  // 处理布尔值开关变化
  const handleBooleanChange = (record: SysConfig, newValue: string) => {
    const oldValue = record.configValue;
    const valueText = newValue === 'true' ? '开启' : '关闭';
    
    const dialog = DialogPlugin.confirm({
      header: '确认修改',
      body: `确定要将配置“${record.configName}”的值修改为“${valueText}”吗？`,
      confirmBtn: '确认',
      cancelBtn: '取消',
      onConfirm: async () => {
        try {
          // 调用更新接口
          const updateData = {
            ...record,
            configValue: newValue
          };
          
          await updateConfig(updateData);
          MessagePlugin.success('配置修改成功');
          
          // 更新本地数据
          const index = dataSource.value.findIndex(item => item.id === record.id);
          if (index !== -1) {
            dataSource.value[index].configValue = newValue;
          }
          
          // 关闭弹窗
          dialog.destroy();
        } catch (error) {
          console.error('配置修改失败:', error);
          MessagePlugin.error('配置修改失败');
          // 失败时也要关闭弹窗
          dialog.destroy();
        }
      },
      onCancel: () => {
        // 取消时关闭弹窗
        dialog.destroy();
      }
    });
  };

  // 表单操作成功回调
  const onFormSuccess = () => {
    searchQuery();
  };

  onMounted(() => {
    loadData(searchFormState);
  });
</script>

<style lang="less" scoped>
  .config-management-container {
    background-color: var(--td-bg-color-container);
    padding: var(--td-comp-paddingTB-xxl) var(--td-comp-paddingLR-xxl);
    border-radius: var(--td-radius-medium);
    
    .filter-card {
      margin-bottom: var(--td-comp-margin-xxl);
    }
    
    .table-card {
      .table-header {
        margin-bottom: var(--td-comp-margin-xl);
        
        .left-operation-container {
          display: flex;
          align-items: center;
          gap: 16px;
        }
      }
    }
  }

  .form-item-content {
    width: 100%;
  }

  .operation-container {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 8px;
  }
</style>