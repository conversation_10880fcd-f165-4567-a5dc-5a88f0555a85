package com.neo.module.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.neo.module.dto.SysUserDTO;
import com.neo.module.entity.SysUser;
import com.neo.module.entity.vo.SysUserVO;

import java.util.List;
import java.util.Map;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2024-07-08
 */
public interface SysUserService extends IService<SysUser> {

    IPage<SysUser> page(Map<String, String> params, QueryWrapper<SysUser> queryWrapper);

    SysUserDTO getUserByUsername(String username);

    SysUserVO getUserInfo(String id);

    boolean updateUser(SysUserVO sysUserVO);

    boolean addUser(SysUserVO sysUserVO);

}
