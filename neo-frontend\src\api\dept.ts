import type { SysDept, SysDeptTree, DeptPageParams, DeptListResult } from '@/api/model/deptModel';
import { request } from '@/utils/request';

const Api = {
  DeptTree: '/system/sysDept/listTree',
  DeptPage: '/system/sysDept/page',
  DeptList: '/system/sysDept/list',
  DeptAdd: '/system/sysDept/add',
  DeptUpdate: '/system/sysDept/update',
  DeptDelete: '/system/sysDept',
  DeptDetail: '/system/sysDept',
};

// 获取部门树形列表
export function getDeptTree(params?: any) {
  return request.post<SysDeptTree[]>({
    url: Api.DeptTree,
    data: params || {},
  });
}

// 分页查询部门
export function getDeptPage(params: DeptPageParams) {
  return request.post<DeptListResult>({
    url: Api.DeptPage,
    data: params,
  });
}

// 获取部门列表
export function getDeptList(params?: any) {
  return request.post<SysDept[]>({
    url: Api.DeptList,
    data: params || {},
  });
}

// 获取部门详情
export function getDeptById(id: string) {
  return request.get<SysDept>({
    url: `${Api.DeptDetail}/${id}`,
  });
}

// 新增部门
export function addDept(data: Omit<SysDept, 'id'>) {
  return request.post({
    url: Api.DeptAdd,
    data,
  });
}

// 更新部门
export function updateDept(data: SysDept) {
  return request.put({
    url: Api.DeptUpdate,
    data,
  });
}

// 删除部门
export function deleteDept(ids: string[]) {
  return request.delete({
    url: Api.DeptDelete,
    data: ids,
  });
}