package com.neo.module.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.neo.module.entity.SysDept;
import com.neo.module.entity.vo.SysDeptTree;

import java.util.List;
import java.util.Map;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2023-04-21
 */
public interface SysDeptService extends IService<SysDept> {

    IPage<SysDept> page(Map<String, String> params, QueryWrapper<SysDept> queryWrapper);

    List<SysDept> list(SysDept sysDept);

    List<SysDeptTree> listTree(SysDept sysDept);
    
    /**
     * 新增部门（维护ancestors字段）
     *
     * @param sysDept 部门对象
     * @return 是否成功
     */
    boolean saveDept(SysDept sysDept);
    
    /**
     * 更新部门（维护ancestors字段）
     *
     * @param sysDept 部门对象
     * @return 是否成功
     */
    boolean updateDept(SysDept sysDept);
}
