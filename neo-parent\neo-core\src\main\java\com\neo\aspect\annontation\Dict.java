package com.neo.aspect.annontation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 字典注解DictAspect
 * <AUTHOR>
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface Dict {

    /**
     * 方法描述: 数据code
     *
     * @return 返回类型： String
     */
    String dictCode();

    /**
     * 方法描述: 数据表名
     *
     * @return 返回类型： String
     */
    String dictTable() default "";

    /**
     * 方法描述: 数据表字段名
     *
     * @return 返回类型： String
     */
    String dictText() default "";

}
