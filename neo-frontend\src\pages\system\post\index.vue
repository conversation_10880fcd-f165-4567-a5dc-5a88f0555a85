<template>
  <div class="post-management-container">
    <!-- 筛选表单 -->
    <t-card class="filter-card" :bordered="false">
      <t-form :data="searchFormState" :label-width="80" colon @reset="onReset" @submit="onSubmit">
        <t-row>
          <t-col :span="10">
            <t-row :gutter="[24, 24]">
              <t-col :span="4">
                <t-form-item label="岗位名称" name="name">
                  <t-input
                    v-model="searchFormState.name"
                    class="form-item-content"
                    type="search"
                    placeholder="请输入岗位名称"
                    clearable
                  />
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="岗位编码" name="code">
                  <t-input
                    v-model="searchFormState.code"
                    class="form-item-content"
                    placeholder="请输入岗位编码"
                    clearable
                  />
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="状态" name="status">
                  <t-select
                    v-model="searchFormState.status"
                    class="form-item-content"
                    :options="statusOptions"
                    placeholder="请选择状态"
                    clearable
                  />
                </t-form-item>
              </t-col>
            </t-row>
          </t-col>

          <t-col :span="2" class="operation-container">
            <t-button theme="primary" type="submit" :style="{ marginLeft: 'var(--td-comp-margin-s)' }">
              查询
            </t-button>
            <t-button type="reset" variant="base" theme="default">
              重置
            </t-button>
          </t-col>
        </t-row>
      </t-form>
    </t-card>

    <!-- 表格容器 -->
    <t-card class="table-card" :bordered="false">
      <div class="table-header">
        <div class="left-operation-container">
          <t-button @click="handleAdd">新增岗位</t-button>
        </div>
      </div>

      <t-table
        :data="paginationData.dataSource.value"
        :columns="columns"
        :row-key="rowKey"
        vertical-align="top"
        :hover="true"
        :pagination="paginationData.tableConfig.value"
        :loading="paginationData.loading.value"
        @page-change="(pageInfo: any) => paginationData.handlePageChange(pageInfo, loadPostData, searchFormState)"
      >
        <template #status="{ row }">
          <t-tag v-if="row.status === '1'" theme="success" variant="light">
            启用
          </t-tag>
          <t-tag v-else theme="danger" variant="light">
            禁用
          </t-tag>
        </template>
        
        <template #op="slotProps">
          <t-space>
            <t-link theme="primary" @click="handleEdit(slotProps.row)">编辑</t-link>
            <t-popconfirm 
              content="确定要删除吗？" 
              @confirm="handleDelete(slotProps.row)"
            >
              <t-link theme="danger">删除</t-link>
            </t-popconfirm>
          </t-space>
        </template>
      </t-table>
    </t-card>

    <!-- 岗位编辑弹窗 -->
    <EditModel 
      v-model:visible="formVisible" 
      :is-edit="isEdit" 
      :post-data="editPostData"
      @success="onFormSuccess"
    />
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted } from 'vue';
  import {
    getPostPage,
    deletePost,
  } from '@/api/post';
  import { SysPost, PostPageParams, PostListResult } from '@/api/model/postModel';
  import { usePagination, CommonPageParams, CommonPageResult } from '@/hooks';
  import EditModel from './components/EditModel.vue';

  // 状态选项
  const statusOptions = [
    { value: '1', label: '启用' },
    { value: '0', label: '禁用' },
  ];

  // 定义表格列
  const columns = [
    {
      title: '岗位编码',
      colKey: 'code',
    },
    {
      title: '岗位名称',
      colKey: 'name',
    },
    {
      title: '排序',
      colKey: 'sort',
    },
    {
      title: '状态',
      colKey: 'status',
    },
    {
      title: '操作',
      colKey: 'op',
      width: 150,
    },
  ];

  // 使用通用分页 hook
  const paginationData = usePagination<SysPost>({
    defaultCurrent: 1,
    defaultPageSize: 10,
    paramType: 'default' // 使用 current/size 参数
  });

  // 搜索表单
  const searchFormState = reactive<Omit<PostPageParams, 'current' | 'pageSize'>>({
    name: '',
    code: '',
    status: '',
  });

  // 对话框相关
  const formVisible = ref(false);
  const isEdit = ref(false);
  const editPostData = ref<SysPost>();

  // 数据加载函数
  const loadPostData = async (params: CommonPageParams): Promise<CommonPageResult<SysPost>> => {
    const res = await getPostPage(params as PostPageParams);
    return {
      records: res.records,
      total: res.total,
      current: res.current,
      size: res.size,
    };
  };

  // 查询
  const searchQuery = () => {
    paginationData.resetToFirstPage(loadPostData, searchFormState);
  };

  // 表单提交
  const onSubmit = () => {
    searchQuery();
  };

  // 表单重置
  const onReset = () => {
    searchFormState.name = '';
    searchFormState.code = '';
    searchFormState.status = '';
    searchQuery();
  };

  const rowKey = 'id';

  // 新增
  const handleAdd = () => {
    isEdit.value = false;
    editPostData.value = undefined;
    formVisible.value = true;
  };

  // 编辑
  const handleEdit = (record: SysPost) => {
    isEdit.value = true;
    editPostData.value = record;
    formVisible.value = true;
  };

  // 删除
  const handleDelete = async (record: SysPost) => {
    try {
      await deletePost([record.id || '']);
      searchQuery();
    } catch (error) {
      console.error('删除失败:', error);
    }
  };

  // 表单操作成功回调
  const onFormSuccess = () => {
    paginationData.refreshData(loadPostData, searchFormState);
  };

  onMounted(() => {
    paginationData.loadData(paginationData.buildPageParams(searchFormState), loadPostData);
  });
</script>

<style lang="less" scoped>
  .post-management-container {
    background-color: var(--td-bg-color-container);
    padding: var(--td-comp-paddingTB-xxl) var(--td-comp-paddingLR-xxl);
    border-radius: var(--td-radius-medium);
    
    .filter-card {
      margin-bottom: var(--td-comp-margin-xxl);
    }
    
    .table-card {
      .table-header {
        margin-bottom: var(--td-comp-margin-xl);
        
        .left-operation-container {
          display: flex;
          align-items: center;
          gap: 10px;
        }
      }
    }
  }

  .form-item-content {
    width: 100%;
  }

  .operation-container {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
</style>