package com.neo.module.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.common.QueryGenerator;
import com.neo.module.entity.SysPost;
import com.neo.module.entity.SysUserPost;
import com.neo.module.mapper.SysPostMapper;
import com.neo.module.mapper.SysUserPostMapper;
import com.neo.module.service.SysPostService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 岗位 服务实现类
 *
 * <AUTHOR>
 * @since 2023-05-24
 */
@Service
public class SysPostServiceImpl extends ServiceImpl<SysPostMapper, SysPost> implements SysPostService {

    @Resource
    private SysPostMapper sysPostMapper;

    @Resource
    private SysUserPostMapper sysUserPostMapper;

    @Override
    public IPage<SysPost> page(Map<String, String> params, QueryWrapper<SysPost> queryWrapper) {
        String value;
        queryWrapper
                .eq(StringUtils.isNotBlank((value = params.get("code"))), "code", value)
                .eq(StringUtils.isNotBlank((value = params.get("name"))), "name", value)
                .eq(StringUtils.isNotBlank((value = params.get("sort"))), "sort", value)
                .eq(StringUtils.isNotBlank((value = params.get("status"))), "status", value)
                .eq(StringUtils.isNotBlank((value = params.get("craeteBy"))), "craete_by", value)
        ;
        IPage<SysPost> page = sysPostMapper.selectPage(QueryGenerator.initPage(params), queryWrapper);
        return page;
    }

    @Override
    public List<SysPost> list(SysPost sysPost) {
        QueryWrapper<SysPost> queryWrapper = new QueryWrapper<>(sysPost);
        return sysPostMapper.selectList(queryWrapper);
    }

    @Override
    public List<String> getPostIdsByUserId(String id) {
        LambdaQueryWrapper<SysUserPost> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUserPost::getUserId, id);
        List<SysUserPost> sysUserPosts = sysUserPostMapper.selectList(wrapper);
        if (sysUserPosts != null && !sysUserPosts.isEmpty()){
            return sysUserPosts.stream().map(SysUserPost::getPostId).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

}
