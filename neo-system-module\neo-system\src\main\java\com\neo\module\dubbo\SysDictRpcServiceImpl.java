package com.neo.module.dubbo;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.neo.module.rpc.SysDictRpcService;
import com.neo.module.service.SysDictItemService;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

@DubboService
public class SysDictRpcServiceImpl implements SysDictRpcService {

    @Resource
    private SysDictItemService sysDictItemService;

    @Override
    public String selectDict(String dictCode, String dictText, String dictTable, String key) {
        if (StringUtils.isBlank(dictCode)){
            return key;
        }
        String dictValue;
        if (StringUtils.isBlank(dictTable) || StringUtils.isBlank(dictText)){
            dictValue = sysDictItemService.selectByDictTable(dictCode,key);
        }else{
            dictValue = sysDictItemService.selectByOtherTable(dictCode,dictText,dictTable,key);
        }
        return dictValue;
    }

}
