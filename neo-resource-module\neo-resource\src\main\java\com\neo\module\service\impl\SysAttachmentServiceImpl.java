package com.neo.module.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.neo.exception.BusinessException;
import com.neo.common.QueryGenerator;
import com.neo.module.entity.SysAttachment;
import com.neo.module.mapper.SysAttachmentMapper;
import com.neo.module.service.StorageService;
import com.neo.module.service.SysAttachmentService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.neo.utils.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 附件信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-06
 */
@Slf4j
@Service
public class SysAttachmentServiceImpl extends ServiceImpl<SysAttachmentMapper, SysAttachment> implements SysAttachmentService {

    @Resource
    private SysAttachmentMapper sysAttachmentMapper;

    @Override
    public IPage<SysAttachment> page(Map<String, String> params, QueryWrapper<SysAttachment> queryWrapper) {
        String value;
        queryWrapper
                .eq(StringUtils.isNotBlank((value = params.get("fileName"))), "file_name", value)
                .eq(StringUtils.isNotBlank((value = params.get("filePath"))), "file_path", value)
                .eq(StringUtils.isNotBlank((value = params.get("fileSize"))), "file_size", value)
                .eq(StringUtils.isNotBlank((value = params.get("fileUrl"))), "file_url", value)
                .eq(StringUtils.isNotBlank((value = params.get("fileType"))), "file_type", value)
                .eq(StringUtils.isNotBlank((value = params.get("storageType"))), "storage_type", value)
                .eq(StringUtils.isNotBlank((value = params.get("md5"))), "md5", value)
                .eq(StringUtils.isNotBlank((value = params.get("status"))), "status", value)
        ;
        IPage<SysAttachment> page = sysAttachmentMapper.selectPage(QueryGenerator.initPage(params), queryWrapper);
        return page;
    }

    @Override
    public List<SysAttachment> list(SysAttachment sysAttachment) {
        QueryWrapper<SysAttachment> queryWrapper = new QueryWrapper<>(sysAttachment);
        List<SysAttachment> sysAttachments = sysAttachmentMapper.selectList(queryWrapper);
        return sysAttachments;
    }

    @Override
    public void deleteFileByIds(Set<String> ids) throws Exception {
        for (String id : ids) {
            SysAttachment sysAttachment = this.getById(id);
            StorageService service = SpringContextUtils.getBean(sysAttachment.getStorageType() + "StorageService", StorageService.class);
            service.delete(sysAttachment.getFilePath());
            this.removeById(id);
        }
    }

    @Override
    public void downloadFileById(String id, HttpServletResponse response) throws Exception {
        SysAttachment sysAttachment = this.getById(id);
        if (sysAttachment == null){
            throw new BusinessException("文件不存在");
        }
        StorageService service = SpringContextUtils.getBean(sysAttachment.getStorageType() + "StorageService", StorageService.class);
        service.download(sysAttachment.getFilePath(), response);
    }

}
