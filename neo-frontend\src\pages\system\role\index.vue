<template>
  <div class="role-management-container">
    <!-- 筛选表单 -->
    <t-card class="filter-card" :bordered="false">
      <t-form :data="searchFormState" :label-width="80" colon @reset="onReset" @submit="onSubmit">
        <t-row>
          <t-col :span="10">
            <t-row :gutter="[24, 24]">
              <t-col :span="4">
                <t-form-item label="角色名称" name="roleName">
                  <t-input
                    v-model="searchFormState.roleName"
                    class="form-item-content"
                    type="search"
                    placeholder="请输入角色名称"
                    clearable
                  />
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="角色编码" name="roleKey">
                  <t-input
                    v-model="searchFormState.roleKey"
                    class="form-item-content"
                    placeholder="请输入角色编码"
                    clearable
                  />
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="状态" name="status">
                  <t-select
                    v-model="searchFormState.status"
                    class="form-item-content"
                    :options="statusOptions"
                    placeholder="请选择状态"
                    clearable
                  />
                </t-form-item>
              </t-col>
            </t-row>
          </t-col>

          <t-col :span="2" class="operation-container">
            <t-button theme="primary" type="submit" :style="{ marginLeft: 'var(--td-comp-margin-s)' }">
              查询
            </t-button>
            <t-button type="reset" variant="base" theme="default">
              重置
            </t-button>
          </t-col>
        </t-row>
      </t-form>
    </t-card>

    <!-- 表格容器 -->
    <t-card class="table-card" :bordered="false">
      <div class="table-header">
        <div class="left-operation-container">
          <t-button @click="handleAdd">新增角色</t-button>
        </div>
      </div>

      <t-table
        :data="paginationData.dataSource.value"
        :columns="columns"
        :row-key="rowKey"
        vertical-align="top"
        :hover="true"
        :pagination="paginationData.tableConfig.value"
        :loading="paginationData.loading.value"
        @page-change="(pageInfo: any) => paginationData.handlePageChange(pageInfo, loadRoleData, searchFormState)"
      >
        <template #status="{ row }">
          <t-tag v-if="row.status === '1'" theme="success" variant="light">
            启用
          </t-tag>
          <t-tag v-else theme="danger" variant="light">
            禁用
          </t-tag>
        </template>
        
        <template #op="slotProps">
          <t-space>
            <t-link theme="primary" @click="handleEdit(slotProps.row)">编辑</t-link>
            <t-popconfirm 
              content="确定要删除吗？" 
              @confirm="handleDelete(slotProps.row)"
            >
              <t-link theme="danger">删除</t-link>
            </t-popconfirm>
          </t-space>
        </template>
      </t-table>
    </t-card>

    <!-- 角色编辑弹窗 -->
    <EditModel 
      v-model:visible="formVisible" 
      :is-edit="isEdit" 
      :role-data="editRoleData"
      @success="onFormSuccess"
    />
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted } from 'vue';
  import {
    getRolePage,
    deleteRole,
    getRoleInfo,
  } from '@/api/role';
  import { SysRole, RolePageParams, RoleListResult } from '@/api/model/roleModel';
  import { usePagination, CommonPageParams, CommonPageResult } from '@/hooks';
  import EditModel from './components/EditModel.vue';

  // 状态选项
  const statusOptions = [
    { value: '1', label: '启用' },
    { value: '0', label: '禁用' },
  ];

  // 定义表格列
  const columns = [
    {
      title: '角色名称',
      colKey: 'roleName',
    },
    {
      title: '角色编码',
      colKey: 'roleKey',
    },
    {
      title: '描述',
      colKey: 'remark',
    },
    {
      title: '状态',
      colKey: 'status',
    },
    {
      title: '操作',
      colKey: 'op',
      width: 150,
    },
  ];

  // 使用通用分页 hook
  const paginationData = usePagination<SysRole>({
    defaultCurrent: 1,
    defaultPageSize: 10,
  });

  // 搜索表单
  const searchFormState = reactive<Omit<RolePageParams, 'current' | 'pageSize'>>({
    roleName: '',
    roleKey: '',
    status: '',
  });

  // 对话框相关
  const formVisible = ref(false);
  const isEdit = ref(false);
  const editRoleData = ref<SysRole>();
  const loading = ref(false);

  // 数据加载函数
  const loadRoleData = async (params: CommonPageParams): Promise<CommonPageResult<SysRole>> => {
    const res = await getRolePage(params as RolePageParams);
    return {
      records: res.records,
      total: res.total,
      current: res.current,
      size: params.pageSize || paginationData.tableConfig.value?.pageSize || 10,
    };
  };

  // 查询
  const searchQuery = () => {
    paginationData.resetToFirstPage(loadRoleData, searchFormState);
  };

  // 表单提交
  const onSubmit = () => {
    searchQuery();
  };

  // 表单重置
  const onReset = () => {
    searchFormState.roleName = '';
    searchFormState.roleKey = '';
    searchFormState.status = '';
    searchQuery();
  };

  const rowKey = 'id';

  // 新增
  const handleAdd = () => {
    isEdit.value = false;
    editRoleData.value = undefined;
    formVisible.value = true;
  };

  // 编辑
  const handleEdit = async (record: SysRole) => {
    if (!record.id) {
      console.error('角色ID不存在');
      return;
    }
    
    loading.value = true;
    try {
      // 从后端获取完整的角色信息
      const roleInfo = await getRoleInfo(record.id);
      isEdit.value = true;
      editRoleData.value = roleInfo;
      formVisible.value = true;
    } catch (error) {
      console.error('获取角色信息失败:', error);
    } finally {
      loading.value = false;
    }
  };

  // 删除
  const handleDelete = async (record: SysRole) => {
    try {
      await deleteRole([record.id || '']);
      searchQuery();
    } catch (error) {
      console.error('删除失败:', error);
    }
  };

  // 表单操作成功回调
  const onFormSuccess = () => {
    paginationData.refreshData(loadRoleData, searchFormState);
  };

  onMounted(() => {
    paginationData.loadData(paginationData.buildPageParams(searchFormState), loadRoleData);
  });
</script>

<style lang="less" scoped>
  .role-management-container {
    background-color: var(--td-bg-color-container);
    padding: var(--td-comp-paddingTB-xxl) var(--td-comp-paddingLR-xxl);
    border-radius: var(--td-radius-medium);
    
    .filter-card {
      margin-bottom: var(--td-comp-margin-xxl);
    }
    
    .table-card {
      .table-header {
        margin-bottom: var(--td-comp-margin-xl);
        
        .left-operation-container {
          display: flex;
          align-items: center;
          gap: 10px;
        }
      }
    }
  }

  .form-item-content {
    width: 100%;
  }

  .operation-container {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
</style>