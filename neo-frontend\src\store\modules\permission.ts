import { defineStore } from 'pinia';
import type { RouteRecordRaw } from 'vue-router';

import type { RouteItem } from '@/api/model/permissionModel';
import { getMenu } from '@/api/menu'; // 使用原有的getMenu接口
import router, { fixedRouterList, homepageRouterList } from '@/router';
import { store } from '@/store';
import { transformObjectToRoute } from '@/utils/route';

interface PermissionState {
  whiteListRouters: string[];
  routers: RouteRecordRaw[];
  removeRoutes: RouteRecordRaw[];
  asyncRoutes: RouteRecordRaw[];
  isBuildingRoutes: boolean; // 添加标志位避免重复请求
  userMenus: any[]; // 存储用户菜单数据，用于权限验证
}

// 将后端菜单数据转换为前端RouteItem格式
function transformMenuToRouteItem(menu: any): RouteItem {
  // 为目录类型菜单设置默认路径，防止路径为空
  const menuPath = menu.path || (menu.menuType === 'M' ? `/${menu.name}` : '');
  
  return {
    path: menuPath,
    name: menu.name || '',
    component: menu.component,
    redirect: menu.redirect,
    meta: {
      title: menu.name || '',
      ...(menu.icon && { icon: menu.icon }),
      hidden: menu.visible === '0', // visible为0表示隐藏（不在导航中显示）
      keepAlive: menu.isCache === '1', // isCache为1表示需要缓存
      orderNo: menu.sort,
    },
    children: menu.children ? menu.children
      .filter((child: any) => {
        // 过滤掉按钮类型菜单和停用的菜单
        return child.menuType !== 'F' && child.status === '1';
      })
      .map(transformMenuToRouteItem) : [],
  };
}

export const usePermissionStore = defineStore('permission', {
  state: (): PermissionState => ({
    whiteListRouters: ['/login'],
    routers: [] as RouteRecordRaw[],
    removeRoutes: [] as RouteRecordRaw[],
    asyncRoutes: [] as RouteRecordRaw[],
    isBuildingRoutes: false, // 初始化标志位
    userMenus: [], // 初始化用户菜单数据
  }),
  actions: {
    async initRoutes() {
      const accessedRouters = this.asyncRoutes;

      // 在菜单展示全部路由
      this.routers = [...homepageRouterList, ...accessedRouters];
      // 在菜单只展示动态路由和首页
      // this.routers = [...homepageRouterList, ...accessedRouters];
      // 在菜单只展示动态路由
      // this.routers = [...accessedRouters];
    },
    async buildAsyncRoutes() {
      // 如果正在构建路由或者已经构建过路由，则直接返回
      if (this.isBuildingRoutes) {
        return [];
      }
      
      try {
        this.isBuildingRoutes = true; // 设置标志位
        
        // 发起菜单权限请求 获取菜单列表
        const menuResult = await getMenu(); // 使用原有的getMenu接口
        
        if (!menuResult) {
          console.warn('No menu data received, using empty array');
          this.asyncRoutes = [];
          this.userMenus = [];
        } else {
          // 存储原始菜单数据用于权限验证
          this.userMenus = menuResult;
          
          // 过滤掉按钮类型菜单和停用的菜单，只处理启用状态的目录(M)和菜单(C)类型
          const filteredMenus = menuResult.filter(menu => 
            (menu.menuType === 'M' || menu.menuType === 'C') && menu.status === '1'
          );
          // 转换后端菜单数据为前端路由格式
          const routeItems: RouteItem[] = filteredMenus.map(transformMenuToRouteItem);
          this.asyncRoutes = transformObjectToRoute(routeItems);
        }
        await this.initRoutes();
        return this.asyncRoutes;
      } catch (error) {
        console.error("Can't build routes", error);
        // 出错时使用默认路由
        this.asyncRoutes = [];
        await this.initRoutes();
        return [];
      } finally {
        this.isBuildingRoutes = false; // 重置标志位
      }
    },
    async restoreRoutes() {
      // 不需要在此额外调用initRoutes更新侧边导肮内容，在登录后asyncRoutes为空会调用
      this.asyncRoutes.forEach((item) => {
        if (item.name) {
          router.removeRoute(item.name);
        }
      });
      this.asyncRoutes = [];
      this.userMenus = [];
      this.isBuildingRoutes = false; // 重置标志位
    },
    
    // 检查用户是否有权限访问指定路径
    hasPermissionToRoute(path: string): boolean {
      // 白名单路由直接允许访问
      if (this.whiteListRouters.includes(path)) {
        return true;
      }
      
      // 首页路由允许访问
      if (path === '/' || path === '/dashboard' || path === '/dashboard/base') {
        return true;
      }
      
      // 如果没有菜单数据，拒绝访问
      if (!this.userMenus || this.userMenus.length === 0) {
        return false;
      }
      
      // 递归检查菜单路径
      const checkMenuPath = (menus: any[], targetPath: string): boolean => {
        for (const menu of menus) {
          // 检查当前菜单（只检查启用状态的菜单）
          if (menu.path === targetPath && menu.status === '1') {
            return true;
          }
          
          // 递归检查子菜单
          if (menu.children && menu.children.length > 0) {
            if (checkMenuPath(menu.children, targetPath)) {
              return true;
            }
          }
        }
        return false;
      };
      
      return checkMenuPath(this.userMenus, path);
    },
  },
});

export function getPermissionStore() {
  return usePermissionStore(store);
}