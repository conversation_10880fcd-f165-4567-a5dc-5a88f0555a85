package com.neo.module.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.neo.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

;

/**
 * 菜单权限表
 *
 * <AUTHOR>
 * @since 2023-03-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@Accessors(chain = true)
@TableName("sys_menu")
public class SysMenu extends BaseEntity {

    /**
     * 菜单图标
     */
    @TableField("icon")
    private String icon;

    /**
     * 父菜单ID
     */
    @TableField("parent_id")
    private String parentId;

    /**
     * 菜单名称
     */
    @TableField("name")
    private String name;

    /**
     * 显示顺序
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 路由地址
     */
    @TableField("path")
    private String path;

    /**
     * 组件路径
     */
    @TableField("component")
    private String component;


    /**
     * 菜单类型（M目录 C菜单 F按钮）
     */
    @TableField("menu_type")
    private String menuType;

    /**
     * 菜单状态（1显示 0隐藏）
     */
    @TableField("visible")
    private String visible;

    /**
     * 权限标识
     */
    @TableField("permission_code")
    private String permissionCode;

    /**
     * 菜单状态（1正常 0停用）
     */
    @TableField("status")
    private String status;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;


}
