<template>
  <t-dialog 
    v-model:visible="dialogVisible" 
    :header="dialogTitle" 
    :width="680" 
    :confirm-btn="{ loading: formLoading }" 
    @confirm="onConfirm" 
    @cancel="onCancel"
  >
    <template v-if="dialogVisible">
      <t-form ref="form" :data="formData" :rules="rules" :label-width="80" @submit="onSubmit">
        <t-form-item label="上级部门" name="parentId">
          <t-tree-select 
            v-model="formData.parentId" 
            :data="parentDeptOptions"
            :tree-props="treeProps"
            placeholder="请选择上级部门"
            clearable
            filterable
            :disabled="isEditMode && !canChangeParent"
          />
        </t-form-item>
        
        <t-form-item label="部门名称" name="name">
          <t-input 
            v-model="formData.name" 
            placeholder="请输入部门名称"
          />
        </t-form-item>
        
        <t-form-item label="显示排序" name="sort">
          <t-input-number 
            v-model="formData.sort" 
            :min="0"
            placeholder="请输入排序"
          />
        </t-form-item>
        
        <t-form-item label="部门状态" name="status">
          <t-switch 
            v-model="formData.status" 
            :custom-value="['1', '0']" 
            size="small"
          />
        </t-form-item>
        
        <t-form-item label="负责人" name="leader">
          <t-input 
            v-model="formData.leader" 
            placeholder="请输入负责人"
          />
        </t-form-item>
        
        <t-form-item label="联系电话" name="phone">
          <t-input 
            v-model="formData.phone" 
            placeholder="请输入联系电话"
          />
        </t-form-item>
        
        <t-form-item label="邮箱" name="email">
          <t-input 
            v-model="formData.email" 
            placeholder="请输入邮箱地址"
          />
        </t-form-item>
      </t-form>
    </template>
  </t-dialog>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { addDept, updateDept } from '@/api/dept';
import { SysDept, SysDeptTree } from '@/api/model/deptModel';

// 定义 props
interface Props {
  visible: boolean;
  isEdit: boolean;
  deptData?: SysDept;
  parentDept?: SysDept; // 新增时指定的父部门
  deptTreeData: SysDeptTree[];
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  isEdit: false,
  deptData: undefined,
  parentDept: undefined,
  deptTreeData: () => []
});

// 定义 emits
const emit = defineEmits<{
  'update:visible': [value: boolean];
  'success': [];
}>();

// 响应式数据
const formLoading = ref(false);
const form = ref();

const formData = ref({
  id: '',
  parentId: '0',
  name: '',
  sort: 0,
  leader: '',
  phone: '',
  email: '',
  status: '1',
});

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => emit('update:visible', value)
});

const dialogTitle = computed(() => {
  return props.isEdit ? '编辑部门' : '新增部门';
});

const isEditMode = computed(() => props.isEdit);

// 是否可以更改父部门（编辑时通常不允许更改父部门，避免层级混乱）
const canChangeParent = computed(() => {
  // 编辑模式下，如果部门有子部门，则不允许更改父部门
  return !props.isEdit || !hasChildren(props.deptData?.id);
});

// 树形选择器属性配置
const treeProps = {
  keys: { 
    value: 'id', 
    label: 'name', 
    children: 'children' 
  },
  checkStrictly: true,
};

// 上级部门选项（排除自己和子部门）
const parentDeptOptions = computed(() => {
  const options = [
    {
      id: '0',
      name: '无上级部门',
      children: [] as SysDeptTree[]
    }
  ];

  // 递归构建部门选项，排除当前编辑的部门及其子部门
  const buildOptions = (depts: SysDeptTree[], excludeId?: string): SysDeptTree[] => {
    return depts
      .filter(dept => dept.id !== excludeId)
      .map(dept => ({
        ...dept,
        children: dept.children ? buildOptions(dept.children, excludeId) : []
      }))
      .filter(dept => !isDescendant(dept.id!, excludeId));
  };

  if (props.deptTreeData.length > 0) {
    const filteredDepts = buildOptions(props.deptTreeData, props.deptData?.id);
    options[0].children = filteredDepts as any;
  }

  return options;
});

// 检查是否有子部门
const hasChildren = (deptId?: string): boolean => {
  if (!deptId) return false;
  
  const findDept = (depts: SysDeptTree[], id: string): SysDeptTree | null => {
    for (const dept of depts) {
      if (dept.id === id) return dept;
      if (dept.children) {
        const found = findDept(dept.children, id);
        if (found) return found;
      }
    }
    return null;
  };

  const dept = findDept(props.deptTreeData, deptId);
  return dept?.children && dept.children.length > 0 || false;
};

// 检查是否是子部门
const isDescendant = (deptId: string, ancestorId?: string): boolean => {
  if (!ancestorId || deptId === ancestorId) return false;
  
  const findDescendants = (depts: SysDeptTree[], parentId: string): string[] => {
    const result: string[] = [];
    depts.forEach(dept => {
      if (dept.parentId === parentId) {
        result.push(dept.id!);
        if (dept.children) {
          result.push(...findDescendants(dept.children, dept.id!));
        }
      }
    });
    return result;
  };

  const descendants = findDescendants(props.deptTreeData, ancestorId);
  return descendants.includes(deptId);
};

// 表单验证规则
const rules = computed(() => ({
  name: [
    { required: true, message: '请输入部门名称' },
    { min: 2, max: 50, message: '部门名称长度应在2-50字符之间' }
  ],
  sort: [
    { required: true, message: '请输入排序' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' as const }
  ],
  email: [
    { pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, message: '请输入正确的邮箱地址', trigger: 'blur' as const }
  ],
}));

// 初始化表单数据
const initFormData = () => {
  formData.value = {
    id: '',
    parentId: props.parentDept?.id || '0',
    name: '',
    sort: 0,
    leader: '',
    phone: '',
    email: '',
    status: '1',
  };
};

// 加载部门详情数据
const loadDeptDetail = () => {
  if (props.deptData) {
    formData.value = { 
      id: props.deptData.id || '',
      parentId: props.deptData.parentId || '0',
      name: props.deptData.name || '',
      sort: props.deptData.sort || 0,
      leader: props.deptData.leader || '',
      phone: props.deptData.phone || '',
      email: props.deptData.email || '',
      status: props.deptData.status || '1',
    };
  }
};

// 监听弹窗显示状态
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    initFormData();
    
    if (props.isEdit && props.deptData) {
      // 编辑模式，使用父组件传递的部门数据
      loadDeptDetail();
    } else if (!props.isEdit && props.parentDept) {
      // 新增模式，设置父部门
      formData.value.parentId = props.parentDept.id!;
    }
  }
});

// 确认按钮处理
const onConfirm = () => {
  form.value.submit();
};

// 取消按钮处理
const onCancel = () => {
  emit('update:visible', false);
};

// 表单提交处理
const onSubmit = ({ validateResult, firstError }: any) => {
  if (validateResult === true) {
    formLoading.value = true;
    const data = { ...formData.value };

    // 如果parentId为'0'，则设为空字符串或null
    if (data.parentId === '0') {
      data.parentId = '';
    }

    const promise = props.isEdit ? updateDept(data) : addDept(data);

    promise.then(() => {
      MessagePlugin.success(props.isEdit ? '更新成功' : '添加成功');
      emit('update:visible', false);
      emit('success');
    }).catch((error) => {
      console.error('操作失败:', error);
      MessagePlugin.error(error.message || (props.isEdit ? '更新失败' : '添加失败'));
    }).finally(() => {
      formLoading.value = false;
    });
  } else {
    console.log('Validation errors: ', validateResult);
    MessagePlugin.warning(firstError);
  }
};
</script>

<style lang="less" scoped>
// 弹窗内容样式优化
:deep(.t-dialog__body) {
  max-height: 400px;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 16px 24px;
}

:deep(.t-dialog) {
  .t-form {
    overflow: visible;
    width: 100%;
    box-sizing: border-box;
  }
  
  // 确保表单项不会超出宽度
  .t-form-item {
    margin-bottom: 16px;
  }
  
  // 输入框宽度限制
  .t-input,
  .t-input-number,
  .t-tree-select,
  .t-switch {
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
  }
}
</style>