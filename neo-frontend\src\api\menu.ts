import type { RouteItem } from '@/api/model/permissionModel';
import { request } from '@/utils/request';

const Api = {
  GetMenu: '/system/sysMenu/getMenu',
  MenuList: '/system/sysMenu/list',
  MenuListTree: '/system/sysMenu/listTree',
  MenuAdd: '/system/sysMenu/add',
  MenuUpdate: '/system/sysMenu/update',
  MenuDelete: '/system/sysMenu',
  MenuPage: '/system/sysMenu/page',
  MenuGet: '/system/sysMenu',
};

export function getMenu() {
  return request.post<SysMenu[]>({
    url: Api.GetMenu,
  });
}

// 菜单实体类型定义
export interface SysMenu {
  id?: string;
  icon?: string;
  parentId?: string;
  name: string;
  sort?: number;
  path?: string;
  component?: string;
  menuType?: string;
  visible?: string;
  permissionCode?: string;
  status?: string;
  remark?: string;
  createTime?: string;
  updateTime?: string;
  children?: SysMenu[];
}

// 分页查询参数类型
export interface PageParams {
  current: number;
  pageSize: number;
  [key: string]: any;
}

// 分页返回结果类型
export interface PageResult {
  records: SysMenu[];
  total: number;
  current: number;
  pageSize: number;
}

// 查询菜单列表
export function getMenuList(params?: Partial<SysMenu>) {
  return request.post<{ list: SysMenu[] }>({
    url: Api.MenuList,
    data: params,
  });
}


// 查询菜单树形列表
export function getMenuTree(params?: Partial<SysMenu>) {
  return request.post<SysMenu[]>({
    url: Api.MenuListTree,
    data: params,
  });
}

// 添加菜单
export function addMenu(data: SysMenu) {
  return request.post({
    url: Api.MenuAdd,
    data,
  });
}

// 更新菜单
export function updateMenu(data: SysMenu) {
  return request.put({
    url: Api.MenuUpdate,
    data,
  });
}

// 删除菜单
export function deleteMenu(ids: string[]) {
  return request.delete({
    url: Api.MenuDelete,
    data: { ids },
  });
}

// 分页查询菜单
export function getMenuPage(params: PageParams) {
  return request.get<PageResult>({
    url: Api.MenuPage,
    params,
  });
}

// 根据ID获取菜单详情
export function getMenuById(id: string) {
  return request.get<SysMenu>({
    url: `${Api.MenuGet}/${id}`,
  });
}