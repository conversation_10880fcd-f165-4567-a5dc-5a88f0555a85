package com.neo.module.utils;

import cn.hutool.core.util.IdUtil;
import com.neo.utils.DateUtils;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;

public class FileUtils {

    /**
     * 获取文件扩展名
     */
    public static String getExtension(String filename) {
        if (filename == null) {
            return null;
        }
        int index = filename.lastIndexOf(".");
        if (index == -1) {
            return "";
        }
        return filename.substring(index + 1);
    }

    /**
     * 获取文件MD5值
     */
    public static String getMd5(InputStream inputStream) throws IOException {
        return DigestUtils.md5Hex(inputStream);
    }

    /**
     * 上传文件
     */
    public static String upload(String path, String originalFilename, InputStream inputStream) throws IOException {
        String extension = getExtension(originalFilename);
        String fileName = IdUtil.fastUUID() + "." + extension;
        String datePath = DateUtils.datePath();

        File dir = new File(path + "/" + datePath);
        if (!dir.exists()) {
            dir.mkdirs();
        }

        File dest = new File(dir, fileName);
        try (FileOutputStream fos = new FileOutputStream(dest)) {
            byte[] buffer = new byte[1024];
            int length;
            while ((length = inputStream.read(buffer)) > 0) {
                fos.write(buffer, 0, length);
            }
        }

        return datePath + "/" + fileName;
    }

} 