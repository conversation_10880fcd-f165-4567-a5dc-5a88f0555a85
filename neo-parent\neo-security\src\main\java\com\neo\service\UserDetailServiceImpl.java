package com.neo.service;

import com.neo.module.dto.SysUserDTO;
import com.neo.module.rpc.SysUserRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class UserDetailServiceImpl implements UserDetailsService {

    @DubboReference
    private SysUserRpcService sysUserRpcService;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        SysUserDTO sysUserDTO = sysUserRpcService.getUserByUsername(username);
        if (sysUserDTO == null){
            throw new UsernameNotFoundException("用户不存在");
        }
        return new SysUserDetail(sysUserDTO);
    }

}
