package com.neo.module.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.neo.aspect.annontation.PrePermissions;
import com.neo.model.Result;
import com.neo.common.QueryGenerator;
import com.neo.module.entity.SysDept;
import com.neo.module.service.SysDeptService;
import com.neo.module.entity.vo.SysDeptTree;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 系统部门
 *
 * <AUTHOR>
 * @since 2023-04-21
 */
@RestController
@RequestMapping("/sysDept")
public class SysDeptController {
    @Resource
    private SysDeptService sysDeptService;

    /**
     * 分页查询
     *
     * @param params
     * @return
     */
    @PostMapping("/page")
    public Result<?> page(@RequestBody Map<String, String> params) {
        QueryWrapper<SysDept> queryWrapper = QueryGenerator.initQueryWrapper(SysDept.class, params);
        return Result.ok(sysDeptService.page(params, queryWrapper));
    }

    /**
     * 查询
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public Result<?> get(@PathVariable String id) {
        SysDept sysDept = sysDeptService.getById(id);
        return Result.ok(sysDept);
    }

    /**
     * 列表
     *
     * @param sysDept
     * @return
     */
    @PostMapping("/list")
    public Result<?> list(@RequestBody SysDept sysDept) {
        List<SysDept> list = sysDeptService.list(sysDept);
        return Result.ok(list);
    }

    /**
     * 树形列表
     *
     * @param sysDept
     * @return
     */
    @PostMapping("/listTree")
    public Result<?> listTree(@RequestBody SysDept sysDept) {
        List<SysDeptTree> list = sysDeptService.listTree(sysDept);
        return Result.ok(list);
    }

    /**
     * 新增
     *
     * @param sysDept
     * @return
     */
    @PrePermissions("system:sysDept:add")
    @PostMapping("/add")
    public Result<?> add(@RequestBody SysDept sysDept) {
        sysDeptService.saveDept(sysDept);
        return Result.ok();
    }

    /**
     * 删除
     *
     * @param ids
     * @return
     */
    @PrePermissions("system:sysDept:delete")
    @DeleteMapping
    public Result<?> delete(@RequestBody Set<String> ids) {
        sysDeptService.removeByIds(ids);
        return Result.ok();
    }

    /**
     * 更新
     *
     * @param sysDept
     * @return
     */
    @PrePermissions("system:sysDept:update")
    @PutMapping("/update")
    public Result<?> update(@RequestBody SysDept sysDept) {
        sysDeptService.updateDept(sysDept);
        return Result.ok();
    }
}

