package com.neo.module.service.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.neo.exception.BusinessException;
import com.neo.module.config.properties.FileConfigProperties;
import com.neo.module.core.OssS3Client;
import com.neo.module.entity.SysAttachment;
import com.neo.module.entity.vo.FileVO;
import com.neo.module.service.SysAttachmentService;
import com.neo.module.service.StorageService;
import com.neo.module.utils.FileUtils;
import com.neo.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.io.IOUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.UUID;

@Slf4j
@Service("ossStorageService")
public class OssStorageServiceImpl implements StorageService {

    @Resource
    private FileConfigProperties properties;

    @Resource
    private SysAttachmentService sysAttachmentService;

    private OssS3Client ossClient;

    @PostConstruct
    public void init() {
        FileConfigProperties.Oss oss = properties.getOss();
        ossClient = new OssS3Client(oss.getEndpoint(), oss.getAccessKey(),
                oss.getSecretKey(), oss.getBucket());
    }

    @Override
    public FileVO upload(MultipartFile file) {
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
            String id = IdWorker.getIdStr();
            String originalFilename = file.getOriginalFilename();
            String suffix = originalFilename.substring(originalFilename.lastIndexOf("."));
            String datePath = DateUtils.datePath();
            String key = datePath + "/" +UUID.randomUUID() + suffix;
            String url = ossClient.uploadFile(key, inputStream, file.getSize());

            FileVO fileVO = new FileVO();
            fileVO.setId(id);
            fileVO.setFileName(originalFilename);
            fileVO.setUrl(url);

            SysAttachment attachment = new SysAttachment();
            attachment.setId(id);
            attachment.setFileName(fileVO.getFileName());
            attachment.setFilePath(key);  // 存储OSS的key
            attachment.setFileSize(file.getSize());
            attachment.setFileUrl(fileVO.getUrl());
            attachment.setFileType(FileUtils.getExtension(fileVO.getFileName()));
            attachment.setStorageType("oss");
            attachment.setMd5(DigestUtils.md5Hex(file.getBytes()));
            attachment.setStatus(true);
            sysAttachmentService.save(attachment);

            return fileVO;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException("上传失败");
        } finally {
            if (inputStream != null){
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error(e.getMessage(),e);
                }
            }
        }
    }

    @Override
    public void download(String filePath, HttpServletResponse response) throws Exception {
        if (!ossClient.doesObjectExist(filePath)) {
            throw new BusinessException("文件不存在");
        }

        try (InputStream inputStream = ossClient.downloadFile(filePath)) {
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment;filename=" + filePath);
            IOUtils.copy(inputStream, response.getOutputStream());
            response.flushBuffer();
        }
    }

    @Override
    public boolean delete(String filePath) {
        if (!ossClient.doesObjectExist(filePath)) {
            return false;
        }
        ossClient.deleteFile(filePath);
        return true;
    }

    @Override
    public InputStream getFileStream(String filePath) {
        if (!ossClient.doesObjectExist(filePath)) {
            throw new BusinessException("文件不存在");
        }
        return ossClient.downloadFile(filePath);
    }

}
