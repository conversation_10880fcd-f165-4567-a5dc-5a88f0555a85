package com.neo.module.controller;

import cn.hutool.core.util.ReUtil;
import cn.hutool.crypto.digest.BCrypt;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.neo.aspect.annontation.PrePermissions;
import com.neo.model.Result;
import com.neo.constant.CommonConstant;
import com.neo.common.BaseController;
import com.neo.common.QueryGenerator;
import com.neo.module.dto.SysUserDTO;
import com.neo.module.entity.SysUser;
import com.neo.module.entity.vo.SysUserVO;
import com.neo.module.service.SysUserService;
import com.neo.utils.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Set;

/**
 * 系统用户
 *
 * <AUTHOR>
 * @since 2024-07-08
 */
@RestController
@RequestMapping("/sysUser")
public class SysUserController extends BaseController {
    @Resource
    private SysUserService sysUserService;

    /**
     * 分页查询
     *
     * @param params
     * @return
     */
    @PrePermissions("system:sysUser:list")
    @PostMapping("/page")
    public Result<?> page(@RequestBody Map<String, String> params) {
        QueryWrapper<SysUser> queryWrapper = QueryGenerator.initQueryWrapper(SysUser.class, params);
        queryWrapper.ne("id",CommonConstant.ADMIN_USER_ID);
        return Result.ok(sysUserService.page(params, queryWrapper));
    }

    /**
     * 查询
     *
     * @param id
     * @return
     */
    @PrePermissions("system:sysUser:get")
    @GetMapping("/{id}")
    public Result<?> get(@PathVariable String id) {
        SysUserVO sysUserVO = sysUserService.getUserInfo(id);
        return Result.ok(sysUserVO);
    }

    /**
     * 当前用户信息
     *
     * @return
     */
    @GetMapping
    public Result<?> getCurrentUser() {
        SysUserDTO userDTO = sysUserService.getUserByUsername(getUserName());
        return Result.ok(userDTO);
    }

    /**
     * 新增用户
     *
     * @param sysUserVO
     * @return
     */
    @PrePermissions("system:sysUser:add")
    @PostMapping("/add")
    public Result<?> add(@RequestBody SysUserVO sysUserVO) {
        sysUserService.addUser(sysUserVO);
        return Result.ok();
    }

    /**
     * 删除
     *
     * @param ids
     * @return
     */
    @PrePermissions("system:sysUser:delete")
    @DeleteMapping
    public Result<?> delete(@RequestBody Set<String> ids) {
        for (String id : ids) {
            if (id.equals(CommonConstant.ADMIN_USER_ID)) {
                return Result.fail("不能删除超管");
            }
        }
        sysUserService.removeByIds(ids);
        return Result.ok();
    }

    /**
     * 更新
     *
     * @param sysUserVO
     * @return
     */
    @PrePermissions("system:sysUser:update")
    @PutMapping("/update")
    public Result<?> update(@RequestBody SysUserVO sysUserVO) {
        if (CommonConstant.ADMIN_USER_ID.equals(sysUserVO.getId()) && !getUserId().equals(CommonConstant.ADMIN_USER_ID)) {
            return Result.fail("不能操作超管");
        }
        sysUserService.updateUser(sysUserVO);
        return Result.ok();
    }

    /**
     * 重置密码
     *
     * @param sysUser
     * @return
     */
    @PrePermissions("system:sysUser:resetPassword")
    @PutMapping("/resetPassword")
    public Result<?> resetPassword(@RequestBody SysUser sysUser) {
        if (StringUtils.isBlank(sysUser.getId())) {
            return Result.fail("id不能为空");
        }
        if (StringUtils.isBlank(sysUser.getPassword())) {
            return Result.fail("密码不能为空");
        }
        if (!ReUtil.isMatch("^(?=.*[a-z])(?=.*[A-Z])(?=.*[~!@&%#_])[a-zA-Z0-9~!@&%#_]{8,16}$", sysUser.getPassword())) {
            return Result.fail("密码格式，包含一个大写，一个小写字母，一个数字，一个特殊字符，且长度为8到16位");
        }
        sysUser.setPassword(BCrypt.hashpw(sysUser.getPassword()));
        return Result.ok();
    }

}

