<template>
  <div class="log-management-container">
    <!-- 筛选表单 -->
    <t-card class="filter-card" :bordered="false">
      <t-form :data="searchFormState" :label-width="80" colon @reset="onReset" @submit="onSubmit">
        <t-row>
          <t-col :span="10">
            <t-row :gutter="[24, 24]">
              <t-col :span="4">
                <t-form-item label="用户名" name="username">
                  <t-input
                    v-model="searchFormState.username"
                    class="form-item-content"
                    type="search"
                    placeholder="请输入用户名"
                    clearable
                  />
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="业务模块" name="businessModule">
                  <t-input
                    v-model="searchFormState.businessModule"
                    class="form-item-content"
                    placeholder="请输入业务模块"
                    clearable
                  />
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="操作名称" name="operateName">
                  <t-input
                    v-model="searchFormState.operateName"
                    class="form-item-content"
                    placeholder="请输入操作名称"
                    clearable
                  />
                </t-form-item>
              </t-col>
            </t-row>
          </t-col>

          <t-col :span="2" class="operation-container">
            <t-button theme="primary" type="submit" :style="{ marginLeft: 'var(--td-comp-margin-s)' }">
              查询
            </t-button>
            <t-button type="reset" variant="base" theme="default">
              重置
            </t-button>
          </t-col>
        </t-row>
      </t-form>
    </t-card>

    <!-- 表格容器 -->
    <t-card class="table-card" :bordered="false">
      <t-table
        :data="paginationData.dataSource.value"
        :columns="columns"
        :row-key="rowKey"
        vertical-align="top"
        :hover="true"
        :pagination="paginationData.tableConfig.value"
        :loading="paginationData.loading.value"
        @page-change="(pageInfo: any) => paginationData.handlePageChange(pageInfo, loadLogData, searchFormState)"
      >
        <template #costTime="{ row }">
          {{ row.costTime }}ms
        </template>
        
        <template #createTime="{ row }">
          {{ formatDate(row.createTime) }}
        </template>
        
        <template #op="slotProps">
          <t-space>
            <t-link theme="primary" @click="handleView(slotProps.row)">详情</t-link>
          </t-space>
        </template>
      </t-table>
    </t-card>

    <!-- 日志详情弹窗 -->
    <LogDetailModel 
      v-model:visible="detailVisible" 
      :log-data="currentLogData"
    />
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted } from 'vue';
  import {
    getLogPage,
  } from '@/api/log';
  import { SysLog, LogPageParams, LogListResult } from '@/api/model/logModel';
  import { usePagination, CommonPageParams, CommonPageResult } from '@/hooks';
  import LogDetailModel from './components/LogDetailModel.vue';

  // 定义表格列
  const columns = [
    {
      title: '用户名',
      colKey: 'username',
      width: 120,
    },
    {
      title: '业务模块',
      colKey: 'businessModule',
      width: 120,
    },
    {
      title: '操作名称',
      colKey: 'operateName',
      width: 150,
    },
    {
      title: '请求方法',
      colKey: 'requestMethod',
      width: 100,
    },
    {
      title: '请求路径',
      colKey: 'requestUrl',
      width: 200,
    },
    {
      title: 'IP地址',
      colKey: 'ipAddress',
      width: 120,
    },
    {
      title: '执行时间',
      colKey: 'costTime',
      width: 100,
    },
    {
      title: '创建时间',
      colKey: 'createTime',
      width: 150,
    },
    {
      title: '操作',
      colKey: 'op',
      width: 100,
      fixed: 'right' as const,
    },
  ];

  // 使用通用分页 hook
  const paginationData = usePagination<SysLog>({
    defaultCurrent: 1,
    defaultPageSize: 10,
    paramType: 'default' // 使用 current/size 参数
  });

  // 搜索表单
  const searchFormState = reactive<Omit<LogPageParams, 'current' | 'pageSize'>>({
    username: '',
    businessModule: '',
    operateName: '',
  });

  // 详情弹窗相关
  const detailVisible = ref(false);
  const currentLogData = ref<SysLog>();

  // 数据加载函数
  const loadLogData = async (params: CommonPageParams): Promise<CommonPageResult<SysLog>> => {
    const res = await getLogPage(params as LogPageParams);
    return {
      records: res.records,
      total: res.total,
      current: res.current,
      size: paginationData.pagination.pageSize,
      pages: res.pages
    };
  };

  // 查询
  const searchQuery = () => {
    paginationData.resetToFirstPage(loadLogData, searchFormState);
  };

  // 表单提交
  const onSubmit = () => {
    searchQuery();
  };

  // 表单重置
  const onReset = () => {
    searchFormState.username = '';
    searchFormState.businessModule = '';
    searchFormState.operateName = '';
    searchQuery();
  };

  const rowKey = 'id';

  // 查看详情
  const handleView = (record: SysLog) => {
    currentLogData.value = record;
    detailVisible.value = true;
  };

  // 格式化时间
  const formatDate = (dateStr: string | undefined) => {
    if (!dateStr) return '';
    const date = new Date(dateStr);
    return date.toLocaleString('zh-CN');
  };

  onMounted(() => {
    paginationData.loadData(paginationData.buildPageParams(searchFormState), loadLogData);
  });
</script>

<style lang="less" scoped>
  .log-management-container {
    background-color: var(--td-bg-color-container);
    padding: var(--td-comp-paddingTB-xxl) var(--td-comp-paddingLR-xxl);
    border-radius: var(--td-radius-medium);
    
    .filter-card {
      margin-bottom: var(--td-comp-margin-xxl);
    }
    
    .table-card {
      .table-header {
        margin-bottom: var(--td-comp-margin-xl);
        
        .left-operation-container {
          display: flex;
          align-items: center;
          gap: 10px;
        }
      }
    }
  }

  .form-item-content {
    width: 100%;
  }

  .operation-container {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
</style>