<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.neo.module.mapper.SysMenuMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.neo.module.entity.SysMenu">

        <result column="icon" property="icon" />
        <result column="parent_id" property="parentId" />
        <result column="name" property="name" />
        <result column="sort" property="sort" />
        <result column="path" property="path" />
        <result column="component" property="component" />
        <result column="menu_type" property="menuType" />
        <result column="visible" property="visible" />
        <result column="permission_code" property="permissionCode" />
        <result column="status" property="status" />
        <result column="remark" property="remark" />
        <result column="id" property="id" />
        <result column="is_deleted" property="deleted" />
        <result column="create_time" property="createTime" />
        <result column="create_by" property="createBy" />
        <result column="update_time" property="updateTime" />
        <result column="update_by" property="updateBy" />
    </resultMap>

    <select id="selectPermissionsByUserId" resultType="java.lang.String">
        SELECT DISTINCT m.permission_code
        FROM sys_menu m
        LEFT JOIN sys_role_menu rm ON m.id = rm.menu_id
        LEFT JOIN sys_user_role ur ON rm.role_id = ur.role_id
        WHERE ur.user_id = #{userId}
          AND m.permission_code IS NOT NULL
          AND m.permission_code != ''
          AND m.status = '1'
          AND m.is_deleted = '0'
    </select>
    
    <select id="selectMenusByUserId" resultMap="BaseResultMap">
        SELECT DISTINCT m.*
        FROM sys_menu m
        LEFT JOIN sys_role_menu rm ON m.id = rm.menu_id
        LEFT JOIN sys_user_role ur ON rm.role_id = ur.role_id
        WHERE ur.user_id = #{userId}
          AND m.menu_type in ('M','C')
          AND m.status = '1'
          AND m.is_deleted = '0'
        ORDER BY m.sort ASC
    </select>

</mapper>