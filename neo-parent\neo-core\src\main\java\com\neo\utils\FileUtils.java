package com.neo.utils;

import cn.hutool.core.lang.UUID;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;

public class FileUtils {

    /**
     * 默认大小 50M
     */
    public static final long DEFAULT_MAX_SIZE = 50 * 1024 * 1024L;

    /**
     * 默认的文件名最大长度 100
     */
    public static final int DEFAULT_FILE_NAME_LENGTH = 100;

    /**
     * 上传文件
     * @param baseDir
     * @param file
     * @return
     * @throws IOException
     */
    public static String upload(String baseDir, MultipartFile file) throws IOException {
        assertAllowFile(file);
        String originalFilename = file.getOriginalFilename();
        String fileName = DateUtils.datePath() + "/" + UUID.fastUUID() + "." + getSuffix(originalFilename);
        String filePath = baseDir + File.separator +  fileName;
        File dest = new File(filePath);
        if (!dest.getParentFile().exists()){
            dest.getParentFile().mkdirs();
        }
        file.transferTo(dest);
        return fileName;
    }

    private static void assertAllowFile(MultipartFile file) {
        long size = file.getSize();
        if (size > DEFAULT_MAX_SIZE) {
            throw new RuntimeException("文件大小超出限制");
        }
        String originalFilename = file.getOriginalFilename();
        if (originalFilename.length() > DEFAULT_FILE_NAME_LENGTH){
            throw new RuntimeException("文件名长度超出限制");
        }

    }

    /**
     * 获取文件前缀
     * @param fileName
     * @return
     */
    public static String getPrefix(String fileName){
        if (StringUtils.isBlank(fileName)){
            return "";
        }
        String prefix = fileName;
        int index = prefix.lastIndexOf(".");
        if (index > -1){
            prefix = fileName.substring(0,index);
        }
        return prefix;
    }

    /**
     * 获取文件后缀
     * @param fileName
     * @return
     */
    public static String getSuffix(String fileName){
        if (StringUtils.isBlank(fileName)){
            return "";
        }
        int index = fileName.lastIndexOf(".");
        if (index > -1){
            return fileName.substring(index+1);
        }
        return "";
    }

}
