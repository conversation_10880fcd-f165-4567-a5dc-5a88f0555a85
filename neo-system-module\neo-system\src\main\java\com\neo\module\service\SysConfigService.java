package com.neo.module.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.neo.module.entity.SysConfig;

import java.util.List;
import java.util.Map;

/**
 * 系统配置 服务类
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
public interface SysConfigService extends IService<SysConfig> {

    IPage<SysConfig> page(Map<String, String> params, QueryWrapper<SysConfig> queryWrapper);

    List<SysConfig> list(SysConfig sysConfig);

    SysConfig getConfigByKey(String configKey);

}
