package com.neo.module.dubbo;

import com.neo.module.dto.SysLogDTO;
import com.neo.module.entity.SysLog;
import com.neo.module.rpc.SysLogRpcService;
import com.neo.module.service.SysLogService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;
import java.util.Date;

@DubboService
public class SysLogRpcServiceImpl implements SysLogRpcService {

    @Resource
    private SysLogService sysLogService;

    @Override
    public boolean saveLog(SysLogDTO sysLogDTO) {
        SysLog sysLog = new SysLog();
        BeanUtils.copyProperties(sysLogDTO, sysLog);
        sysLog.setCreateTime(new Date());
        return sysLogService.save(sysLog);
    }

}
