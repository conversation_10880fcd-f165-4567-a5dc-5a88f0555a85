# 通用分页 Hook 使用指南

## 概述

`usePagination` 是一个通用的分页处理 Hook，专门解决前端 pages/system 目录下各个管理页面的分页参数不统一问题。

## 问题解决

**原问题**: 不同页面使用不同的分页参数：
- 用户管理、岗位管理：使用 `current`/`size`
- 角色管理、字典管理、日志管理：使用 `page`/`pageSize`

**解决方案**: 提供统一的分页接口，现在所有页面都统一使用 `page`/`pageSize` 参数格式。

## 基本用法

### 1. 导入依赖

```typescript
import { usePagination, CommonPageParams, CommonPageResult } from '@/hooks';
```

### 2. 初始化分页

```typescript
// 现在所有页面都统一使用 page/pageSize 参数
const paginationData = usePagination<DataType>({
  defaultCurrent: 1,
  defaultPageSize: 10,
  paramType: 'legacy' // 使用 page/pageSize（现在是默认值）
});
```
```

### 3. 创建数据加载函数

```typescript
const loadData = async (params: CommonPageParams): Promise<CommonPageResult<DataType>> => {
  const res = await getDataPage(params as YourPageParams);
  return {
    records: res.records,
    total: res.total,
    current: res.current,
    size: res.size,
  };
};
```

### 4. 更新模板

```vue
<template>
  <t-table
    :data="paginationData.dataSource.value"
    :columns="columns"
    :row-key="rowKey"
    :pagination="paginationData.tableConfig.value"
    :loading="paginationData.loading.value"
    @page-change="(pageInfo: any) => paginationData.handlePageChange(pageInfo, loadData, searchParams)"
  >
  </t-table>
</template>
```

### 5. 更新搜索表单

```typescript
// 搜索表单（去除分页参数）
const searchFormState = reactive<Omit<PageParams, 'page' | 'pageSize'>>({
  name: '',
  status: '',
  // ... 其他搜索条件
});

// 查询方法
const searchQuery = () => {
  paginationData.resetToFirstPage(loadData, searchFormState);
};
```

## 完整示例

以下是岗位管理页面的重构示例：

### 重构前（原始代码）

```typescript
// 表格相关数据
const dataSource = ref<SysPost[]>([]);
const loading = ref(false);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
});

// 搜索表单
const searchFormState = reactive<PostPageParams>({
  current: 1,
  size: 10,
  name: '',
  code: '',
  status: '',
});

// 获取数据
const loadData = async (params: PostPageParams) => {
  loading.value = true;
  try {
    const res: PostListResult = await getPostPage(params);
    dataSource.value = res.records;
    pagination.total = res.total;
    pagination.current = res.current;
  } catch (error) {
    console.error('获取岗位列表失败:', error);
  } finally {
    loading.value = false;
  }
};

// 表格分页变化
const handlePageChange = (curr: any) => {
  pagination.current = curr.current;
  pagination.pageSize = curr.pageSize;
  searchFormState.current = curr.current;
  searchFormState.size = curr.pageSize;
  loadData(searchFormState);
};
```

### 重构后（使用通用 Hook）

```typescript
import { usePagination, CommonPageParams, CommonPageResult } from '@/hooks';

// 使用通用分页 hook
const paginationData = usePagination<SysPost>({
  defaultCurrent: 1,
  defaultPageSize: 10,
  paramType: 'legacy' // 使用 page/pageSize 参数（现在是默认值）
});

// 搜索表单（移除分页参数）
const searchFormState = reactive<Omit<PostPageParams, 'page' | 'pageSize'>>({
  name: '',
  code: '',
  status: '',
});

// 数据加载函数
const loadPostData = async (params: CommonPageParams): Promise<CommonPageResult<SysPost>> => {
  const res = await getPostPage(params as PostPageParams);
  return {
    records: res.records,
    total: res.total,
    current: res.current,
    size: res.size,
  };
};

// 查询
const searchQuery = () => {
  paginationData.resetToFirstPage(loadPostData, searchFormState);
};

// 表单操作成功回调
const onFormSuccess = () => {
  paginationData.refreshData(loadPostData, searchFormState);
};

onMounted(() => {
  paginationData.loadData(paginationData.buildPageParams(searchFormState), loadPostData);
});
```

### 模板更新

```vue
<template>
  <t-table
    :data="paginationData.dataSource.value"
    :columns="columns"
    :row-key="rowKey"
    :pagination="paginationData.tableConfig.value"
    :loading="paginationData.loading.value"
    @page-change="(pageInfo: any) => paginationData.handlePageChange(pageInfo, loadPostData, searchFormState)"
  >
    <!-- 表格模板内容 -->
  </t-table>
</template>
```

## API 参考

### PaginationOptions

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| defaultCurrent | number | 1 | 初始页码 |
| defaultPageSize | number | 10 | 初始每页条数 |
| paramType | 'default' \| 'legacy' | 'legacy' | 参数类型，default 使用 current/size，legacy 使用 page/pageSize（现在默认使用 page/pageSize） |

### 返回值

| 属性/方法 | 类型 | 说明 |
|-----------|------|------|
| dataSource | Ref\<T[]\> | 表格数据 |
| loading | Ref\<boolean\> | 加载状态 |
| pagination | Reactive\<object\> | 分页状态 |
| tableConfig | ComputedRef | TDesign 表格分页配置 |
| buildPageParams | (params) => CommonPageParams | 构建分页参数 |
| handlePageChange | (pageInfo, loadFn, searchParams) => void | 处理分页变化 |
| loadData | (params, loadFn) => void | 加载数据 |
| resetToFirstPage | (loadFn, searchParams) => void | 重置到第一页 |
| refreshData | (loadFn, searchParams) => void | 刷新当前页 |

## 已重构页面

- ✅ 岗位管理 (`/src/pages/system/post/index.vue`)
- ✅ 角色管理 (`/src/pages/system/role/index.vue`)
- ✅ 字典管理 (`/src/pages/system/dict/index.vue`)
- ✅ 日志管理 (`/src/pages/system/log/index.vue`)
- ✅ 用户管理 (`/src/pages/system/user/index.vue`) - 部分重构

## 注意事项

1. **参数类型选择**：根据后端接口选择正确的 `paramType`
2. **TypeScript 类型**：确保数据加载函数返回正确的 `CommonPageResult` 格式
3. **搜索参数**：从搜索表单中移除分页相关参数，避免重复定义
4. **事件处理**：使用 Hook 提供的方法处理分页变化，无需手动管理分页状态

## 迁移清单

对现有页面进行重构时，请按以下步骤操作：

1. [ ] 添加 usePagination Hook 导入
2. [ ] 替换原有的 dataSource、loading、pagination 定义
3. [ ] 创建符合规范的数据加载函数
4. [ ] 更新搜索表单，移除分页参数
5. [ ] 更新模板中的表格属性和事件处理
6. [ ] 更新查询和重置方法
7. [ ] 测试分页功能是否正常

通过使用这个通用分页 Hook，可以大大减少代码重复，提高维护效率，并确保所有页面的分页功能保持一致。