import { request } from '@/utils/request';
import { SysAttachment, AttachmentListResult, AttachmentPageParams } from './model/attachmentModel';

enum Api {
  AttachmentList = '/resource/sysAttachment/page',
  AttachmentInfo = '/resource/sysAttachment',
  DeleteAttachment = '/resource/sysAttachment',
  UploadFile = '/resource/sysAttachment/upload',
  DownloadFile = '/resource/sysAttachment/download',
}

/**
 * 分页查询附件列表
 * @param params
 */
export const getAttachmentPage = (params: AttachmentPageParams) => {
  return request.post<AttachmentListResult>({
    url: Api.AttachmentList,
    data: params,
  });
};

/**
 * 获取附件详情
 * @param id
 */
export const getAttachmentInfo = (id: string) => {
  return request.get<SysAttachment>({
    url: `${Api.AttachmentInfo}/${id}`,
  });
};

/**
 * 删除附件
 * @param ids
 */
export const deleteAttachment = (ids: string[]) => {
  return request.delete({
    url: Api.DeleteAttachment,
    data: ids,
  });
};

/**
 * 上传文件
 * @param file
 */
export const uploadFile = (file: File) => {
  const formData = new FormData();
  formData.append('file', file);
  
  return request.post({
    url: Api.UploadFile,
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

/**
 * 下载文件
 * @param id
 */
export const downloadFile = (id: string) => {
  return request.get({
    url: `${Api.DownloadFile}/${id}`,
    responseType: 'blob',
  });
};
