<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.neo</groupId>
        <artifactId>neo-parent</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>neo-database</artifactId>

    <dependencies>
        <!-- 数据库驱动 -->
        <!--mysql-->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>${mysql-connector-java.version}</version>
            <scope>runtime</scope>
        </dependency>
        <!--  sqlserver-->
        <!--        <dependency>-->
        <!--            <groupId>com.microsoft.sqlserver</groupId>-->
        <!--            <artifactId>sqljdbc4</artifactId>-->
        <!--            <scope>runtime</scope>-->
        <!--        </dependency>-->
        <!-- oracle驱动 -->
        <!--        <dependency>-->
        <!--            <groupId>com.oracle</groupId>-->
        <!--            <artifactId>ojdbc6</artifactId>-->
        <!--            <scope>runtime</scope>-->
        <!--        </dependency>-->
        <!-- postgresql驱动 -->
        <!--        <dependency>-->
        <!--            <groupId>org.postgresql</groupId>-->
        <!--            <artifactId>postgresql</artifactId>-->
        <!--            <version>${postgresql.version}</version>-->
        <!--            <scope>runtime</scope>-->
        <!--        </dependency>-->

        <!-- mybatis-plus -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>

        <!-- druid -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
        </dependency>

        <!-- 动态数据源 -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>neo-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>neo-security</artifactId>
        </dependency>
    </dependencies>

</project>