export interface SysLog {
  id?: string;
  userId?: string;
  username?: string;
  businessId?: string;
  businessModule?: string;
  operateType?: string;
  operateName?: string;
  methodName?: string;
  requestUrl?: string;
  requestParam?: string;
  requestMethod?: string;
  resultJson?: string;
  ipAddress?: string;
  costTime?: number;
  createTime?: string;
}

export interface LogPageParams extends Partial<SysLog> {
  current: number;
  pageSize: number;
}

export interface LogListResult {
  records: SysLog[];
  total: number;
  current: number;
  pages: number;
}