/**
 * ES手机信息数据模型
 */

/**
 * ES手机信息实体
 */
export interface EsPhoneInfo {
  id?: string;
  brand?: string;
  model?: string;
  cpu?: string;
  memorySize?: number;
  storageSize?: number;
  screenSize?: number;
  screenResolution?: string;
  cameraMain?: number;
  cameraFront?: number;
  batteryCapacity?: number;
  osType?: string;
  osVersion?: string;
  price?: number;
  releaseDate?: string;
  networkType?: string;
  weight?: number;
  color?: string;
  is5gSupported?: string;
}

/**
 * ES手机信息分页查询参数
 */
export interface EsPhoneInfoPageParams {
  current: number;
  pageSize: number;
  brand?: string;
  model?: string;
  cpu?: string;
  memorySize?: number;
  storageSize?: number;
  screenSize?: number;
  screenResolution?: string;
  cameraMain?: number;
  cameraFront?: number;
  batteryCapacity?: number;
  osType?: string;
  osVersion?: string;
  price?: number;
  releaseDate?: string;
  networkType?: string;
  weight?: number;
  color?: string;
  is5gSupported?: string;
  // ES特有的搜索参数
  keyword?: string; // 全文搜索关键词
  priceMin?: number; // 价格范围最小值
  priceMax?: number; // 价格范围最大值
  memorySizeMin?: number; // 内存范围最小值
  memorySizeMax?: number; // 内存范围最大值
  storageSizeMin?: number; // 存储范围最小值
  storageSizeMax?: number; // 存储范围最大值
}

/**
 * ES手机信息列表结果
 */
export interface EsPhoneInfoListResult {
  records: EsPhoneInfo[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

/**
 * ES手机信息分页结果
 */
export type EsPhoneInfoPageResult = EsPhoneInfoListResult;

/**
 * ES索引操作结果
 */
export interface EsIndexResult {
  success: boolean;
  message: string;
}
