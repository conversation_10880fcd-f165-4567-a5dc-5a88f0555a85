<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.neo.module.mapper.SysDictItemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.neo.module.entity.SysDictItem">

        <result column="dict_id" property="dictId" />
        <result column="dict_code" property="dictCode" />
        <result column="item_text" property="itemText" />
        <result column="item_value" property="itemValue" />
        <result column="description" property="description" />
        <result column="sort" property="sort" />
        <result column="status" property="status" />
        <result column="id" property="id" />
        <result column="is_deleted" property="deleted" />
        <result column="create_time" property="createTime" />
        <result column="create_by" property="createBy" />
        <result column="update_time" property="updateTime" />
        <result column="update_by" property="updateBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,is_deleted,create_time,create_by,update_time,update_by,dict_id,dict_code, item_text, item_value, description, sort, status
    </sql>

    <select id="selectByDictTable" resultType="java.lang.String">
        select item_text
        from sys_dict_item
        where dict_code = #{dictCode} and item_value = #{value}
    </select>

    <select id="selectByOtherTable" resultType="java.lang.String">
        select ${dictText}
        from ${dictTable}
        where ${dictCode} = #{value}
    </select>

</mapper>
