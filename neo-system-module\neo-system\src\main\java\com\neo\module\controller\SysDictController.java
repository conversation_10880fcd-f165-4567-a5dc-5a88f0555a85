package com.neo.module.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.neo.aspect.annontation.PrePermissions;
import com.neo.model.Result;
import com.neo.constant.CacheConstants;
import com.neo.common.QueryGenerator;
import com.neo.module.entity.SysDict;
import com.neo.module.service.SysDictService;
import com.neo.utils.RedisUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 数据字典
 *
 * <AUTHOR>
 * @since 2023-05-25
 */
@RestController
@RequestMapping("/sysDict")
public class SysDictController {
    @Resource
    private SysDictService sysDictService;
    @Resource
    private RedisUtils redisUtils;

    /**
     * 分页查询
     *
     * @param params
     * @return
     */
    @PrePermissions("system:sysDict:list")
    @PostMapping("/page")
    public Result<?> page(@RequestBody Map<String, String> params) {
        QueryWrapper<SysDict> queryWrapper = QueryGenerator.initQueryWrapper(SysDict.class, params);
        return Result.ok(sysDictService.page(params, queryWrapper));
    }

    /**
     * 查询
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public Result<?> get(@PathVariable String id) {
        SysDict sysDict = sysDictService.getById(id);
        return Result.ok(sysDict);
    }

    /**
     * 列表
     *
     * @param sysDict
     * @return
     */
    @PostMapping("/list")
    public Result<?> list(@RequestBody SysDict sysDict) {
        List<SysDict> list = sysDictService.list(sysDict);
        return Result.ok(list);
    }

    /**
     * 新增
     *
     * @param sysDict
     * @return
     */
    @PrePermissions("system:sysDict:add")
    @PostMapping("/add")
    public Result<?> add(@RequestBody SysDict sysDict) {
        sysDictService.save(sysDict);
        return Result.ok();
    }

    /**
     * 删除
     *
     * @param ids
     * @return
     */
    @PrePermissions("system:sysDict:delete")
    @DeleteMapping
    public Result<?> delete(@RequestBody Set<String> ids) {
        sysDictService.removeByIds(ids);
        return Result.ok();
    }

    /**
     * 更新
     *
     * @param sysDict
     * @return
     */
    @PrePermissions("system:sysDict:update")
    @PutMapping("/update")
    public Result<?> update(@RequestBody SysDict sysDict) {
        sysDictService.updateById(sysDict);
        return Result.ok();
    }

    /**
     * 清除缓存
     *
     * @param sysDict
     * @return
     */
    @PrePermissions("system:sysDict:cleanCache")
    @PostMapping("/cleanCache")
    private Result<?> cleanCache(@RequestBody SysDict sysDict) {
        String key = CacheConstants.SYS_DICT_KEY + ":" + sysDict.getDictCode() + ":" + "*";
        redisUtils.delete(key);
        return Result.ok();
    }
}

