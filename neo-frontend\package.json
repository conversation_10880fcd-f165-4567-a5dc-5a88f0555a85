{"name": "neo-frontend", "version": "0.13.1", "type": "module", "scripts": {"dev:mock": "vite --open --mode mock", "dev": "vite --open --mode development", "dev:linux": "vite --mode development", "build:test": "vite build --mode test", "build": "vue-tsc --noEmit && vite build --mode release", "build:type": "vue-tsc --noEmit", "build:site": "vue-tsc --noEmit && vite build --mode site", "preview": "vite preview", "lint": "eslint --ext .vue,.js,.jsx,.ts,.tsx ./ --max-warnings 0", "lint:fix": "eslint --ext .vue,.js,jsx,.ts,.tsx ./ --max-warnings 0 --fix", "stylelint": "stylelint src/**/*.{html,vue,css,sass,less}", "stylelint:fix": "stylelint --fix src/**/*.{html,vue,css,sass,less}", "prepare": "node -e \"if(require('fs').existsSync('.git')){process.exit(1)}\" || is-ci || husky install", "site:preview": "npm run build && cp -r dist _site", "test": "echo \"no test specified,work in process\"", "test:coverage": "echo \"no test:coverage specified,work in process\""}, "dependencies": {"@vueuse/core": "^12.3.0", "axios": "^1.8.4", "dayjs": "^1.11.13", "echarts": "5.4.3", "lodash": "^4.17.21", "nprogress": "^0.2.0", "pinia": "2.1.7", "pinia-plugin-persistedstate": "^3.2.0", "qrcode.vue": "^3.4.1", "qs": "^6.13.1", "tdesign-icons-vue-next": "^0.3.6", "tdesign-vue-next": "^1.13.2", "tvision-color": "^1.6.0", "vue": "^3.5.13", "vue-i18n": "^9.9.1", "vue-router": "~4.3.0"}, "devDependencies": {"@antfu/eslint-config": "^4.10.1", "@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@types/echarts": "^4.9.21", "@types/lodash": "^4.17.6", "@types/mockjs": "^1.0.10", "@types/nprogress": "^0.2.3", "@types/qs": "^6.9.17", "@vitejs/plugin-vue": "^5.0.4", "@vitejs/plugin-vue-jsx": "^4.1.1", "@vue/compiler-sfc": "~3.3.8", "commitizen": "^4.3.0", "cz-conventional-changelog": "^3.3.0", "eslint": "^9.22.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-vue-scoped-css": "^2.9.0", "globals": "^16.0.0", "husky": "^9.1.7", "less": "^4.2.0", "lint-staged": "^15.2.2", "mockjs": "^1.1.0", "postcss-html": "^1.6.0", "postcss-less": "^6.0.0", "prettier": "^3.5.3", "rspack-resolver": "^1.1.1", "stylelint": "~16.16.0", "stylelint-config-standard": "^37.0.0", "stylelint-order": "~6.0.4", "typescript": "~5.4.3", "typescript-eslint": "^8.26.1", "vite": "^6.2.2", "vite-plugin-mock": "^3.0.1", "vite-svg-loader": "^5.1.0", "vue-tsc": "^2.2.8"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"*.{js,jsx,vue,ts,tsx}": ["prettier --write", "npm run lint:fix"], "*.{html,vue,css,sass,less}": ["npm run stylelint:fix"]}, "engines": {"node": ">=18.18.0"}, "description": "NEO后台管理系统"}