package com.neo.module.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.neo.module.entity.PhoneInfo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * 手机信息表 服务类
 *
 * <AUTHOR>
 * @since 2025-08-22
 */
public interface PhoneInfoService extends IService<PhoneInfo> {

    IPage<PhoneInfo> page(Map<String, String> params, QueryWrapper<PhoneInfo> queryWrapper);

    List<PhoneInfo> list(PhoneInfo phoneInfo);

}
