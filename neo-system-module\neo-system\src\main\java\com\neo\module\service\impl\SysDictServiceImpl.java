package com.neo.module.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.common.QueryGenerator;
import com.neo.module.entity.SysDict;
import com.neo.module.mapper.SysDictMapper;
import com.neo.module.service.SysDictService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 数据字典 服务实现类
 *
 * <AUTHOR>
 * @since 2023-05-25
 */
@Slf4j
@Service
public class SysDictServiceImpl extends ServiceImpl<SysDictMapper, SysDict> implements SysDictService {

    @Resource
    private SysDictMapper sysDictMapper;

    @Override
    public IPage<SysDict> page(Map<String, String> params, QueryWrapper<SysDict> queryWrapper) {
        String value;
        queryWrapper
                .eq(StringUtils.isNotBlank((value = params.get("dictName"))), "dict_name", value)
                .eq(StringUtils.isNotBlank((value = params.get("dictCode"))), "dict_code", value)
                .eq(StringUtils.isNotBlank((value = params.get("status"))), "status", value)
        ;
        IPage<SysDict> page = sysDictMapper.selectPage(QueryGenerator.initPage(params), queryWrapper);
        return page;
    }

    @Override
    public List<SysDict> list(SysDict sysDict) {
        QueryWrapper<SysDict> queryWrapper = new QueryWrapper<>(sysDict);
        List<SysDict> sysDicts = sysDictMapper.selectList(queryWrapper);
        return sysDicts;
    }

}
