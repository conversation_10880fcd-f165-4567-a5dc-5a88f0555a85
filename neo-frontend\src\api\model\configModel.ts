export interface SysConfig {
  id?: string;
  configName?: string;
  configKey?: string;
  configValue?: string;
  configType?: string;
  remark?: string;
  createTime?: string;
  updateTime?: string;
}

export interface ConfigPageParams extends SysConfig {
  page: number;
  pageSize: number;
}

export interface ConfigListResult {
  records: SysConfig[];
  total: number;
  current: number;
  pages: number;
}