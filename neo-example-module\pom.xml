<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.neo</groupId>
        <artifactId>neo-parent</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>neo-example-module</artifactId>
    <version>${revision}</version>

    <properties>
        <revision>1.0.0</revision>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>neo-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>neo-cloud</artifactId>
        </dependency>

        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>neo-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>neo-database</artifactId>
        </dependency>

        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>neo-security</artifactId>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.neo</groupId>-->
<!--            <artifactId>neo-doc</artifactId>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>neo-elasticsearch</artifactId>
        </dependency>


<!--        <dependency>-->
<!--            <groupId>com.neo</groupId>-->
<!--            <artifactId>neo-mq</artifactId>-->
<!--        </dependency>-->

    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.9.0</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>com.github.therapi</groupId>
                            <artifactId>therapi-runtime-javadoc-scribe</artifactId>
                            <version>0.15.0</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.springframework.boot</groupId>
                            <artifactId>spring-boot-configuration-processor</artifactId>
                            <version>${spring-boot.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
            </plugin>
        </plugins>
    </build>

</project>