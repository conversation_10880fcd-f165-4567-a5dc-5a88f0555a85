import { ref, reactive, computed } from 'vue';
import type { TableProps } from 'tdesign-vue-next';

/**
 * 通用分页参数接口
 */
export interface CommonPageParams {
  current?: number;
  pageSize?: number;
  [key: string]: any;
}

/**
 * 通用分页响应接口
 */
export interface CommonPageResult<T = any> {
  records: T[];
  total: number;
  current: number;
  size: number;
  pages?: number;
}

/**
 * 分页配置选项
 */
export interface PaginationOptions {
  /**
   * 初始页码，默认为 1
   */
  defaultCurrent?: number;
  /**
   * 初始每页条数，默认为 10
   */
  defaultPageSize?: number;
  /**
   * 分页参数类型，default 表示使用 current/size，legacy 表示使用 page/pageSize
   */
  paramType?: 'default' | 'legacy';
}

/**
 * 通用分页 Hook
 * @param options 分页配置选项
 * @returns 分页相关的状态和方法
 */
export function usePagination<T = any>(options: PaginationOptions = {}) {
  const {
    defaultCurrent = 1,
    defaultPageSize = 10
  } = options;

  // 数据状态
  const dataSource = ref<T[]>([]);
  const loading = ref(false);
  
  // 分页状态
  const pagination = reactive({
    current: defaultCurrent,
    pageSize: defaultPageSize,
    total: 0,
  });

  /**
   * 构建分页参数
   * @param extraParams 额外参数
   * @returns 包含分页信息的参数对象
   */
  const buildPageParams = (extraParams: Record<string, any> = {}): CommonPageParams => {
    return {
      current: pagination.current,
      pageSize: pagination.pageSize,
      ...extraParams,
    };
  };

  /**
   * 处理分页响应数据
   * @param response API 响应数据
   */
  const handlePageResponse = (response: CommonPageResult<T>) => {
    dataSource.value = response.records || [];
    pagination.total = response.total || 0;
    pagination.current = response.current || pagination.current;
    pagination.pageSize = response.size;
  };

  /**
   * 分页变化处理函数
   * @param pageInfo 分页信息
   * @param loadDataFn 加载数据的函数
   * @param searchParams 搜索参数
   */
  const handlePageChange = async (
    pageInfo: any,
    loadDataFn: (params: CommonPageParams) => Promise<CommonPageResult<T>>,
    searchParams: Record<string, any> = {}
  ) => {
    pagination.current = pageInfo.current;
    pagination.pageSize = pageInfo.pageSize;
    
    const params = buildPageParams(searchParams);
    await loadData(params, loadDataFn);
  };

  /**
   * 加载数据
   * @param params 请求参数
   * @param loadDataFn 加载数据的函数
   */
  const loadData = async (
    params: CommonPageParams,
    loadDataFn: (params: CommonPageParams) => Promise<CommonPageResult<T>>
  ) => {
    loading.value = true;
    try {
      const response = await loadDataFn(params);
      handlePageResponse(response);
    } catch (error) {
      console.error('加载数据失败:', error);
      dataSource.value = [];
      pagination.total = 0;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 重置到第一页
   * @param loadDataFn 加载数据的函数
   * @param searchParams 搜索参数
   */
  const resetToFirstPage = async (
    loadDataFn: (params: CommonPageParams) => Promise<CommonPageResult<T>>,
    searchParams: Record<string, any> = {}
  ) => {
    pagination.current = 1;
    const params = buildPageParams(searchParams);
    await loadData(params, loadDataFn);
  };

  /**
   * 刷新当前页数据
   * @param loadDataFn 加载数据的函数
   * @param searchParams 搜索参数
   */
  const refreshData = async (
    loadDataFn: (params: CommonPageParams) => Promise<CommonPageResult<T>>,
    searchParams: Record<string, any> = {}
  ) => {
    const params = buildPageParams(searchParams);
    await loadData(params, loadDataFn);
  };

  // TDesign 表格分页配置
  const tableConfig = computed((): TableProps['pagination'] => ({
    current: pagination.current,
    pageSize: pagination.pageSize,
    total: pagination.total,
    showJumper: true,
    pageSizeOptions: [10, 20, 50, 100],
  }));

  return {
    // 状态
    dataSource,
    loading,
    pagination,
    
    // 方法
    buildPageParams,
    handlePageResponse,
    handlePageChange,
    loadData,
    resetToFirstPage,
    refreshData,
    
    // 配置
    tableConfig,
  };
}