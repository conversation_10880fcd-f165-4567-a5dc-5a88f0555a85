import cn.hutool.jwt.JWTUtil;

import java.util.HashMap;
import java.util.Map;

public class PasswordTest {

    public static void main(String[] args) {
//        BCryptPasswordEncoder bCryptPasswordEncoder = new BCryptPasswordEncoder();
//        System.out.println(bCryptPasswordEncoder.encode("neo@2024"));

        Map<String,Object> map = new HashMap<>();
        map.put("user","1");
        System.out.println(JWTUtil.createToken(map, "abcdefghijklmnopqrstuvwxyz".getBytes()));
    }

}
