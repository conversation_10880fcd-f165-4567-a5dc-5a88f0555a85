package com.neo.module.service;

import com.neo.module.entity.vo.FileVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;

public interface StorageService {

    /**
     * 上传文件
     */
    FileVO upload(MultipartFile file);

    /**
     * 根据文件ID下载文件
     */
    void download(String filePath, HttpServletResponse response) throws Exception;

    /**
     * 根据文件路径获取文件流（仅内部使用）
     */
    InputStream getFileStream(String filePath) throws Exception;

    /**
     * 根据文件路径删除文件（仅内部使用）
     */
    boolean delete(String filePath) throws Exception;
}
