import com.neo.module.core.OssS3Client;

import java.io.File;

public class TestOssS3 {

    public static void main(String[] args) {
        OssS3Client ossS3Client = new OssS3Client("https://test-1604047904955009.oss-cn-guangzhou.oss-accesspoint.aliyuncs.com"
                , "LTAI5t67Pv6VRWESGJPgr4mj"
                , "******************************"
                , "w7s");
        File file = new File("D:\\maven\\apache-maven-3.6.2\\README.txt");
        ossS3Client.uploadFile("README.txt",file);
    }

}
