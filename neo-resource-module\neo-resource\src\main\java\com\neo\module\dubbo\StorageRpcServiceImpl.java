package com.neo.module.dubbo;

import com.neo.module.core.StorageServiceFactory;
import com.neo.module.dto.StorageDTO;
import com.neo.module.entity.vo.FileVO;
import com.neo.module.rpc.StorageRpcService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

@DubboService
public class StorageRpcServiceImpl implements StorageRpcService {

    @Resource
    private StorageServiceFactory storageServiceFactory;

    @Override
    public StorageDTO upload(MultipartFile file) throws Exception {
        FileVO fileVO = storageServiceFactory.getStorageService().upload(file);
        StorageDTO storageDTO = new StorageDTO();
        BeanUtils.copyProperties(fileVO, storageDTO);
        return storageDTO;
    }

}
