<template>
  <t-dialog 
    v-model:visible="dialogVisible" 
    :header="dialogTitle" 
    :width="600" 
    :confirm-btn="{ loading: formLoading }" 
    @confirm="onConfirm" 
    @cancel="onCancel"
  >
    <template v-if="dialogVisible">
      <t-form ref="form" :data="formData" :rules="rules" :label-width="80" @submit="onSubmit">
        <t-form-item label="配置名称" name="configName">
          <t-input 
            v-model="formData.configName" 
            placeholder="请输入配置名称"
          />
        </t-form-item>
        
        <t-form-item label="配置键" name="configKey">
          <t-input 
            v-model="formData.configKey" 
            :disabled="isEdit"
            placeholder="请输入配置键"
          />
        </t-form-item>
        
        <t-form-item label="配置值" name="configValue">
          <!-- 文本类型 -->
          <t-input 
            v-if="formData.configType === 'text'"
            v-model="formData.configValue" 
            placeholder="请输入文本"
          />
          
          <!-- 数字类型 -->
          <t-input-number 
            v-else-if="formData.configType === 'number'"
            v-model="formData.configValue" 
            placeholder="请输入数字"
            :decimal-places="2"
            :allow-input-over-limit="false"
            style="width: 100%;"
          />
          
          <!-- 布尔值类型 -->
          <t-switch 
            v-else-if="formData.configType === 'boolean'"
            :model-value="String(formData.configValue)"
            @change="handleBooleanChange"
            :custom-value="['true', 'false']"
            size="medium"
          />
          
          <!-- JSON类型 -->
          <t-textarea 
            v-else-if="formData.configType === 'json'"
            v-model="formData.configValue" 
            placeholder="请输入有效的JSON字符串"
            :autosize="{ minRows: 3, maxRows: 8 }"
            :maxlength="5000"
          />
          
          <!-- 默认输入框（未选择类型时） -->
          <t-input 
            v-else
            v-model="formData.configValue" 
            placeholder="请先选择配置类型"
            disabled
          />
        </t-form-item>
        
        <t-form-item label="配置类型" name="configType">
          <t-select 
            v-model="formData.configType" 
            placeholder="请选择配置类型"
            clearable
          >
            <t-option value="text" label="文本" />
            <t-option value="number" label="数字" />
            <t-option value="boolean" label="布尔值" />
            <t-option value="json" label="JSON" />
          </t-select>
        </t-form-item>
        
        <t-form-item label="备注" name="remark">
          <t-textarea 
            v-model="formData.remark" 
            placeholder="请输入备注"
            :maxlength="500"
            :autosize="{ minRows: 3, maxRows: 5 }"
          />
        </t-form-item>
      </t-form>
    </template>
  </t-dialog>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { addConfig, updateConfig } from '@/api/config';
import { SysConfig } from '@/api/model/configModel';

// 定义 props
interface Props {
  visible: boolean;
  isEdit: boolean;
  configData?: SysConfig;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  isEdit: false,
  configData: undefined
});

// 定义 emits
const emit = defineEmits<{
  'update:visible': [value: boolean];
  'success': [];
}>();

// 响应式数据
const formLoading = ref(false);
const form = ref();

const formData = ref({
  id: '',
  configName: '',
  configKey: '',
  configValue: '',
  configType: '',
  remark: '',
});

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => emit('update:visible', value)
});

const dialogTitle = computed(() => {
  return props.isEdit ? '编辑系统配置' : '新增系统配置';
});

const rules = computed(() => {
  const baseRules: any = {
    configName: [
      { required: true, message: '请输入配置名称' }
    ],
    configKey: [
      { required: true, message: '请输入配置键' }
    ],
    configValue: [
      { required: true, message: '请输入配置值' }
    ],
    configType: [
      { required: true, message: '请选择配置类型' }
    ],
  };

  // 为JSON类型添加格式验证
  if (formData.value.configType === 'json') {
    baseRules.configValue.push({
      validator: (val: string) => {
        if (!val || val.trim() === '') {
          return false;
        }
        try {
          JSON.parse(val);
          return true;
        } catch (e) {
          return false;
        }
      },
      message: '请输入有效的JSON格式'
    });
  }

  return baseRules;
});

// 初始化表单数据
const initFormData = () => {
  formData.value = {
    id: '',
    configName: '',
    configKey: '',
    configValue: '',
    configType: '',
    remark: '',
  };
};

// 加载配置详情数据
const loadConfigDetail = () => {
  if (props.configData) {
    // 使用父组件传递的配置数据
    formData.value = { 
      id: props.configData.id || '',
      configName: props.configData.configName || '',
      configKey: props.configData.configKey || '',
      configValue: props.configData.configValue || '',
      configType: props.configData.configType || '',
      remark: props.configData.remark || ''
    };
    
    // 如果是布尔类型，确保值是有效的布尔字符串
    if (formData.value.configType === 'boolean') {
      const validBooleanValues = ['true', 'false'];
      if (!validBooleanValues.includes(formData.value.configValue)) {
        formData.value.configValue = 'true'; // 默认值
      }
    }
    
    // 如果是数字类型，确保值是有效的数字字符串
    if (formData.value.configType === 'number') {
      if (!formData.value.configValue || isNaN(Number(formData.value.configValue))) {
        formData.value.configValue = '0'; // 默认值
      }
    }
  }
};

// 处理布尔值变化
const handleBooleanChange = (value: any) => {
  formData.value.configValue = String(value);
};

// 监听配置类型变化，清空配置值
watch(() => formData.value.configType, (newType, oldType) => {
  if (newType !== oldType && oldType !== '') {
    // 类型变化时设置对应的默认值
    if (newType === 'boolean') {
      // 布尔类型默认为true
      formData.value.configValue = 'true';
    } else if (newType === 'number') {
      // 数字类型默认为0
      formData.value.configValue = '0';
    } else {
      // 其他类型清空
      formData.value.configValue = '';
    }
  }
});

// 监听弹窗显示状态
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    initFormData();
    
    if (props.isEdit && props.configData) {
      // 编辑模式，使用父组件传递的配置数据
      loadConfigDetail();
    }
  }
});

// 确认按钮处理
const onConfirm = () => {
  form.value.submit();
};

// 取消按钮处理
const onCancel = () => {
  emit('update:visible', false);
};

// 表单提交处理
const onSubmit = ({ validateResult, firstError }: any) => {
  if (validateResult === true) {
    formLoading.value = true;
    const data = { ...formData.value };
    
    // 确保布尔值以字符串形式保存
    if (data.configType === 'boolean') {
      data.configValue = String(data.configValue);
    }

    const promise = props.isEdit ? updateConfig(data) : addConfig(data);

    promise.then(() => {
      MessagePlugin.success(props.isEdit ? '更新成功' : '添加成功');
      emit('update:visible', false);
      emit('success');
    }).catch((e) => {
      MessagePlugin.error(e.message || (props.isEdit ? '更新失败' : '添加失败'));
    }).finally(() => {
      formLoading.value = false;
    });
  } else {
    console.log('Errors: ', validateResult);
    MessagePlugin.warning(firstError);
  }
};
</script>

<style lang="less" scoped>
// 弹窗内容样式优化
:deep(.t-dialog__body) {
  max-height: 500px;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 16px 24px;
}

:deep(.t-dialog) {
  .t-form {
    overflow: visible;
    width: 100%;
    box-sizing: border-box;
  }
  
  // 确保表单项不会超出宽度
  .t-form-item {
    margin-bottom: 16px;
  }
  
  // 输入框宽度限制
  .t-input,
  .t-input-number,
  .t-select,
  .t-textarea {
    width: 100%;
  }
  
  // 开关组件样式
  .t-switch {
    margin: 8px 0;
  }
}
</style>