package com.neo.aspect;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.neo.aspect.annontation.Sensitive;
import com.neo.enums.DesensitizedType;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;

@Aspect
@Component
@Slf4j
public class SensitiveAspect {

    /**
     * 定义切点，切入所有包含@Sensitive注解的方法
     */
    @Pointcut("@annotation(com.neo.aspect.annontation.Sensitive)")
    public void sensitivePointcut() {
    }

    /**
     * 环绕通知，处理包含敏感信息的对象
     *
     * @param joinPoint 切入点
     * @return 处理结果
     * @throws Throwable 异常
     */
    @Around("sensitivePointcut()")
    public Object sensitiveAround(ProceedingJoinPoint joinPoint) throws Throwable {
        // 执行原方法
        Object result = joinPoint.proceed();

        // 对结果进行脱敏处理
        return handleSensitiveData(result);
    }

    /**
     * 处理敏感数据
     *
     * @param result 原始结果
     * @return 处理后的结果
     */
    private Object handleSensitiveData(Object result) {
        if (result == null) {
            return null;
        }

        try {
            ObjectMapper mapper = new ObjectMapper();
            String json = mapper.writeValueAsString(result);
            // 如果是字符串，直接返回
            if (result instanceof String) {
                return json;
            }
            // 如果是基本类型，直接返回
            if (isPrimitiveOrWrapper(result.getClass()) || result.getClass() == String.class) {
                return result;
            }
            
            // 处理对象中的敏感字段
            processSensitiveFields(result);
            
            return result;
        } catch (JsonProcessingException e) {
            log.error("处理敏感数据时序列化失败: {}", e.getMessage());
            return result;
        } catch (Exception e) {
            log.error("处理敏感数据时发生异常: {}", e.getMessage());
            return result;
        }
    }

    /**
     * 处理对象中的敏感字段
     *
     * @param obj 待处理的对象
     */
    private void processSensitiveFields(Object obj) {
        if (obj == null) {
            return;
        }

        // 获取所有字段，包括父类字段
        Field[] fields = getAllFields(obj.getClass());
        for (Field field : fields) {
            try {
                // 检查字段是否包含Sensitive注解
                if (field.isAnnotationPresent(Sensitive.class)) {
                    field.setAccessible(true);
                    Object value = field.get(obj);
                    
                    if (value instanceof String && StringUtils.hasText((String) value)) {
                        Sensitive sensitive = field.getAnnotation(Sensitive.class);
                        DesensitizedType desensitizedType = sensitive.desensitizedType();
                        
                        // 执行脱敏处理
                        String desensitizedValue = desensitizedType.desensitizer().apply((String) value);
                        field.set(obj, desensitizedValue);
                    }
                }
            } catch (IllegalAccessException e) {
                log.error("访问字段 {} 时发生异常: {}", field.getName(), e.getMessage());
            }
        }
    }

    /**
     * 获取类的所有字段，包括父类字段
     *
     * @param clazz 类
     * @return 所有字段数组
     */
    private Field[] getAllFields(Class<?> clazz) {
        // 这里可以使用更复杂的方式获取包括父类在内的所有字段
        return clazz.getDeclaredFields();
    }

    /**
     * 判断是否为基本类型或包装类型
     *
     * @param clazz 类
     * @return 是否为基本类型或包装类型
     */
    private boolean isPrimitiveOrWrapper(Class<?> clazz) {
        return clazz.isPrimitive() ||
                clazz == Boolean.class || clazz == Byte.class || clazz == Character.class ||
                clazz == Short.class || clazz == Integer.class || clazz == Long.class ||
                clazz == Float.class || clazz == Double.class;
    }
}