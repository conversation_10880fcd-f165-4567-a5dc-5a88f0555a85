package com.neo.module.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.neo.aspect.annontation.PrePermissions;
import com.neo.model.Result;
import com.neo.common.QueryGenerator;
import com.neo.module.entity.SysConfig;
import com.neo.module.service.SysConfigService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 系统配置
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
@RestController
@RequestMapping("/sysConfig")
public class SysConfigController {
    @Resource
    private SysConfigService sysConfigService;

    /**
     * 分页查询
     *
     * @param params
     * @return
     */
    @PrePermissions("system:sysConfig:list")
    @PostMapping("/page")
    public Result<?> page(@RequestBody Map<String, String> params) {
        QueryWrapper<SysConfig> queryWrapper = QueryGenerator.initQueryWrapper(SysConfig.class, params);
        return Result.ok(sysConfigService.page(params, queryWrapper));
    }

    /**
     * 查询
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public Result<?> get(@PathVariable String id) {
        SysConfig sysConfig = sysConfigService.getById(id);
        return Result.ok(sysConfig);
    }

    /**
     * 列表
     *
     * @param sysConfig
     * @return
     */
    @PostMapping("/list")
    public Result<?> list(@RequestBody SysConfig sysConfig) {
        List<SysConfig> list = sysConfigService.list(sysConfig);
        return Result.ok(list);
    }

    /**
     * 新增
     *
     * @param sysConfig
     * @return
     */
    @PrePermissions("system:sysConfig:add")
    @PostMapping("/add")
    public Result<?> add(@RequestBody SysConfig sysConfig) {
        sysConfigService.save(sysConfig);
        return Result.ok();
    }

    /**
     * 删除
     *
     * @param ids
     * @return
     */
    @PrePermissions("system:sysConfig:delete")
    @DeleteMapping
    public Result<?> delete(@RequestBody Set<String> ids) {
        sysConfigService.removeByIds(ids);
        return Result.ok();
    }

    /**
     * 更新
     *
     * @param sysConfig
     * @return
     */
    @PrePermissions("system:sysConfig:update")
    @PutMapping("/update")
    public Result<?> update(@RequestBody SysConfig sysConfig) {
        sysConfigService.updateById(sysConfig);
        return Result.ok();
    }

}

