<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.neo.module.mapper.SysLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.neo.module.entity.SysLog">

        <result column="user_id" property="userId" />
        <result column="username" property="username" />
        <result column="business_id" property="businessId" />
        <result column="business_module" property="businessModule" />
        <result column="operate_type" property="operateType" />
        <result column="operate_name" property="operateName" />
        <result column="method_name" property="methodName" />
        <result column="request_url" property="requestUrl" />
        <result column="request_param" property="requestParam" />
        <result column="request_method" property="requestMethod" />
        <result column="result_json" property="resultJson" />
        <result column="ip_address" property="ipAddress" />
        <result column="cost_time" property="costTime" />
        <result column="id" property="id" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
id,create_time,user_id, username, business_id, business_module, operate_type, operate_name, method_name, request_url, request_param, request_method, result_json, ip_address, cost_time
    </sql>

</mapper>
