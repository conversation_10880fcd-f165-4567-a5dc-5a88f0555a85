<template>
  <div class="table-container">
    <t-card class="list-card-container" :bordered="false">
      <t-row justify="space-between">
        <div class="left-operation-container">
          <t-button @click="() => handleAdd()">新增部门</t-button>
          <t-button variant="base" theme="default" @click="expandAll(true)">展开全部</t-button>
          <t-button variant="base" theme="default" @click="expandAll(false)">折叠全部</t-button>
        </div>
        <div class="search-input">
          <t-input 
            v-model="filterText" 
            placeholder="请输入部门名称" 
            clearable
            @change="onFilter"
          >
            <template #suffix-icon>
              <search-icon size="16px" />
            </template>
          </t-input>
        </div>
      </t-row>

      <t-enhanced-table
        ref="tableRef"
        :columns="columns"
        :data="filteredDeptData"
        :tree="{ childrenKey: 'children' }"
        :loading="loading"
        :hover="true"
        :stripe="true"
        v-model:expanded-tree-nodes="expandedNodes"
        row-key="id"
        @expand-change="onExpandChange"
      >
        <template #status="{ row }">
          <t-tag v-if="row.status === '1'" theme="success" variant="light">
            启用
          </t-tag>
          <t-tag v-else theme="danger" variant="light">
            禁用
          </t-tag>
        </template>
        
        <template #op="{ row }">
          <t-space :size="8">
            <t-link theme="primary" hover="color" @click="handleEdit(row)">编辑</t-link>
            <t-link theme="success" hover="color" @click="handleAdd(row)">新增子部门</t-link>
            <t-popconfirm content="确认删除吗？删除后子部门也将被删除" @confirm="handleDelete(row)">
              <t-link theme="danger" hover="color">删除</t-link>
            </t-popconfirm>
          </t-space>
        </template>
      </t-enhanced-table>
    </t-card>

    <!-- 部门编辑弹窗 -->
    <EditModel 
      v-model:visible="formVisible" 
      :is-edit="isEdit" 
      :dept-data="editDeptData"
      :parent-dept="parentDeptData"
      :dept-tree-data="deptTreeData"
      @success="refresh"
    />
  </div>
</template>

<script lang="ts" setup>
import { SearchIcon } from 'tdesign-icons-vue-next';
import { MessagePlugin, EnhancedTable as TEnhancedTable } from 'tdesign-vue-next';
import { onMounted, ref, computed } from 'vue';
import type { PrimaryTableCol, TreeNodeValue, ExpandOptions } from 'tdesign-vue-next';

import {
  getDeptTree,
  getDeptById,
  deleteDept,
} from '@/api/dept';
import { SysDept, SysDeptTree } from '@/api/model/deptModel';
import EditModel from './components/EditModel.vue';

defineOptions({
  name: 'ListDept',
});

// 表格相关
const tableRef = ref();
const loading = ref(false);
const deptTreeData = ref<SysDeptTree[]>([]);
const expandedNodes = ref<TreeNodeValue[]>([]);
const filterText = ref('');

// 表格列定义
const columns: PrimaryTableCol[] = [
  { 
    colKey: 'name', 
    title: '部门名称', 
    width: 200,
    ellipsis: true
  },
  { 
    colKey: 'sort', 
    title: '排序', 
    width: 80,
    align: 'center'
  },
  { 
    colKey: 'leader', 
    title: '负责人', 
    width: 120
  },
  { 
    colKey: 'phone', 
    title: '联系电话', 
    width: 150
  },
  { 
    colKey: 'email', 
    title: '邮箱', 
    width: 200,
    ellipsis: true
  },
  { 
    colKey: 'status', 
    title: '状态', 
    width: 80,
    align: 'center'
  },
  { 
    colKey: 'createTime', 
    title: '创建时间', 
    width: 180,
    ellipsis: true
  },
  { 
    colKey: 'op', 
    title: '操作', 
    width: 200, 
    fixed: 'right',
    align: 'center'
  },
];

// 过滤后的部门数据
const filteredDeptData = computed(() => {
  if (!filterText.value) {
    return deptTreeData.value;
  }
  
  const filterLower = filterText.value.toLowerCase();
  return filterDeptData(deptTreeData.value, filterLower);
});

// 过滤部门数据
const filterDeptData = (data: SysDeptTree[], filterText: string): SysDeptTree[] => {
  if (!filterText) return data;
  
  return data.reduce((acc: SysDeptTree[], item) => {
    // 检查当前节点是否匹配
    const isMatch = item.name?.toLowerCase().includes(filterText);
    
    // 递归处理子节点
    const filteredChildren = item.children ? filterDeptData(item.children, filterText) : [];
    
    // 如果当前节点匹配或者有子节点匹配，则保留该节点
    if (isMatch || filteredChildren.length > 0) {
      acc.push({
        ...item,
        children: filteredChildren
      });
    }
    
    return acc;
  }, []);
};

// 弹窗相关
const formVisible = ref(false);
const isEdit = ref(false);
const editDeptData = ref<SysDept>();
const parentDeptData = ref<SysDept>();

// 获取部门数据
const getDeptData = async () => {
  loading.value = true;
  try {
    const res = await getDeptTree();
    deptTreeData.value = res || [];
    // 默认展开所有节点
    setTimeout(() => {
      expandAll(true);
    }, 100);
  } catch (error) {
    MessagePlugin.error('获取部门数据失败');
  } finally {
    loading.value = false;
  }
};

// 刷新数据
const refresh = () => {
  getDeptData();
};

// 展开/折叠所有节点
const expandAll = (expand: boolean) => {
  if (expand) {
    // 展开所有节点
    tableRef.value?.expandAll();
  } else {
    // 折叠所有节点
    tableRef.value?.foldAll();
  }
};

// 处理节点展开/折叠
const onExpandChange = (expanded: TreeNodeValue[], options: ExpandOptions<unknown>) => {
  expandedNodes.value = expanded;
};

// 过滤处理
const onFilter = () => {
  // 当过滤文本变化时，展开所有节点以确保能看到匹配的结果
  if (filterText.value) {
    setTimeout(() => {
      expandAll(true);
    }, 0);
  }
};

// 新增部门
const handleAdd = (parentRow?: SysDept) => {
  isEdit.value = false;
  editDeptData.value = undefined;
  parentDeptData.value = parentRow;
  formVisible.value = true;
};

// 编辑部门
const handleEdit = async (row: SysDept) => {
  try {
    loading.value = true;
    const deptDetail = await getDeptById(String(row.id));
    
    isEdit.value = true;
    editDeptData.value = deptDetail;
    parentDeptData.value = undefined;
    formVisible.value = true;
  } catch (error) {
    console.error('获取部门详情失败:', error);
    MessagePlugin.error('获取部门详情失败，请重试');
  } finally {
    loading.value = false;
  }
};

// 删除部门
const handleDelete = async (row: SysDept) => {
  try {
    await deleteDept([String(row.id)]);
    MessagePlugin.success('删除成功');
    refresh();
  } catch (error) {
    console.error('删除失败:', error);
    MessagePlugin.error('删除失败，请重试');
  }
};

// 挂载时获取数据
onMounted(() => {
  refresh();
});
</script>

<style lang="less" scoped>
.table-container {
  background-color: var(--td-bg-color-container);
  border-radius: var(--td-radius-medium);
  min-height: 600px;
  padding: var(--td-comp-paddingTB-xxl) var(--td-comp-paddingLR-xxl);
}

.left-operation-container {
  display: flex;
  align-items: center;
  margin-bottom: var(--td-comp-margin-xxl);
  gap: var(--td-comp-margin-l);
}

.search-input {
  width: 360px;
}
</style>