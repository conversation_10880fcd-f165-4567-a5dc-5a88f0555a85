<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.7</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.neo</groupId>
    <artifactId>neo-parent</artifactId>
    <version>${revision}</version>
    <name>neo-parent</name>
    <modules>
        <module>neo-core</module>
        <module>neo-database</module>
        <module>neo-cloud</module>
        <module>neo-doc</module>
        <module>neo-web</module>
        <module>neo-elasticsearch</module>
        <module>neo-mq</module>
        <module>neo-security</module>
        <module>neo-job</module>
    </modules>
    <packaging>pom</packaging>
    <description>Neo-Cloud-微服务系统</description>

    <properties>
        <revision>1.0.0</revision>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <spring-boot.version>2.7.5</spring-boot.version>
        <spring-cloud.version>2021.0.8</spring-cloud.version>
        <spring-cloud-alibaba.version>2021.0.5.0</spring-cloud-alibaba.version>
        <dubbo.version>3.1.6</dubbo.version>
        <lock4j.version>2.2.7</lock4j.version>
        <rocketmq.version>2.3.0</rocketmq.version>
        <fastjson.version>2.0.14</fastjson.version>
        <transmittable-thread-local.version>2.13.2</transmittable-thread-local.version>
        <jjwt.version>0.9.1</jjwt.version>
        <commons.version>2.6</commons.version>
        <hutool.version>5.8.12</hutool.version>
        <!-- 数据库驱动 -->
        <postgresql.version>42.2.25</postgresql.version>
        <ojdbc6.version>********</ojdbc6.version>
        <sqljdbc4.version>4.0</sqljdbc4.version>
        <mysql-connector-java.version>8.0.27</mysql-connector-java.version>
        <!-- 持久层 -->
        <mybatis-plus.version>3.5.1</mybatis-plus.version>
        <dynamic-datasource-spring-boot-starter.version>3.5.0</dynamic-datasource-spring-boot-starter.version>
        <druid.version>1.1.22</druid.version>
        <!-- Log4j2爆雷漏洞 -->
        <log4j2.version>2.17.0</log4j2.version>
        <logback.version>1.2.9</logback.version>
        <!-- 定时任务 -->
        <xxljob.version>2.4.2</xxljob.version>
        <!-- mq配置 -->
        <rocketmq.version>2.3.0</rocketmq.version>
        <!-- ElasticSearch -->
        <elasticSearch.version>7.17.28</elasticSearch.version>
        <easy-es.version>3.0.0</easy-es.version>
        <!-- Minio -->
        <minio.version>8.2.2</minio.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- spring-cloud-->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- spring-cloud-alibaba -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Apache Dubbo 配置 -->
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-spring-boot-starter</artifactId>
                <version>${dubbo.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-spring-boot-actuator</artifactId>
                <version>${dubbo.version}</version>
            </dependency>

            <!-- JWT -->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jjwt.version}</version>
            </dependency>

            <!-- commons -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-lang</groupId>
                <artifactId>commons-lang</artifactId>
                <version>${commons.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <!-- mybatis-plus -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-core</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <!-- druid -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <!-- 动态数据源 -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>${dynamic-datasource-spring-boot-starter.version}</version>
            </dependency>

            <!-- 线程传递值 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${transmittable-thread-local.version}</version>
            </dependency>

            <dependency>
                <groupId>com.neo</groupId>
                <artifactId>neo-core</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.neo</groupId>
                <artifactId>neo-cloud</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.neo</groupId>
                <artifactId>neo-database</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.neo</groupId>
                <artifactId>neo-web</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.neo</groupId>
                <artifactId>neo-security</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.neo</groupId>
                <artifactId>neo-system-api</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.neo</groupId>
                <artifactId>neo-resource-api</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.neo</groupId>
                <artifactId>neo-doc</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.neo</groupId>
                <artifactId>neo-elasticsearch</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-high-level-client</artifactId>
                <version>${elasticSearch.version}</version>
            </dependency>

            <dependency>
                <groupId>org.elasticsearch</groupId>
                <artifactId>elasticsearch</artifactId>
                <version>${elasticSearch.version}</version>
            </dependency>

            <dependency>
                <groupId>com.neo</groupId>
                <artifactId>neo-mq</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxljob.version}</version>
            </dependency>

            <dependency>
                <groupId>org.dromara.easy-es</groupId>
                <artifactId>easy-es-boot-starter</artifactId>
                <version>${easy-es.version}</version>
            </dependency>

            <!--消息队列-->
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-spring-boot-starter</artifactId>
                <version>${rocketmq.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <!-- json -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>${fastjson.version}</version>
        </dependency>

    </dependencies>

    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <!-- 环境标识，需要与配置文件的名称相对应 -->
                <profiles.active>dev</profiles.active>
                <nacos.server>192.168.31.155:8848</nacos.server>
                <nacos.discovery.group>DEFAULT_GROUP</nacos.discovery.group>
                <nacos.config.group>DEFAULT_GROUP</nacos.config.group>
            </properties>
            <activation>
                <!-- 默认环境 -->
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <!-- 环境标识，需要与配置文件的名称相对应 -->
                <profiles.active>test</profiles.active>
                <nacos.server>38.207.190.19:8848</nacos.server>
                <nacos.discovery.group>DEFAULT_GROUP</nacos.discovery.group>
                <nacos.config.group>DEFAULT_GROUP</nacos.config.group>
            </properties>
            <activation>
                <!-- 默认环境 -->
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <profiles.active>prod</profiles.active>
                <nacos.server>127.0.0.1:8848</nacos.server>
                <nacos.discovery.group>DEFAULT_GROUP</nacos.discovery.group>
                <nacos.config.group>DEFAULT_GROUP</nacos.config.group>
            </properties>
        </profile>
        <profile>
            <id>jdxb</id>
            <properties>
                <profiles.active>jdxb</profiles.active>
                <nacos.server>100.66.1.3:8848</nacos.server>
                <nacos.discovery.group>DEFAULT_GROUP</nacos.discovery.group>
                <nacos.config.group>DEFAULT_GROUP</nacos.config.group>
            </properties>
        </profile>
    </profiles>

    <repositories>
        <repository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

    <build>
        <plugins>
            <!-- 添加flatten-maven-plugin插件 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.3.0</version>
                <inherited>true</inherited>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                        <configuration>
                            <!-- 避免IDE将 .flattened-pom.xml 自动识别为功能模块 -->
                            <updatePomFile>true</updatePomFile>
                            <flattenMode>resolveCiFriendliesOnly</flattenMode>
                            <pomElements>
                                <parent>expand</parent>
                                <distributionManagement>remove</distributionManagement>
                                <repositories>remove</repositories>
                            </pomElements>
                        </configuration>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>