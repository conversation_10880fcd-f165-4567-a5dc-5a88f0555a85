package com.neo.context;

import com.neo.annotation.DataScope;
import lombok.Data;

/**
 * 数据权限上下文
 * 用于存储当前线程的数据权限信息
 * 
 * <AUTHOR>
 */
@Data
public class DataScopeContext {

    /**
     * 是否清除数据权限
     */
    private Boolean dataScopeClearFlag = false;

    /**
     * 数据权限SQL片段
     */
    private String dataScopeSql = "";

    /**
     * 数据权限注解
     */
    private DataScope dataScopeAnnotation;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 部门ID
     */
    private String deptId;

    /**
     * 数据权限范围
     * 1：全部数据权限 
     * 2：自定数据权限 
     * 3：本部门数据权限 
     * 4：本部门及以下数据权限
     */
    private String dataScopeType;

    /**
     * 权限部门ID列表（用于自定数据权限）
     */
    private String deptIds;

    private static final ThreadLocal<DataScopeContext> CONTEXT_HOLDER = new ThreadLocal<>();

    /**
     * 获取当前线程的数据权限上下文
     */
    public static DataScopeContext getContext() {
        DataScopeContext context = CONTEXT_HOLDER.get();
        if (context == null) {
            context = new DataScopeContext();
            CONTEXT_HOLDER.set(context);
        }
        return context;
    }

    /**
     * 设置当前线程的数据权限上下文
     */
    public static void setContext(DataScopeContext context) {
        CONTEXT_HOLDER.set(context);
    }

    /**
     * 清除当前线程的数据权限上下文
     */
    public static void clearContext() {
        CONTEXT_HOLDER.remove();
    }

}