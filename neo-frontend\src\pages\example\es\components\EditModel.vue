<template>
  <t-dialog 
    v-model:visible="dialogVisible" 
    :header="dialogTitle" 
    :width="800" 
    :confirm-btn="{ loading: formLoading }" 
    @confirm="onConfirm" 
    @cancel="onCancel"
  >
    <template v-if="dialogVisible">
      <t-form ref="form" :data="formData" :rules="rules" :label-width="100" @submit="onSubmit">
        <t-row :gutter="[16, 16]">
          <t-col :span="6">
            <t-form-item label="手机品牌" name="brand">
              <t-input 
                v-model="formData.brand" 
                placeholder="请输入手机品牌"
              />
            </t-form-item>
          </t-col>
          
          <t-col :span="6">
            <t-form-item label="手机型号" name="model">
              <t-input 
                v-model="formData.model" 
                placeholder="请输入手机型号"
              />
            </t-form-item>
          </t-col>
        </t-row>

        <t-row :gutter="[16, 16]">
          <t-col :span="6">
            <t-form-item label="处理器型号" name="cpu">
              <t-input 
                v-model="formData.cpu" 
                placeholder="请输入处理器型号"
              />
            </t-form-item>
          </t-col>
          
          <t-col :span="6">
            <t-form-item label="内存大小(GB)" name="memorySize">
              <t-input-number 
                v-model="formData.memorySize" 
                placeholder="请输入内存大小"
                :min="1"
                :max="1024"
              />
            </t-form-item>
          </t-col>
        </t-row>

        <t-row :gutter="[16, 16]">
          <t-col :span="6">
            <t-form-item label="存储大小(GB)" name="storageSize">
              <t-input-number 
                v-model="formData.storageSize" 
                placeholder="请输入存储大小"
                :min="1"
                :max="2048"
              />
            </t-form-item>
          </t-col>
          
          <t-col :span="6">
            <t-form-item label="屏幕尺寸(英寸)" name="screenSize">
              <t-input-number 
                v-model="formData.screenSize" 
                placeholder="请输入屏幕尺寸"
                :min="3"
                :max="15"
                :decimal-places="1"
              />
            </t-form-item>
          </t-col>
        </t-row>

        <t-row :gutter="[16, 16]">
          <t-col :span="6">
            <t-form-item label="屏幕分辨率" name="screenResolution">
              <t-input 
                v-model="formData.screenResolution" 
                placeholder="如：1920x1080"
              />
            </t-form-item>
          </t-col>
          
          <t-col :span="6">
            <t-form-item label="主摄像头(MP)" name="cameraMain">
              <t-input-number 
                v-model="formData.cameraMain" 
                placeholder="请输入主摄像头像素"
                :min="1"
                :max="200"
              />
            </t-form-item>
          </t-col>
        </t-row>

        <t-row :gutter="[16, 16]">
          <t-col :span="6">
            <t-form-item label="前摄像头(MP)" name="cameraFront">
              <t-input-number 
                v-model="formData.cameraFront" 
                placeholder="请输入前摄像头像素"
                :min="1"
                :max="100"
              />
            </t-form-item>
          </t-col>
          
          <t-col :span="6">
            <t-form-item label="电池容量(mAh)" name="batteryCapacity">
              <t-input-number 
                v-model="formData.batteryCapacity" 
                placeholder="请输入电池容量"
                :min="1000"
                :max="10000"
              />
            </t-form-item>
          </t-col>
        </t-row>

        <t-row :gutter="[16, 16]">
          <t-col :span="6">
            <t-form-item label="操作系统类型" name="osType">
              <t-select 
                v-model="formData.osType" 
                :options="osTypeOptions"
                placeholder="请选择操作系统类型"
              />
            </t-form-item>
          </t-col>
          
          <t-col :span="6">
            <t-form-item label="操作系统版本" name="osVersion">
              <t-input 
                v-model="formData.osVersion" 
                placeholder="如：Android 13"
              />
            </t-form-item>
          </t-col>
        </t-row>

        <t-row :gutter="[16, 16]">
          <t-col :span="6">
            <t-form-item label="价格(元)" name="price">
              <t-input-number 
                v-model="formData.price" 
                placeholder="请输入价格"
                :min="0"
                :max="50000"
                :decimal-places="2"
              />
            </t-form-item>
          </t-col>
          
          <t-col :span="6">
            <t-form-item label="发布日期" name="releaseDate">
              <t-date-picker 
                v-model="formData.releaseDate" 
                placeholder="请选择发布日期"
                format="YYYY-MM-DD"
              />
            </t-form-item>
          </t-col>
        </t-row>

        <t-row :gutter="[16, 16]">
          <t-col :span="6">
            <t-form-item label="网络类型" name="networkType">
              <t-select 
                v-model="formData.networkType" 
                :options="networkTypeOptions"
                placeholder="请选择网络类型"
              />
            </t-form-item>
          </t-col>
          
          <t-col :span="6">
            <t-form-item label="重量(g)" name="weight">
              <t-input-number 
                v-model="formData.weight" 
                placeholder="请输入重量"
                :min="50"
                :max="1000"
                :decimal-places="1"
              />
            </t-form-item>
          </t-col>
        </t-row>

        <t-row :gutter="[16, 16]">
          <t-col :span="6">
            <t-form-item label="颜色" name="color">
              <t-input 
                v-model="formData.color" 
                placeholder="请输入颜色"
              />
            </t-form-item>
          </t-col>
          
          <t-col :span="6">
            <t-form-item label="5G支持" name="is5gSupported">
              <t-switch 
                v-model="formData.is5gSupported" 
                :custom-value="['Y', 'N']" 
                checked="Y" 
                unchecked="N"
                size="small"
              />
            </t-form-item>
          </t-col>
        </t-row>
      </t-form>
    </template>
  </t-dialog>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { addEsPhoneInfo, updateEsPhoneInfo } from '@/api/esPhoneInfo';
import { EsPhoneInfo } from '@/api/model/esPhoneInfoModel';

// 定义 props
interface Props {
  visible: boolean;
  isEdit: boolean;
  phoneData?: EsPhoneInfo;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  isEdit: false,
  phoneData: undefined
});

// 定义 emits
const emit = defineEmits<{
  'update:visible': [value: boolean];
  'success': [];
}>();

const form = ref();
const formLoading = ref(false);

// 选项配置
const osTypeOptions = [
  { value: 'Android', label: 'Android' },
  { value: 'iOS', label: 'iOS' },
  { value: 'HarmonyOS', label: 'HarmonyOS' },
];

const networkTypeOptions = [
  { value: '4G', label: '4G' },
  { value: '5G', label: '5G' },
  { value: '4G/5G', label: '4G/5G' },
];

const formData = ref({
  id: '',
  brand: '',
  model: '',
  cpu: '',
  memorySize: null,
  storageSize: null,
  screenSize: null,
  screenResolution: '',
  cameraMain: null,
  cameraFront: null,
  batteryCapacity: null,
  osType: '',
  osVersion: '',
  price: null,
  releaseDate: '',
  networkType: '',
  weight: null,
  color: '',
  is5gSupported: 'N',
});

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => emit('update:visible', value)
});

const dialogTitle = computed(() => {
  return props.isEdit ? '编辑ES手机信息' : '新增ES手机信息';
});

const rules = computed(() => ({
  brand: [
    { required: true, message: '请输入手机品牌' }
  ],
  model: [
    { required: true, message: '请输入手机型号' }
  ],
  cpu: [
    { required: true, message: '请输入处理器型号' }
  ],
  osType: [
    { required: true, message: '请选择操作系统类型' }
  ],
}));

// 监听弹窗显示状态
watch(() => props.visible, (visible) => {
  if (visible) {
    resetForm();
    if (props.isEdit && props.phoneData) {
      // 编辑模式，填充数据
      Object.assign(formData.value, props.phoneData);
    }
  }
});

// 重置表单
const resetForm = () => {
  formData.value = {
    id: '',
    brand: '',
    model: '',
    cpu: '',
    memorySize: null,
    storageSize: null,
    screenSize: null,
    screenResolution: '',
    cameraMain: null,
    cameraFront: null,
    batteryCapacity: null,
    osType: '',
    osVersion: '',
    price: null,
    releaseDate: '',
    networkType: '',
    weight: null,
    color: '',
    is5gSupported: 'N',
  };
};

// 取消操作
const onCancel = () => {
  emit('update:visible', false);
};

// 确认操作
const onConfirm = () => {
  form.value?.submit();
};

// 表单提交
const onSubmit = async ({ validateResult, firstError }) => {
  if (validateResult === true) {
    formLoading.value = true;
    
    const promise = props.isEdit ? updateEsPhoneInfo({ ...formData.value }) : addEsPhoneInfo({ ...formData.value });

    promise.then(() => {
      MessagePlugin.success(props.isEdit ? '更新成功' : '添加成功');
      emit('update:visible', false);
      emit('success');
    }).catch((e) => {
      MessagePlugin.error(e.message || (props.isEdit ? '更新失败' : '添加失败'));
    }).finally(() => {
      formLoading.value = false;
    });
  } else {
    MessagePlugin.warning(firstError);
  }
};
</script>

<style lang="less" scoped>
// 弹窗内容样式优化
:deep(.t-dialog__body) {
  max-height: 600px;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 16px 24px;
}

:deep(.t-form-item) {
  margin-bottom: 16px;
}
</style>
