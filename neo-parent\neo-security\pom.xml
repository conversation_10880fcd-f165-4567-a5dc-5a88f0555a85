<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.neo</groupId>
        <artifactId>neo-parent</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>neo-security</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>neo-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>neo-system-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>neo-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-spring-boot-starter</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>

    </dependencies>

</project>