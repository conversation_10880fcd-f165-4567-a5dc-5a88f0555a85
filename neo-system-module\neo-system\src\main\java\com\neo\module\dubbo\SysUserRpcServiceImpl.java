package com.neo.module.dubbo;

import com.neo.module.dto.SysLoginLogDTO;
import com.neo.module.dto.SysUserDTO;
import com.neo.module.entity.SysLoginLog;
import com.neo.module.rpc.SysUserRpcService;
import com.neo.module.service.SysLoginLogService;
import com.neo.module.service.SysMenuService;
import com.neo.module.service.SysRoleService;
import com.neo.module.service.SysUserService;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

@DubboService
public class SysUserRpcServiceImpl implements SysUserRpcService {

    @Resource
    private SysUserService sysUserService;
    @Resource
    private SysLoginLogService sysLoginLogService;
    @Resource
    private SysMenuService sysMenuService;
    @Resource
    private SysRoleService sysRoleService;

    @Override
    public SysUserDTO getUserByUsername(String username) {
        return sysUserService.getUserByUsername(username);
    }

    @Override
    public void saveLog(SysLoginLogDTO sysLoginLogDTO) {
        SysLoginLog sysLoginLog = new SysLoginLog();
        sysLoginLog.setUserId(sysLoginLogDTO.getUserId());
        sysLoginLog.setUsername(sysLoginLogDTO.getUsername());
        sysLoginLog.setIpAddress(sysLoginLogDTO.getIpAddr());
        sysLoginLog.setBrowser(sysLoginLogDTO.getBrowser());
        sysLoginLog.setBrowserVersion(sysLoginLogDTO.getBrowserVersion());
        sysLoginLog.setEngine(sysLoginLogDTO.getEngine());
        sysLoginLog.setEngineVersion(sysLoginLogDTO.getEngineVersion());
        sysLoginLog.setOs(sysLoginLogDTO.getOs());
        sysLoginLog.setOsName(sysLoginLogDTO.getPlatform());
        sysLoginLog.setIsMobile(sysLoginLogDTO.getIsMobile());
        sysLoginLog.setStatus(sysLoginLogDTO.getStatus());
        sysLoginLog.setMessage(sysLoginLogDTO.getMessage());
        sysLoginLogService.save(sysLoginLog);
    }

}
