import { request } from '@/utils/request';
import type { LoginParams, LoginResult, CaptchaResult } from './model/loginModel';
import { Result } from '@/types/axios';

/**
 * 用户登录
 */
export function login(data: LoginParams) {
  // 转换字段名以匹配后端LoginModel
  const loginData = {
    username: data.account,
    password: data.password,
    codeId: data.codeId,
    code: data.code,
  };

  return request.post<LoginResult>({
    url: '/auth/login',
    data: loginData,
  });
}

/**
 * 用户登出
 */
export function logout() {
  return request.post({
    url: '/auth/logout',
  });
}

/**
 * 获取验证码
 */
export function getCaptcha() {
  return request.get<CaptchaResult>({
    url: '/auth/captcha',
  });
}
