package com.neo.module.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.neo.module.entity.SysLoginLog;

import java.util.List;
import java.util.Map;

/**
 * 登录日志 服务类
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
public interface SysLoginLogService extends IService<SysLoginLog> {

    IPage<SysLoginLog> page(Map<String, String> params, QueryWrapper<SysLoginLog> queryWrapper);

    List<SysLoginLog> list(SysLoginLog sysLoginLog);

}
