package ${package.Entity};

    #foreach($pkg in ${table.importPackages})
    import ${pkg};
    #end

    #if(${entityLombokModel})
    import lombok.Data;
    import lombok.EqualsAndHashCode;
    import lombok.NoArgsConstructor;
    #end

import java.io.Serializable;

public class ${entity}DTO implements Serializable {

    #foreach($field in ${table.fields})
        private ${field.propertyType} ${field.propertyName};

    #end
}
