// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

export async function page(options?: { [key: string]: any }) {
    return request<BaseResponse<PageInfo<${entity}Type.${entity}>>>('/#if(${controllerMappingHyphenStyle})${controllerMappingHyphen}#else${table.entityPath}#end/page', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        data: options
    });
}

export async function list(options?: { [key: string]: any }) {
    return request<BaseResponse<${entity}Type.${entity}[]>>('/#if(${controllerMappingHyphenStyle})${controllerMappingHyphen}#else${table.entityPath}#end/list', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        data: options
    });
}

export async function add(params:${entity}Type.${entity}) {
    return request<BaseResponse<any>>('/#if(${controllerMappingHyphenStyle})${controllerMappingHyphen}#else${table.entityPath}#end/add', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        data: params
    });
}

export async function update(params:${entity}Type.${entity}) {
    return request<BaseResponse<any>>('/#if(${controllerMappingHyphenStyle})${controllerMappingHyphen}#else${table.entityPath}#end/update', {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        data: params
    });
}

export async function deletes(ids:string[]) {
    return request<BaseResponse<any>>('/#if(${controllerMappingHyphenStyle})${controllerMappingHyphen}#else${table.entityPath}#end', {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
        },
        data: ids
    });
}
