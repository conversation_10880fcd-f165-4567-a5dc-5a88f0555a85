<template>
  <div class="dict-management-container">
    <!-- 筛选表单 -->
    <t-card class="filter-card" :bordered="false">
      <t-form :data="searchFormState" :label-width="80" colon @reset="onReset" @submit="onSubmit">
        <t-row>
          <t-col :span="10">
            <t-row :gutter="[24, 24]">
              <t-col :span="4">
                <t-form-item label="字典名称" name="dictName">
                  <t-input
                    v-model="searchFormState.dictName"
                    class="form-item-content"
                    type="search"
                    placeholder="请输入字典名称"
                    clearable
                  />
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="字典编码" name="dictCode">
                  <t-input
                    v-model="searchFormState.dictCode"
                    class="form-item-content"
                    type="search"
                    placeholder="请输入字典编码"
                    clearable
                  />
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="状态" name="status">
                  <t-select
                    v-model="searchFormState.status"
                    class="form-item-content"
                    :options="statusOptions"
                    placeholder="请选择状态"
                    clearable
                  />
                </t-form-item>
              </t-col>
            </t-row>
          </t-col>

          <t-col :span="2" class="operation-container">
            <t-button theme="primary" type="submit" :style="{ marginLeft: 'var(--td-comp-margin-s)' }">
              查询
            </t-button>
            <t-button type="reset" variant="base" theme="default">
              重置
            </t-button>
          </t-col>
        </t-row>
      </t-form>
    </t-card>

    <!-- 表格容器 -->
    <t-card class="table-card" :bordered="false">
      <div class="table-header">
        <div class="left-operation-container">
          <t-button theme="primary" @click="handleAdd">
            新增字典
          </t-button>
          <t-button theme="danger" :disabled="!selectedRowKeys.length" @click="handleBatchDelete">
            删除字典
          </t-button>
        </div>
      </div>

      <t-table
        :data="paginationData.dataSource.value"
        :columns="columns"
        :row-key="rowKey"
        :selected-row-keys="selectedRowKeys"
        vertical-align="top"
        :pagination="paginationData.tableConfig.value"
        :loading="paginationData.loading.value"
        @page-change="(pageInfo: any) => paginationData.handlePageChange(pageInfo, loadDictData, searchFormState)"
        @select-change="onSelectChange"
        @row-click="handleRowClick"
      >
        <template #status="{ row }">
          <t-tag v-if="row.status === '1'" theme="success" variant="light">
            启用
          </t-tag>
          <t-tag v-else theme="danger" variant="light">
            禁用
          </t-tag>
        </template>
        
        <template #op="slotProps">
          <t-space>
            <t-link theme="primary" @click="handleEdit(slotProps.row, $event)">编辑</t-link>
            <t-link theme="primary" @click="handleViewDictItems(slotProps.row, $event)">查看字典项</t-link>
            <t-popconfirm 
              content="确定要删除吗？" 
              @confirm="handleDelete(slotProps.row)"
            >
              <t-link theme="danger" @click="stopPropagation($event)">删除</t-link>
            </t-popconfirm>
          </t-space>
        </template>
      </t-table>
    </t-card>

    <!-- 字典编辑弹窗 -->
    <EditModel 
      v-model:visible="formVisible" 
      :is-edit="isEdit" 
      :dict-data="editDictData"
      @success="onFormSuccess"
    />

    <!-- 字典项管理弹窗 -->
    <DictItemModel
      v-model:visible="dictItemVisible"
      :dict-data="currentDict"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import type { PageInfo, PrimaryTableCol, RowEventContext, TableRowData } from 'tdesign-vue-next';
import { getDictPage, deleteDict } from '@/api/dict';
import type { SysDict, DictPageParams, DictListResult } from '@/api/model/dictModel';
import { usePagination, CommonPageParams, CommonPageResult } from '@/hooks';
import EditModel from './components/EditModel.vue';
import DictItemModel from './components/DictItemModel.vue';

// 状态选项
const statusOptions = [
  { value: '1', label: '启用' },
  { value: '0', label: '禁用' },
];

// 定义表格列
const columns: PrimaryTableCol[] = [
  { colKey: 'row-select', type: 'multiple' as const, width: 60, fixed: 'left' as const },
  { title: '字典名称', colKey: 'dictName', width: 200, ellipsis: true },
  { title: '字典编码', colKey: 'dictCode', width: 200, ellipsis: true },
  { title: '状态', colKey: 'status', width: 100 },
  { title: '描述', colKey: 'description', width: 300, ellipsis: true },
  { title: '创建时间', colKey: 'createTime', width: 200 },
  { title: '操作', colKey: 'op', width: 200, fixed: 'right' as const },
];

// 使用通用分页 hook
const paginationData = usePagination<SysDict>({
  defaultCurrent: 1,
  defaultPageSize: 10,
  paramType: 'default' // 使用 current/size 参数
});

// 表格相关数据
const selectedRowKeys = ref<string[]>([]);
const rowKey = 'id';

// 搜索表单
const searchFormState = reactive<Omit<DictPageParams, 'current' | 'pageSize'>>({
  dictName: '',
  dictCode: '',
  status: '',
});

// 对话框相关
const formVisible = ref(false);
const isEdit = ref(false);
const editDictData = ref<SysDict>();

// 字典项弹窗相关
const dictItemVisible = ref(false);
const currentDict = ref<SysDict>({});

// 数据加载函数
const loadDictData = async (params: CommonPageParams): Promise<CommonPageResult<SysDict>> => {
  const res = await getDictPage(params as DictPageParams);
  return {
    records: res.records,
    total: res.total,
    current: res.current,
    size: res.size,
  };
};

// 获取数据 - 为兼容性保留
const loadData = (params: DictPageParams) => {
  return paginationData.loadData(params, loadDictData);
};

// 查询
const searchQuery = () => {
  paginationData.resetToFirstPage(loadDictData, searchFormState);
};

// 表单提交
const onSubmit = () => {
  searchQuery();
};

// 表单重置
const onReset = () => {
  searchFormState.dictName = '';
  searchFormState.dictCode = '';
  searchFormState.status = '';
  searchQuery();
};

// 表格分页变化 - 为兼容性保留
const handlePageChange = (pageInfo: any) => {
  paginationData.handlePageChange(pageInfo, loadDictData, searchFormState);
};

// 选择变化
const onSelectChange = (selectedKeys: (string | number)[]) => {
  selectedRowKeys.value = selectedKeys as string[];
};

// 行点击
const handleRowClick = (context: RowEventContext<TableRowData>) => {
  handleViewDictItems(context.row as SysDict);
};

// 新增
const handleAdd = () => {
  isEdit.value = false;
  editDictData.value = undefined;
  formVisible.value = true;
};

// 编辑
const handleEdit = (record: SysDict, event?: Event) => {
  if (event) {
    event.stopPropagation();
  }
  isEdit.value = true;
  editDictData.value = record;
  formVisible.value = true;
};

// 删除
const handleDelete = async (record: SysDict) => {
  try {
    await deleteDict([record.id || '']);
    MessagePlugin.success('删除成功');
    searchQuery();
  } catch (error) {
    MessagePlugin.error('删除失败');
  }
};

// 批量删除
const handleBatchDelete = async () => {
  try {
    await deleteDict(selectedRowKeys.value);
    MessagePlugin.success('删除字典成功');
    selectedRowKeys.value = [];
    searchQuery();
  } catch (error) {
    MessagePlugin.error('删除字典失败');
  }
};

// 查看字典项
const handleViewDictItems = (row: SysDict, event?: Event) => {
  if (event) {
    event.stopPropagation();
  }
  currentDict.value = { ...row };
  dictItemVisible.value = true;
};

// 阻止事件冒泡
const stopPropagation = (event: Event) => {
  event.stopPropagation();
};

// 表单操作成功回调
const onFormSuccess = () => {
  paginationData.refreshData(loadDictData, searchFormState);
};

onMounted(() => {
  paginationData.loadData(paginationData.buildPageParams(searchFormState), loadDictData);
});
</script>

<style scoped lang="less">
.dict-management-container {
  background-color: var(--td-bg-color-container);
  padding: var(--td-comp-paddingTB-xxl) var(--td-comp-paddingLR-xxl);
  border-radius: var(--td-radius-medium);
  
  .filter-card {
    margin-bottom: var(--td-comp-margin-xxl);
  }
  
  .table-card {
    .table-header {
      margin-bottom: var(--td-comp-margin-xl);
      
      .left-operation-container {
        display: flex;
        align-items: center;
        gap: 16px;
      }
    }
  }
}

.form-item-content {
  width: 100%;
}

.operation-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 8px;
}

.dict-item-header {
  margin-bottom: 16px;

  .dict-info {
    margin-top: 16px;
    padding: 16px;
    background-color: #f5f5f5;
    border-radius: 4px;
  }
}
</style>