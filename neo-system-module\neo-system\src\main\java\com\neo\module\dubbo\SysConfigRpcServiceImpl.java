package com.neo.module.dubbo;

import cn.hutool.core.bean.BeanUtil;
import com.neo.module.dto.SysConfigDTO;
import com.neo.module.entity.SysConfig;
import com.neo.module.rpc.SysConfigRpcService;
import com.neo.module.service.SysConfigService;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

@DubboService
public class SysConfigRpcServiceImpl implements SysConfigRpcService {

    @Resource
    private SysConfigService sysConfigService;

    @Override
    public SysConfigDTO getConfig(String configKey) {
        SysConfig sysConfig = sysConfigService.getConfigByKey(configKey);
        if (sysConfig == null){
            return null;
        }
        SysConfigDTO sysConfigDTO = new SysConfigDTO();
        BeanUtil.copyProperties(sysConfig,sysConfigDTO);
        return sysConfigDTO;
    }

}
