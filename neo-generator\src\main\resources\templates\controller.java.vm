package ${package.Controller};

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.neo.model.Result;
import com.neo.common.QueryGenerator;
import ${package.Entity}.${table.entityName};
import ${package.Service}.${table.serviceName};
import javax.annotation.Resource;

import org.springframework.web.bind.annotation.*;

    #if(${restControllerStyle})
    import org.springframework.web.bind.annotation.RestController;
    #else
    import org.springframework.stereotype.Controller;
    #end
    #if(${superControllerClassPackage})
    import ${superControllerClassPackage};
    #end

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * $!{table.comment}
 * <AUTHOR>
 * @since ${date}
 */
#if(${restControllerStyle})
@RestController
#else
@Controller
#end
##@RequestMapping("#if(${package.ModuleName})/${package.ModuleName}#end/#if(${controllerMappingHyphenStyle})${controllerMappingHyphen}#else${table.entityPath}#end")
@RequestMapping("/#if(${controllerMappingHyphenStyle})${controllerMappingHyphen}#else${table.entityPath}#end")
#if(${kotlin})
class ${table.controllerName}#if(${superControllerClass}) : ${superControllerClass}()#end

#else
    #if(${superControllerClass})
    public class ${table.controllerName} extends ${superControllerClass} {
    #else
    public class ${table.controllerName} {
    #end
@Resource
private ${table.serviceName} ${table.entityPath}Service;

/**
* 分页查询
 * @param params
 * @return
*/
@PostMapping("/page")
public Result<?> page(@RequestBody Map<String, String> params) {
    QueryWrapper<${table.entityName}> queryWrapper = QueryGenerator.initQueryWrapper(${table.entityName}. class,params);
    return Result.ok(${table.entityPath}Service.page(params, queryWrapper));
}

/**
 * 查询
 * @param id
 * @return
 */
@GetMapping("/{id}")
public Result<?> get(@PathVariable String id) {
    ${table.entityName} ${table.entityPath} =${table.entityPath}Service.getById(id);
    return Result.ok(${table.entityPath});
}

/**
 * 列表
 * @param ${table.entityPath}
 * @return
 */
@PostMapping("/list")
public Result<?> list(@RequestBody ${table.entityName} ${table.entityPath}) {
    List<${table.entityName}> list = ${table.entityPath}Service.list(${table.entityPath});
    return Result.ok(list);
}

/**
 * 新增
 * @param ${table.entityPath}
 * @return
 */
@PostMapping("/add")
public Result<?> add(@RequestBody ${table.entityName} ${table.entityPath}) {
        ${table.entityPath}Service.save(${table.entityPath});
    return Result.ok();
}

/**
 * 删除
 * @param ids
 * @return
 */
@DeleteMapping
public Result<?> delete(@RequestBody Set<String> ids) {
        ${table.entityPath}Service.removeByIds(ids);
    return Result.ok();
}

/**
 * 更新
 * @param ${table.entityPath}
 * @return
 */
@PutMapping("/update")
public Result<?> update(@RequestBody ${table.entityName} ${table.entityPath}) {
        ${table.entityPath}Service.updateById(${table.entityPath});
    return Result.ok();
}
}

#end