package com.neo.module.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 文件配置
 * file:
 * prefix: /files/
 * domain: http://localhost:8080
 * storage-type: local  # 或 oss
 * local:
 * path: /path/to/your/upload/directory
 * oss:
 * endpoint: your-oss-endpoint
 * access-key: your-access-key
 * secret-key: your-secret-key
 * bucket: your-bucket-name
 */
@Data
@Component
@ConfigurationProperties(prefix = "file")
public class FileConfigProperties {

    private String prefix;
    private String domain;
    private String storageType = "local";
    private Local local = new Local();
    private Oss oss = new Oss();

    @Data
    public static class Local {
        private String path;
    }

    @Data
    public static class Oss {
        private String endpoint;
        private String accessKey;
        private String secretKey;
        private String bucket;
    }
} 