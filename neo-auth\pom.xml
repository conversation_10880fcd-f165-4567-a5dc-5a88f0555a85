<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.neo</groupId>
        <artifactId>neo-parent</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>neo-auth</artifactId>
    <modelVersion>4.0.0</modelVersion>

    <properties>
        <revision>1.0.0</revision>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>neo-cloud</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.neo</groupId>
                    <artifactId>neo-database</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>neo-security</artifactId>
        </dependency>

        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>neo-system-api</artifactId>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.neo</groupId>-->
<!--            <artifactId>neo-doc</artifactId>-->
<!--        </dependency>-->
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>