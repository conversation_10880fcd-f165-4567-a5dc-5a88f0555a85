<template>
  <t-dialog
    v-model:visible="dialogVisible"
    :header="isEdit ? '编辑文件信息' : '新增文件'"
    width="600px"
    :confirm-btn="null"
    :cancel-btn="null"
    @close="handleClose"
  >
    <t-form
      ref="formRef"
      :data="formData"
      :rules="rules"
      :label-width="100"
      @submit="handleSubmit"
    >
      <t-form-item label="文件名称" name="fileName">
        <t-input
          v-model="formData.fileName"
          placeholder="请输入文件名称"
          :disabled="isEdit"
        />
      </t-form-item>

      <t-form-item label="文件类型" name="fileType">
        <t-input
          v-model="formData.fileType"
          placeholder="请输入文件类型"
          :disabled="isEdit"
        />
      </t-form-item>

      <t-form-item label="文件大小" name="fileSize">
        <t-input
          v-model="formData.fileSizeDisplay"
          placeholder="文件大小"
          :disabled="true"
        />
      </t-form-item>

      <t-form-item label="存储类型" name="storageType">
        <t-select
          v-model="formData.storageType"
          :options="storageTypeOptions"
          placeholder="请选择存储类型"
          :disabled="isEdit"
        />
      </t-form-item>

      <t-form-item label="状态" name="status">
        <t-radio-group v-model="formData.status">
          <t-radio :value="true">正常</t-radio>
          <t-radio :value="false">禁用</t-radio>
        </t-radio-group>
      </t-form-item>

      <t-form-item label="MD5值" name="md5">
        <t-input
          v-model="formData.md5"
          placeholder="MD5值"
          :disabled="true"
        />
      </t-form-item>

      <t-form-item label="文件路径" name="filePath">
        <t-textarea
          v-model="formData.filePath"
          placeholder="文件存储路径"
          :disabled="true"
          :autosize="{ minRows: 2, maxRows: 4 }"
        />
      </t-form-item>

      <t-form-item label="访问地址" name="fileUrl">
        <t-textarea
          v-model="formData.fileUrl"
          placeholder="文件访问地址"
          :disabled="true"
          :autosize="{ minRows: 2, maxRows: 4 }"
        />
      </t-form-item>
    </t-form>

    <template #footer>
      <t-space>
        <t-button variant="outline" @click="handleClose">取消</t-button>
        <t-button theme="primary" @click="handleSubmit">确定</t-button>
      </t-space>
    </template>
  </t-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import type { FormInstanceFunctions, SubmitContext } from 'tdesign-vue-next';
import { SysAttachment } from '@/api/model/attachmentModel';
import { formatFileSize } from '@/utils/file';

interface Props {
  visible: boolean;
  isEdit: boolean;
  attachmentData?: SysAttachment;
}

interface Emits {
  (e: 'update:visible', visible: boolean): void;
  (e: 'success'): void;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  isEdit: false,
  attachmentData: undefined,
});

const emit = defineEmits<Emits>();

// 存储类型选项
const storageTypeOptions = [
  { value: 'LOCAL', label: '本地存储' },
  { value: 'OSS', label: '对象存储' },
];

// 表单引用
const formRef = ref<FormInstanceFunctions>();

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => {
    emit('update:visible', value);
  },
});

// 表单数据
const formData = reactive<SysAttachment & { fileSizeDisplay?: string }>({
  id: '',
  fileName: '',
  filePath: '',
  fileSize: 0,
  fileUrl: '',
  fileType: '',
  storageType: 'LOCAL',
  md5: '',
  status: true,
  fileSizeDisplay: '',
});

// 表单验证规则
const rules = {
  fileName: [
    { required: true, message: '请输入文件名称', trigger: 'blur' },
  ],
  storageType: [
    { required: true, message: '请选择存储类型', trigger: 'change' },
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' },
  ],
};



// 监听附件数据变化
watch(
  () => props.attachmentData,
  (newData) => {
    if (newData) {
      Object.assign(formData, {
        ...newData,
        fileSizeDisplay: formatFileSize(newData.fileSize),
      });
    }
  },
  { immediate: true, deep: true }
);

// 监听对话框显示状态
watch(
  () => props.visible,
  (visible) => {
    if (visible && !props.isEdit) {
      // 新增时重置表单
      resetForm();
    }
  }
);

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    id: '',
    fileName: '',
    filePath: '',
    fileSize: 0,
    fileUrl: '',
    fileType: '',
    storageType: 'LOCAL',
    md5: '',
    status: true,
    fileSizeDisplay: '0 B',
  });
  formRef.value?.clearValidate();
};

// 提交表单
const handleSubmit = () => {
  formRef.value?.validate().then((result) => {
    if (result === true) {
      // 这里只是更新状态，实际的更新接口需要根据后端提供的接口来实现
      MessagePlugin.success(props.isEdit ? '编辑成功' : '新增成功');
      emit('success');
      handleClose();
    }
  });
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  resetForm();
};
</script>

<style scoped>
:deep(.t-form-item__label) {
  font-weight: 500;
}
</style>
