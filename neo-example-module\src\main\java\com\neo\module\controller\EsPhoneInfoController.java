package com.neo.module.controller;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.neo.model.Result;
import com.neo.module.commom.EsBaseController;
import com.neo.module.entity.EsPhoneInfo;
import com.neo.module.es.EsPhoneInfoMapper;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * ES手机信息控制器
 *
 * <AUTHOR>
 * @since 2025-08-22
 */
@RestController
@RequestMapping("/es/phoneInfo")
public class EsPhoneInfoController extends EsBaseController {

    @Resource
    private EsPhoneInfoMapper esPhoneInfoMapper;

    /**
     * 分页查询手机信息
     *
     * @param params 查询参数
     * @return 分页结果
     */
    @PostMapping("/page")
    public Result<?> page(@RequestBody Map<String, String> params) {
        LambdaEsQueryWrapper<EsPhoneInfo> wrapper = new LambdaEsQueryWrapper<>();
        return Result.ok(esPhoneInfoMapper.pageQuery(wrapper, this.getCurrentPage(params), this.getPageSize(params)));
    }

    /**
     * 查询手机信息列表
     *
     * @param params 查询参数
     * @return 查询结果
     */
    @PostMapping("/list")
    public Result<?> list(@RequestBody Map<String, String> params) {
        LambdaEsQueryWrapper<EsPhoneInfo> wrapper = new LambdaEsQueryWrapper<>();
        List<EsPhoneInfo> list = esPhoneInfoMapper.selectList(wrapper);
        return Result.ok(list);
    }

    /**
     * 根据ID查询手机信息
     *
     * @param id 手机ID
     * @return 手机信息
     */
    @GetMapping("/{id}")
    public Result<?> get(@PathVariable String id) {
        EsPhoneInfo phoneInfo = esPhoneInfoMapper.selectById(id);
        return Result.ok(phoneInfo);
    }

    /**
     * 新增手机信息
     *
     * @param esPhoneInfo 手机信息
     * @return 操作结果
     */
    @PostMapping("/add")
    public Result<?> add(@RequestBody EsPhoneInfo esPhoneInfo) {
        esPhoneInfo.setId(IdWorker.getIdStr());
        esPhoneInfoMapper.insert(esPhoneInfo);
        return Result.ok();
    }

    /**
     * 批量新增手机信息
     *
     * @param phoneInfoList 手机信息列表
     * @return 操作结果
     */
    @PostMapping("/batchAdd")
    public Result<?> batchAdd(@RequestBody List<EsPhoneInfo> phoneInfoList) {
        for (EsPhoneInfo esPhoneInfo : phoneInfoList) {
            esPhoneInfo.setId(IdWorker.getIdStr());
        }
        esPhoneInfoMapper.insertBatch(phoneInfoList);
        return Result.ok();
    }

    /**
     * 更新手机信息
     *
     * @param esPhoneInfo 手机信息
     * @return 操作结果
     */
    @PutMapping("/update")
    public Result<?> update(@RequestBody EsPhoneInfo esPhoneInfo) {
        esPhoneInfoMapper.updateById(esPhoneInfo);
        return Result.ok();
    }

    /**
     * 删除手机信息
     *
     * @param ids 手机ID集合
     * @return 操作结果
     */
    @DeleteMapping
    public Result<?> delete(@RequestBody Set<String> ids) {
        esPhoneInfoMapper.deleteBatchIds(ids);
        return Result.ok();
    }

    /**
     * 创建索引
     *
     * @return 操作结果
     */
    @PostMapping("/createIndex")
    public Result<?> createIndex() {
        boolean success = esPhoneInfoMapper.createIndex();
        return success ? Result.ok("索引创建成功") : Result.fail("索引创建失败");
    }

    /**
     * 删除索引
     *
     * @return 操作结果
     */
    @PostMapping("/deleteIndex")
    public Result<?> deleteIndex() {
        boolean success = esPhoneInfoMapper.deleteIndex();
        return success ? Result.ok("索引删除成功") : Result.fail("索引删除失败");
    }

}
