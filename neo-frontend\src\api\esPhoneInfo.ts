import { request } from '@/utils/request';
import { EsPhoneInfo, EsPhoneInfoListResult, EsPhoneInfoPageParams, EsPhoneInfoPageResult } from './model/esPhoneInfoModel';

const Api = {
  EsPhoneInfoList: '/example/es/phoneInfo/list',
  EsPhoneInfoPage: '/example/es/phoneInfo/page',
  EsPhoneInfoAdd: '/example/es/phoneInfo/add',
  EsPhoneInfoBatchAdd: '/example/es/phoneInfo/batchAdd',
  EsPhoneInfoUpdate: '/example/es/phoneInfo/update',
  EsPhoneInfoDelete: '/example/es/phoneInfo',
  EsPhoneInfoGet: '/example/es/phoneInfo/',
  EsCreateIndex: '/example/es/phoneInfo/createIndex',
  EsDeleteIndex: '/example/es/phoneInfo/deleteIndex',
};

/**
 * 获取ES手机信息列表
 * @param params 查询参数
 */
export function getEsPhoneInfoList(params: EsPhoneInfo) {
  return request.post<EsPhoneInfoListResult>({
    url: Api.EsPhoneInfoList,
    data: params,
  });
}

/**
 * 分页查询ES手机信息
 * @param params 分页查询参数
 */
export function getEsPhoneInfoPage(params: EsPhoneInfoPageParams) {
  return request.post<EsPhoneInfoPageResult>({
    url: Api.EsPhoneInfoPage,
    data: params,
  });
}

/**
 * 根据ID获取ES手机信息
 * @param id 手机信息ID
 */
export function getEsPhoneInfoById(id: string) {
  return request.get<EsPhoneInfo>({
    url: `${Api.EsPhoneInfoGet}${id}`,
  });
}

/**
 * 新增ES手机信息
 * @param data 手机信息数据
 */
export function addEsPhoneInfo(data: EsPhoneInfo) {
  return request.post({
    url: Api.EsPhoneInfoAdd,
    data,
  });
}

/**
 * 批量新增ES手机信息
 * @param data 手机信息数据数组
 */
export function batchAddEsPhoneInfo(data: EsPhoneInfo[]) {
  return request.post({
    url: Api.EsPhoneInfoBatchAdd,
    data,
  });
}

/**
 * 更新ES手机信息
 * @param data 手机信息数据
 */
export function updateEsPhoneInfo(data: EsPhoneInfo) {
  return request.put({
    url: Api.EsPhoneInfoUpdate,
    data,
  });
}

/**
 * 删除ES手机信息
 * @param ids 手机信息ID数组
 */
export function deleteEsPhoneInfo(ids: string[]) {
  return request.delete({
    url: Api.EsPhoneInfoDelete,
    data: new Set(ids),
  });
}

/**
 * 创建ES索引
 */
export function createEsIndex() {
  return request.post({
    url: Api.EsCreateIndex,
  });
}

/**
 * 删除ES索引
 */
export function deleteEsIndex() {
  return request.post({
    url: Api.EsDeleteIndex,
  });
}
