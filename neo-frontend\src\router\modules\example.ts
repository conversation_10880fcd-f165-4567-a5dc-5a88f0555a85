import Layout from '@/layouts/index.vue';

export default [
  {
    path: '/example',
    name: 'example',
    component: Layout,
    redirect: '/example/db',
    meta: { title: { zh_CN: '示例页面', en_US: 'Example Pages' }, icon: 'layers' },
    children: [
      {
        path: 'db',
        name: 'ExampleDb',
        component: () => import('@/pages/example/db/index.vue'),
        meta: { title: { zh_CN: '手机信息管理', en_US: 'Phone Info Management' } },
      },
      {
        path: 'es',
        name: 'ExampleEs',
        component: () => import('@/pages/example/es/index.vue'),
        meta: { title: { zh_CN: 'ES搜索示例', en_US: 'ES Search Example' } },
      },
    ],
  },
];
