<template>
  <t-dialog 
    v-model:visible="dialogVisible" 
    :header="dialogTitle" 
    :width="600" 
    :confirm-btn="{ loading: formLoading }" 
    @confirm="onConfirm" 
    @cancel="onCancel"
  >
    <template v-if="dialogVisible">
      <t-form ref="form" :data="formData" :rules="rules" :label-width="80" @submit="onSubmit">
        <t-form-item label="岗位编码" name="code">
          <t-input 
            v-model="formData.code" 
            :disabled="isEdit"
            placeholder="请输入岗位编码"
          />
        </t-form-item>
        
        <t-form-item label="岗位名称" name="name">
          <t-input 
            v-model="formData.name" 
            placeholder="请输入岗位名称"
          />
        </t-form-item>
        
        <t-form-item label="排序" name="sort">
          <t-input-number 
            v-model="formData.sort" 
            placeholder="请输入排序号"
            :min="0"
            :max="9999"
          />
        </t-form-item>
        
        <t-form-item label="状态" name="status">
          <t-switch 
            v-model="formData.status" 
            :custom-value="['1', '0']" 
            checked="1" 
            unchecked="0"
            size="small"
          />
        </t-form-item>
      </t-form>
    </template>
  </t-dialog>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { addPost, updatePost } from '@/api/post';
import { SysPost } from '@/api/model/postModel';

// 定义 props
interface Props {
  visible: boolean;
  isEdit: boolean;
  postData?: SysPost;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  isEdit: false,
  postData: undefined
});

// 定义 emits
const emit = defineEmits<{
  'update:visible': [value: boolean];
  'success': [];
}>();

// 响应式数据
const formLoading = ref(false);
const form = ref();

const formData = ref({
  id: '',
  code: '',
  name: '',
  sort: 0,
  status: '1',
});

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => emit('update:visible', value)
});

const dialogTitle = computed(() => {
  return props.isEdit ? '编辑岗位' : '新增岗位';
});

const rules = computed(() => ({
  code: [
    { required: true, message: '请输入岗位编码' }
  ],
  name: [
    { required: true, message: '请输入岗位名称' }
  ],
  sort: [
    { required: true, message: '请输入排序号' }
  ],
}));

// 初始化表单数据
const initFormData = () => {
  formData.value = {
    id: '',
    code: '',
    name: '',
    sort: 0,
    status: '1',
  };
};

// 加载岗位详情数据
const loadPostDetail = () => {
  if (props.postData) {
    // 使用父组件传递的岗位数据
    formData.value = { 
      id: props.postData.id || '',
      code: props.postData.code || '',
      name: props.postData.name || '',
      sort: props.postData.sort || 0,
      status: props.postData.status || '1'
    };
  }
};

// 监听弹窗显示状态
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    initFormData();
    
    if (props.isEdit && props.postData) {
      // 编辑模式，使用父组件传递的岗位数据
      loadPostDetail();
    }
  }
});

// 确认按钮处理
const onConfirm = () => {
  form.value.submit();
};

// 取消按钮处理
const onCancel = () => {
  emit('update:visible', false);
};

// 表单提交处理
const onSubmit = ({ validateResult, firstError }: any) => {
  if (validateResult === true) {
    formLoading.value = true;
    const data = { ...formData.value };

    const promise = props.isEdit ? updatePost(data) : addPost(data);

    promise.then(() => {
      MessagePlugin.success(props.isEdit ? '更新成功' : '添加成功');
      emit('update:visible', false);
      emit('success');
    }).catch((e) => {
      MessagePlugin.error(e.message || (props.isEdit ? '更新失败' : '添加失败'));
    }).finally(() => {
      formLoading.value = false;
    });
  } else {
    console.log('Errors: ', validateResult);
    MessagePlugin.warning(firstError);
  }
};
</script>

<style lang="less" scoped>
// 弹窗内容样式优化
:deep(.t-dialog__body) {
  max-height: 400px;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 16px 24px;
}

:deep(.t-dialog) {
  .t-form {
    overflow: visible;
    width: 100%;
    box-sizing: border-box;
  }
  
  // 确保表单项不会超出宽度
  .t-form-item {
    margin-bottom: 16px;
  }
  
  // 输入框宽度限制
  .t-input,
  .t-input-number,
  .t-switch {
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
  }
}
</style>