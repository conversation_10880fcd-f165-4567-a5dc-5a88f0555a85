package com.neo.module.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.constant.CommonConstant;
import com.neo.module.entity.SysMenu;
import com.neo.module.entity.vo.SysMenuTree;
import com.neo.module.mapper.SysMenuMapper;
import com.neo.module.service.SysMenuService;
import com.neo.utils.TreeUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 菜单权限表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-28
 */
@Service
public class SysMenuServiceImpl extends ServiceImpl<SysMenuMapper, SysMenu> implements SysMenuService {

    @Resource
    private SysMenuMapper sysMenuMapper;

    @Override
    public List<SysMenu> list(SysMenu sysMenu) {
        QueryWrapper<SysMenu> queryWrapper = new QueryWrapper<>();
        if (sysMenu != null) {
            if (StringUtils.isNotBlank(sysMenu.getName())) {
                queryWrapper.lambda().like(SysMenu::getName, sysMenu.getName());
            }
            if (StringUtils.isNotBlank(sysMenu.getStatus())) {
                queryWrapper.lambda().eq(SysMenu::getStatus, sysMenu.getStatus());
            }
        }
        queryWrapper.lambda().orderByAsc(SysMenu::getSort);
        return this.list(queryWrapper);
    }

    @Override
    public List<SysMenuTree> listTree(SysMenu sysMenu) {
        List<SysMenu> list = list(sysMenu);
        return TreeUtils.getInstance().convertTree(list,SysMenuTree.class,"0");
    }

    @Override
    public List<SysMenuTree> getMenuByUserId(String userId) {
        if (CommonConstant.ADMIN_USER_ID.equals(userId)){
            return getAllMenuList();
        }
        List<SysMenu> list = sysMenuMapper.selectMenusByUserId(userId);
        return TreeUtils.getInstance().convertTree(list,SysMenuTree.class,"0");
    }

    @Override
    public List<SysMenuTree> getAllMenuList() {
        QueryWrapper<SysMenu> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().orderByAsc(SysMenu::getSort);
        queryWrapper.lambda().in(SysMenu::getMenuType, "M", "C");
        queryWrapper.lambda().eq(SysMenu::getStatus, "1"); // 只过滤停用状态，不过滤隐藏状态
        List<SysMenu> list = this.list(queryWrapper);
        return TreeUtils.getInstance().convertTree(list,SysMenuTree.class,"0");
    }

    @Override
    public List<String> getPermissionsByUserId(String id) {
        return sysMenuMapper.selectPermissionsByUserId(id);
    }

}