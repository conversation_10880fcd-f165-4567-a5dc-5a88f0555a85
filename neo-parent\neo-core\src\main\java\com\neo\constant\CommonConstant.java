package com.neo.constant;

public class CommonConstant {

    /**
     * 成功
     */
    public static final Integer SUCCESS_CODE = 200;

    /**
     * 失败
     */
    public static final Integer ERROR_CODE = 500;

    public static final CharSequence HTTP = "http";

    public static final CharSequence HTTPS = "https";

    public static final String UTF8 = "UTF-8";

    /**
     * 操作日志
     */
    public static final String LOG_TYPE_OPER = "0";
    /**
     * 登录日志
     */
    public static final String LOG_TYPE_LOGIN = "1";

    /**
     * 操作日志类型：查询
     */
    public static final String OPERATE_TYPE_QUERY = "1";

    /**
     * 操作日志类型：新增
     */
    public static final String OPERATE_TYPE_ADD = "2";

    /**
     * 操作日志类型：修改
     */
    public static final String OPERATE_TYPE_UPDATE = "3";

    /**
     * 操作日志类型：删除
     */
    public static final String OPERATE_TYPE_DELETE = "4";

    /**
     * 操作日志类型：导入
     */
    public static final String OPERATE_TYPE_IMPORT = "5";

    /**
     * 操作日志类型：导出
     */
    public static final String OPERATE_TYPE_EXPORT = "6";

    /**
     * 成功
     */
    public static final String SUCCESS = "1";
    /**
     * 失败
     */
    public static final String FAIL = "0";
    /**
     * 正常
     */
    public static final String NORMAL = "1";
    /**
     * 禁用
     */
    public static final String DISABLE = "0";

    /**
     * 是
     */
    public static final String YES = "1";

    /**
     * 否
     */
    public static final String NO = "0";

    /**
     * 日志topic
     */
    public static final String MQ_LOG_TOPIC = "logTopic";

    /**
     * 字典翻译key后缀
     */
    public static final String DICT_SUFFIX = "_dictText";

    /**
     * 管理员用户id
     */
    public static final String ADMIN_USER_ID = "10000";

}
