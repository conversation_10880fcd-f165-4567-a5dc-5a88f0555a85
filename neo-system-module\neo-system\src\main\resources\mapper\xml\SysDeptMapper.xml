<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.neo.module.mapper.SysDeptMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.neo.module.entity.SysDept">

        <result column="parent_id" property="parentId" />
        <result column="ancestors" property="ancestors" />
        <result column="name" property="name" />
        <result column="sort" property="sort" />
        <result column="leader" property="leader" />
        <result column="phone" property="phone" />
        <result column="email" property="email" />
        <result column="status" property="status" />
        <result column="id" property="id" />
        <result column="is_deleted" property="deleted" />
        <result column="create_time" property="createTime" />
        <result column="create_by" property="createBy" />
        <result column="update_time" property="updateTime" />
        <result column="update_by" property="updateBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
id,is_deleted,create_time,create_by,update_time,update_by,parent_id, ancestors, name, sort, leader, phone, email, status
    </sql>

</mapper>
