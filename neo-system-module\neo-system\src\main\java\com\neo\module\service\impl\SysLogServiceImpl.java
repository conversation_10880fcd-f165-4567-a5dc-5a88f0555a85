package com.neo.module.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.common.QueryGenerator;
import com.neo.module.entity.SysLog;
import com.neo.module.mapper.SysLogMapper;
import com.neo.module.service.SysLogService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 系统日志 服务实现类
 *
 * <AUTHOR>
 * @since 2023-05-31
 */
@Service
public class SysLogServiceImpl extends ServiceImpl<SysLogMapper, SysLog> implements SysLogService {

    @Resource
    private SysLogMapper sysLogMapper;

    @Override
    public IPage<SysLog> page(Map<String, String> params, QueryWrapper<SysLog> queryWrapper) {
        String value;
        queryWrapper
                .eq(StringUtils.isNotBlank((value = params.get("userId"))), "user_id", value)
                .eq(StringUtils.isNotBlank((value = params.get("username"))), "username", value)
                .eq(StringUtils.isNotBlank((value = params.get("businessId"))), "business_id", value)
                .eq(StringUtils.isNotBlank((value = params.get("businessModule"))), "business_module", value)
                .eq(StringUtils.isNotBlank((value = params.get("operateType"))), "operate_type", value)
                .eq(StringUtils.isNotBlank((value = params.get("operateName"))), "operate_name", value)
                .eq(StringUtils.isNotBlank((value = params.get("methodName"))), "method_name", value)
                .eq(StringUtils.isNotBlank((value = params.get("requestUrl"))), "request_url", value)
                .eq(StringUtils.isNotBlank((value = params.get("requestParam"))), "request_param", value)
                .eq(StringUtils.isNotBlank((value = params.get("requestMethod"))), "request_method", value)
                .eq(StringUtils.isNotBlank((value = params.get("resultJson"))), "result_json", value)
                .eq(StringUtils.isNotBlank((value = params.get("ipAddress"))), "ip_address", value)
                .eq(StringUtils.isNotBlank((value = params.get("costTime"))), "cost_time", value)
        ;
        IPage<SysLog> page = sysLogMapper.selectPage(QueryGenerator.initPage(params), queryWrapper);
        return page;
    }

    @Override
    public List<SysLog> list(SysLog sysLog) {
        QueryWrapper<SysLog> queryWrapper = new QueryWrapper<>(sysLog);
        List<SysLog> sysLogs = sysLogMapper.selectList(queryWrapper);
        return sysLogs;
    }

}
