{"compilerOptions": {"target": "esnext", "module": "esnext", "moduleResolution": "node", "jsx": "preserve", "jsxImportSource": "vue", "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "skipLibCheck": true, "allowSyntheticDefaultImports": true, "lib": ["esnext", "dom"], "types": ["vite/client"], "noEmit": true, "baseUrl": "./", "paths": {"@/*": ["src/*"]}, "noImplicitAny": true, "strictFunctionTypes": true, "strictBindCallApply": true, "noImplicitThis": true, "alwaysStrict": true}, "include": ["**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "node_modules/tdesign-vue-next/global.d.ts"], "compileOnSave": false}