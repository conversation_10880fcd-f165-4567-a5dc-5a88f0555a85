spring:
  main:
    web-application-type: reactive
  cloud:
    # 网关配置
    gateway:
      # 打印请求日志(自定义)
      requestLog: true
      discovery:
        locator:
          lowerCaseServiceId: true
          enabled: true
      routes:
        # 认证中心
        - id: neo-auth
          uri: lb://neo-auth
          predicates:
            - Path=/auth/**
          filters:
            - StripPrefix=1
        # 系统模块
        - id: neo-system
          uri: lb://neo-system
          predicates:
            - Path=/system/**
          filters:
            - StripPrefix=1

# 安全配置
security:
  # 不校验白名单
  ignore:
    whites:
      - /code
      - /auth/logout
      - /auth/login