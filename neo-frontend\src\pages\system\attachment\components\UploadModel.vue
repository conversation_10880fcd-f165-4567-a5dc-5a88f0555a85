<template>
  <t-dialog
    v-model:visible="dialogVisible"
    header="文件上传"
    width="600px"
    :confirm-btn="null"
    :cancel-btn="null"
    @close="handleClose"
  >
    <div class="upload-container">
      <t-upload
        ref="uploadRef"
        v-model="files"
        :action="uploadAction"
        :headers="uploadHeaders"
        :data="uploadData"
        :before-upload="beforeUpload"
        :on-success="onUploadSuccess"
        :on-fail="onUploadFail"
        :on-remove="onRemove"
        :on-progress="onProgress"
        multiple
        :max="10"
        :size-limit="100 * 1024 * 1024"
        :format="allowedFormats"
        theme="file-flow"
        tips="支持批量上传，单个文件大小不超过100MB"
        placeholder="点击上传文件或将文件拖拽到此区域"
      >
        <template #file-list-display="{ files }">
          <div class="file-list">
            <div
              v-for="(file, index) in files"
              :key="index"
              class="file-item"
            >
              <div class="file-info">
                <t-icon :name="getFileIcon(file.name)" size="24px" />
                <div class="file-details">
                  <div class="file-name">{{ file.name }}</div>
                  <div class="file-size">{{ formatFileSize(file.size) }}</div>
                </div>
              </div>
              
              <div class="file-status">
                <div v-if="file.status === 'progress'" class="progress-container">
                  <t-progress
                    :percentage="file.percent || 0"
                    size="small"
                    :show-info="false"
                  />
                  <span class="progress-text">{{ file.percent || 0 }}%</span>
                </div>
                
                <t-tag v-else-if="file.status === 'success'" theme="success" variant="light">
                  上传成功
                </t-tag>
                
                <t-tag v-else-if="file.status === 'fail'" theme="danger" variant="light">
                  上传失败
                </t-tag>
                
                <t-tag v-else theme="default" variant="light">
                  等待上传
                </t-tag>
              </div>
              
              <div class="file-actions">
                <t-button
                  theme="default"
                  variant="text"
                  size="small"
                  @click="removeFile(index)"
                >
                  删除
                </t-button>
              </div>
            </div>
          </div>
        </template>
      </t-upload>
    </div>

    <template #footer>
      <t-space>
        <t-button variant="outline" @click="handleClose">取消</t-button>
        <t-button 
          theme="primary" 
          @click="handleUpload"
          :loading="uploading"
          :disabled="files.length === 0"
        >
          开始上传
        </t-button>
      </t-space>
    </template>
  </t-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import type { UploadFile, UploadInstanceFunctions } from 'tdesign-vue-next';
import { uploadFile } from '@/api/attachment';
import { formatFileSize, getFileIcon } from '@/utils/file';

interface Props {
  visible: boolean;
}

interface Emits {
  (e: 'update:visible', visible: boolean): void;
  (e: 'success'): void;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
});

const emit = defineEmits<Emits>();

// 上传组件引用
const uploadRef = ref<UploadInstanceFunctions>();

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => {
    emit('update:visible', value);
  },
});

// 文件列表
const files = ref<UploadFile[]>([]);

// 上传状态
const uploading = ref(false);

// 上传配置
const uploadAction = '/sysAttachment/upload';
const uploadHeaders = {};
const uploadData = {};

// 允许的文件格式
const allowedFormats = [
  'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp',
  'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx',
  'txt', 'zip', 'rar', '7z',
  'mp4', 'avi', 'mov', 'wmv',
  'mp3', 'wav', 'flac'
];

// 上传前检查
const beforeUpload = (file: UploadFile) => {
  // 检查文件大小
  if (file.size && file.size > 100 * 1024 * 1024) {
    MessagePlugin.error('文件大小不能超过100MB');
    return false;
  }
  
  // 检查文件格式
  const fileExtension = file.name?.split('.').pop()?.toLowerCase();
  if (fileExtension && !allowedFormats.includes(fileExtension)) {
    MessagePlugin.error('不支持的文件格式');
    return false;
  }
  
  return true;
};

// 上传成功
const onUploadSuccess = (context: any) => {
  MessagePlugin.success(`${context.file.name} 上传成功`);
  // 更新文件状态
  const fileIndex = files.value.findIndex(f => f.name === context.file.name);
  if (fileIndex !== -1) {
    files.value[fileIndex].status = 'success';
  }
};

// 上传失败
const onUploadFail = (context: any) => {
  MessagePlugin.error(`${context.file.name} 上传失败`);
  // 更新文件状态
  const fileIndex = files.value.findIndex(f => f.name === context.file.name);
  if (fileIndex !== -1) {
    files.value[fileIndex].status = 'fail';
  }
};

// 上传进度
const onProgress = (context: any) => {
  const fileIndex = files.value.findIndex(f => f.name === context.file.name);
  if (fileIndex !== -1) {
    files.value[fileIndex].percent = context.percent;
    files.value[fileIndex].status = 'progress';
  }
};

// 移除文件
const onRemove = (context: any) => {
  const fileIndex = files.value.findIndex(f => f.name === context.file.name);
  if (fileIndex !== -1) {
    files.value.splice(fileIndex, 1);
  }
};

// 手动移除文件
const removeFile = (index: number) => {
  files.value.splice(index, 1);
};

// 开始上传
const handleUpload = async () => {
  if (files.value.length === 0) {
    MessagePlugin.warning('请选择要上传的文件');
    return;
  }
  
  uploading.value = true;
  
  try {
    // 使用上传组件的上传方法
    uploadRef.value?.uploadFiles();
    
    // 等待所有文件上传完成
    const checkUploadComplete = () => {
      const allCompleted = files.value.every(file => 
        file.status === 'success' || file.status === 'fail'
      );
      
      if (allCompleted) {
        uploading.value = false;
        const successCount = files.value.filter(file => file.status === 'success').length;
        const failCount = files.value.filter(file => file.status === 'fail').length;
        
        if (successCount > 0) {
          MessagePlugin.success(`成功上传 ${successCount} 个文件${failCount > 0 ? `，失败 ${failCount} 个` : ''}`);
          emit('success');
        }
        
        if (successCount === files.value.length) {
          // 全部成功，关闭对话框
          setTimeout(() => {
            handleClose();
          }, 1000);
        }
      } else {
        // 继续检查
        setTimeout(checkUploadComplete, 500);
      }
    };
    
    checkUploadComplete();
  } catch (error) {
    uploading.value = false;
    MessagePlugin.error('上传失败');
  }
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  files.value = [];
  uploading.value = false;
};


</script>

<style scoped>
.upload-container {
  margin: 20px 0;
}

.file-list {
  margin-top: 16px;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border: 1px solid var(--td-border-level-1-color);
  border-radius: var(--td-radius-default);
  margin-bottom: 8px;
  background-color: var(--td-bg-color-container);
}

.file-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.file-details {
  margin-left: 12px;
}

.file-name {
  font-weight: 500;
  color: var(--td-text-color-primary);
  margin-bottom: 4px;
}

.file-size {
  font-size: 12px;
  color: var(--td-text-color-placeholder);
}

.file-status {
  margin: 0 16px;
  min-width: 100px;
}

.progress-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-text {
  font-size: 12px;
  color: var(--td-text-color-secondary);
  min-width: 30px;
}

.file-actions {
  min-width: 60px;
  text-align: right;
}
</style>
