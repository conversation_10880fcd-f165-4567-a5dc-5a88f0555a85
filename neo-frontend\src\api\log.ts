import { request } from '@/utils/request';
import { SysLog, LogListResult, LogPageParams } from './model/logModel';

enum Api {
  LogList = '/system/sysLog/page',
  LogInfo = '/system/sysLog',
}

/**
 * 分页查询日志列表
 * @param params
 */
export const getLogPage = (params: LogPageParams) => {
  return request.post<LogListResult>({
    url: Api.LogList,
    params,
  });
};

/**
 * 获取日志详情
 * @param id
 */
export const getLogInfo = (id: string) => {
  return request.get<SysLog>({
    url: `${Api.LogInfo}/${id}`,
  });
};