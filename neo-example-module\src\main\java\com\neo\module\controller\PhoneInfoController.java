package com.neo.module.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.neo.model.Result;
import com.neo.common.QueryGenerator;
import com.neo.module.entity.PhoneInfo;
import com.neo.module.service.PhoneInfoService;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.*;

import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 手机信息表
 *
 * <AUTHOR>
 * @since 2025-08-22
 */
@RestController
@RequestMapping("/neoPhoneInfo")
public class PhoneInfoController {
    @Resource
    private PhoneInfoService phoneInfoService;

    /**
     * 分页查询
     *
     * @param params
     * @return
     */
    @PostMapping("/page")
    public Result<?> page(@RequestBody Map<String, String> params) {
        QueryWrapper<PhoneInfo> queryWrapper = QueryGenerator.initQueryWrapper(PhoneInfo.class, params);
        return Result.ok(phoneInfoService.page(params, queryWrapper));
    }

    /**
     * 查询
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public Result<?> get(@PathVariable String id) {
        PhoneInfo phoneInfo = phoneInfoService.getById(id);
        return Result.ok(phoneInfo);
    }

    /**
     * 列表
     *
     * @param phoneInfo
     * @return
     */
    @PostMapping("/list")
    public Result<?> list(@RequestBody PhoneInfo phoneInfo) {
        List<PhoneInfo> list = phoneInfoService.list(phoneInfo);
        return Result.ok(list);
    }

    /**
     * 新增
     *
     * @param phoneInfo
     * @return
     */
    @PostMapping("/add")
    public Result<?> add(@RequestBody PhoneInfo phoneInfo) {
        phoneInfoService.save(phoneInfo);
        return Result.ok();
    }

    /**
     * 删除
     *
     * @param ids
     * @return
     */
    @DeleteMapping
    public Result<?> delete(@RequestBody Set<String> ids) {
        phoneInfoService.removeByIds(ids);
        return Result.ok();
    }

    /**
     * 更新
     *
     * @param phoneInfo
     * @return
     */
    @PutMapping("/update")
    public Result<?> update(@RequestBody PhoneInfo phoneInfo) {
        phoneInfoService.updateById(phoneInfo);
        return Result.ok();
    }

}

