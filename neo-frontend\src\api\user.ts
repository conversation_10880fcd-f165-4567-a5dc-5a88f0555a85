import type { SysUser, UserListR<PERSON>ult, UserPageResult } from '@/api/model/userModel';
import { request } from '@/utils/request';

const Api = {
  UserList: '/system/sysUser/list',
  UserPage: '/system/sysUser/page',
  UserAdd: '/system/sysUser/add',
  UserUpdate: '/system/sysUser/update',
  UserDelete: '/system/sysUser',
  UserGet: '/system/sysUser/',
  UserResetPassword: '/system/sysUser/resetPassword',
};

export function getUserList(params: SysUser) {
  return request.post<UserListResult>({
    url: Api.UserList,
    data: params,
  });
}

export function getUserPage(params: any) {
  return request.post<UserPageResult>({
    url: Api.UserPage,
    data: params,
  });
}

export function addUser(data: SysUser) {
  return request.post({
    url: Api.UserAdd,
    data,
  });
}

export function updateUser(data: SysUser) {
  return request.put({
    url: Api.UserUpdate,
    data,
  });
}

export function deleteUser(ids: string[]) {
  return request.delete({
    url: Api.UserDelete,
    data: ids,
  });
}

export function getUserById(id: string) {
  return request.get<SysUser>({
    url: `${Api.UserGet}${id}`,
  });
}

export function resetPassword(data: SysUser) {
  return request.put({
    url: Api.UserResetPassword,
    data,
  });
}