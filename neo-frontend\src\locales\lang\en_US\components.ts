export default {
  isSetup: {
    on: 'Enabled',
    off: 'Disabled',
  },
  manage: 'Manage',
  delete: 'Delete',
  commonTable: {
    contractName: 'Name',
    contractStatus: 'Status',
    contractNum: 'Number',
    contractType: 'Type',
    contractPayType: 'Pay Type',
    contractAmount: 'Amount',
    contractNamePlaceholder: 'enter contract name',
    contractStatusPlaceholder: 'enter contract status',
    contractNumPlaceholder: 'enter contract number',
    contractTypePlaceholder: 'enter contract type',
    operation: 'Operation',
    detail: 'detail',
    delete: 'delete',
    contractStatusEnum: {
      fail: 'fail',
      audit: 'audit',
      executing: 'executing',
      pending: 'pending',
      finish: 'finish',
    },
    contractTypeEnum: {
      main: 'main',
      sub: 'sub',
      supplement: 'supplement',
    },
    reset: 'reset',
    query: 'query',
  },
};
