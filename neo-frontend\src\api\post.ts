import { request } from '@/utils/request';
import { SysPost, PostListResult, PostPageParams } from './model/postModel';

enum Api {
  PostList = '/system/sysPost/page',
  PostInfo = '/system/sysPost',
  AddPost = '/system/sysPost/add',
  UpdatePost = '/system/sysPost/update',
  DeletePost = '/system/sysPost',
  PostSelectList = '/system/sysPost/list',
}

/**
 * 分页查询岗位列表
 * @param params
 */
export const getPostPage = (params: PostPageParams) => {
  return request.post<PostListResult>({
    url: Api.PostList,
    data: params,
  });
};

/**
 * 获取岗位详情
 * @param id
 */
export const getPostInfo = (id: string) => {
  return request.get<SysPost>({
    url: `${Api.PostInfo}/${id}`,
  });
};

/**
 * 添加岗位
 * @param params
 */
export const addPost = (params: SysPost) => {
  return request.post({
    url: Api.AddPost,
    data: params,
  });
};

/**
 * 更新岗位
 * @param params
 */
export const updatePost = (params: SysPost) => {
  return request.put({
    url: Api.UpdatePost,
    data: params,
  });
};

/**
 * 删除岗位
 * @param ids
 */
export const deletePost = (ids: string[]) => {
  return request.delete({
    url: Api.DeletePost,
    data: ids,
  });
};

/**
 * 获取岗位选择列表
 */
export const getPostSelectList = () => {
  return request.post<SysPost[]>({
    url: Api.PostSelectList,
    data: {},
  });
};