package com.neo.module.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.neo.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 登录日志
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@Accessors(chain = true)
@TableName("sys_login_log")
public class SysLoginLog extends BaseEntity {

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 用户账号
     */
    @TableField("username")
    private String username;

    /**
     * IP地址
     */
    @TableField("ip_address")
    private String ipAddress;

    /**
     * 浏览器
     */
    @TableField("browser")
    private String browser;

    /**
     * 浏览器版本
     */
    @TableField("browser_version")
    private String browserVersion;

    /**
     * 内核
     */
    @TableField("engine")
    private String engine;

    /**
     * 内核版本
     */
    @TableField("engine_version")
    private String engineVersion;

    /**
     * 操作系统
     */
    @TableField("os")
    private String os;

    /**
     * 操作系统名称
     */
    @TableField("os_name")
    private String osName;

    /**
     * 是否为移动终端
     */
    @TableField("is_mobile")
    private String isMobile;

    /**
     * 状态
     */
    @TableField("status")
    private String status;

    /**
     * 提示消息
     */
    @TableField("message")
    private String message;


}
