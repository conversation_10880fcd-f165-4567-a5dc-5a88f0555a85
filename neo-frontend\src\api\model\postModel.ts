export interface SysPost {
  id?: string;
  code?: string;  // 岗位代码
  name?: string;  // 岗位名称
  sort?: number;  // 排序
  status?: string; // 状态
  createTime?: string;
  updateTime?: string;
  createBy?: string;
  updateBy?: string;
}

export interface PostPageParams {
  current: number;
  pageSize: number;
  code?: string;
  name?: string;
  status?: string;
}

export interface PostListResult {
  records: SysPost[];
  total: number;
  current: number;
  size: number;
}