package com.neo.aspect;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.neo.aspect.annontation.Log;
import com.neo.constant.CommonConstant;
import com.neo.model.LogModel;
import com.neo.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Aspect
@Component
public class LogAspect {



    /**
     * 切入点
     */
    @Pointcut("@annotation(com.neo.aspect.annontation.Log)")
    public void logPointCut() {

    }

    @Around("logPointCut()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        Object result = null;
        try {
            long beginTime = System.currentTimeMillis();
            // 执行方法
            result = point.proceed();
            // 执行时长(毫秒)
            long time = System.currentTimeMillis() - beginTime;
            // 保存日志，异步执行
            saveSysLog(point, time, result);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return result;
    }

    private void saveSysLog(ProceedingJoinPoint joinPoint, long time, Object result) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        LogModel logModel = new LogModel();
        Log log = method.getAnnotation(Log.class);
        if (log != null) {
            //日志类型
            logModel.setOperateType(log.logType());
            //模块名称 用”-“进行分割
            String[] values = log.value().split("-");
            //模块名称
            if (StringUtils.isNotBlank(log.businessModule())) {
                logModel.setBusinessModule(log.businessModule());
            } else {
                logModel.setBusinessModule(values[0]);
            }
            //操作名称
            if (values.length > 1) {
                logModel.setOperateName(values[1]);
            } else if (values.length == 1) {
                logModel.setOperateName(values[1]);
            }
        } else {
            return;
        }
        // 获取参数转成map
        Map<String, Object> paramMap = getParameters(joinPoint);
        // 请求的方法名
        String className = joinPoint.getTarget().getClass().getName();
        String methodName = signature.getName();
        logModel.setMethodName(className + "." + methodName + "()");
        logModel.setBusinessModule(log.value());
        // 设置操作类型
        if (log.logType().equals(CommonConstant.LOG_TYPE_OPER)) {
            logModel.setOperateType(getOperateType(methodName, log.operateType()).toString());
        }else{
            logModel.setOperateType(log.operateType());
        }
        //responseParam
        String responseParamStr = null;
        try {
            ObjectMapper mapper = new ObjectMapper();
            responseParamStr = mapper.writeValueAsString(result);
            logModel.setResultJson(responseParamStr);
        } catch (Exception e) {
            LogAspect.log.error(e.getMessage(), e);
        }
        Object paramObj = null;
        //businessId
        String businessId = null;
        Object[] args = joinPoint.getArgs();
        Object param = args.length > 0 ? joinPoint.getArgs()[0] : new Object();

        paramObj = getJsonByObject(param);

        if (paramObj instanceof JSONObject) {
            businessId = ((JSONObject) paramObj).getString("id");
            if (StrUtil.isBlank(businessId)) {
                businessId = ((JSONObject) paramObj).getString("businessId");
            }
        }
        saveSysLog(time, method, logModel, paramMap, responseParamStr, businessId);
    }

    private void saveSysLog(long time, Method method, LogModel logModel, Map<String, Object> paramMap, String responseParamStr, String businessId) {
        if (StrUtil.isBlank(businessId)) {
            try {
                JSONObject jsonObject = JSONObject.parseObject(responseParamStr);
                JSONObject data = jsonObject.getJSONObject("data");
                if (data != null){
                    businessId = data.getString("id");
                }
            } catch (Exception e) {
                log.error(e.getMessage(),e);
            }
        }
        logModel.setBusinessId(businessId);
        try {
            // 请求的参数
            String params = JSONObject.toJSONString(paramMap);
            logModel.setRequestParam(params);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        }
        // 获取request
        HttpServletRequest request = SpringContextUtils.getHttpServletRequest();
        logModel.setRequestUrl(request.getRequestURL().toString());
        // 设置IP地址
        logModel.setIpAddress(IPUtils.getIpAddr(request));
        // 获取登录用户信息
        String userId = SecurityUtils.getUserId();
        String username = SecurityUtils.getUsername();

        logModel.setUserId(userId);
        logModel.setUsername(username);

        // 耗时
        logModel.setCostTime(time);
        logModel.setRequestMethod(getRequestType(method));
        String jsonString = JSONObject.toJSONString(logModel);
        log.info("操作日志：{}",jsonString);
        SpringUtils.context().publishEvent(logModel);
    }

    private String getRequestType(Method targetMethod) {
        Annotation[] annotations = targetMethod.getAnnotations();
        String type = "";
        for (Annotation anno : annotations) {
            if(!anno.annotationType().getName().startsWith(RequestMapping.class.getPackage().getName())) {
                continue;
            }
            if(anno instanceof RequestMapping){
                RequestMethod[] method = ((RequestMapping) anno).method();
                type = Arrays.stream(method).map(Enum::name).collect(Collectors.joining(","));
            }
            else if (anno instanceof GetMapping) {
                type = "GET";
            } else if (anno instanceof PostMapping) {
                type = "POST";
            } else if (anno instanceof PutMapping) {
                type = "PUT";
            } else if (anno instanceof PatchMapping) {
                type = "PATCH";
            } else if (anno instanceof DeleteMapping) {
                type = "DELETE";
            }
            break;
        }
        return type;
    }

    /**
     * 获取操作类型
     *
     * @param methodName
     * @param operateType
     * @return
     */
    private Object getOperateType(String methodName, String operateType) {
//        if (!operateType.equals("0")) {
//            return operateType;
//        }
        if (methodName.startsWith("list")||methodName.startsWith("page")||methodName.startsWith("get")) {
            return CommonConstant.OPERATE_TYPE_QUERY;
        }
        if (methodName.startsWith("add")) {
            return CommonConstant.OPERATE_TYPE_ADD;
        }
        if (methodName.startsWith("update")) {
            return CommonConstant.OPERATE_TYPE_UPDATE;
        }
        if (methodName.startsWith("delete")) {
            return CommonConstant.OPERATE_TYPE_DELETE;
        }
        if (methodName.startsWith("import")) {
            return CommonConstant.OPERATE_TYPE_IMPORT;
        }
        if (methodName.startsWith("export")) {
            return CommonConstant.OPERATE_TYPE_EXPORT;
        }
        return CommonConstant.OPERATE_TYPE_QUERY;
    }

    private Object getJsonByObject(Object param) {
        ObjectMapper mapper = new ObjectMapper();
        String json = "{}";
        try {
            json = mapper.writeValueAsString(param);
        } catch (JsonProcessingException e) {
            log.error("请求入参序列化失败："+e.getMessage(), e);
        }
        return JSONObject.parse(json);
    }

    /**
     * 获取连接点上的方法入参
     *
     * @param point 连接点
     * @return 方法入参
     */
    @SuppressWarnings("rawtypes")
    public static Map getParameters(ProceedingJoinPoint point) {
        // 获取方法签名
        MethodSignature ms = (MethodSignature) point.getSignature();
        // 获取参数名称
        String[] parameterNames = ms.getParameterNames();

        // 获取参数
        Object[] args = point.getArgs();
        Map<String, Object> paramMap = new HashMap<>();
        for (int i = 0; i < parameterNames.length; i++) {
            paramMap.put(parameterNames[i], args[i]);
        }
        return paramMap;
    }

}
