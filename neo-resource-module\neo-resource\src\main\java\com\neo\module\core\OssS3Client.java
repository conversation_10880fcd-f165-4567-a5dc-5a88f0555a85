package com.neo.module.core;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.core.ResponseInputStream;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.net.URL;
import java.time.Duration;
import java.util.List;

@Slf4j
public class OssS3Client {

    private final S3Client s3Client;
    private final String bucketName;
    private final S3Presigner presigner;
    private final String endpoint;

    public OssS3Client(String endpoint, String accessKey, String secretKey, String bucket) {
        this.endpoint = endpoint;
        // 创建凭证
        AwsBasicCredentials credentials = AwsBasicCredentials.create(accessKey, secretKey);

        // 构建S3客户端
        this.s3Client = S3Client.builder()
                .endpointOverride(URI.create(endpoint))
                .credentialsProvider(StaticCredentialsProvider.create(credentials))
                .region(Region.US_EAST_1) // 可以是任意region，因为我们使用自定义endpoint
                .forcePathStyle(true) // 启用path-style访问
                .build();

        this.bucketName = bucket;

        this.presigner = S3Presigner.builder()
                .endpointOverride(URI.create(endpoint))
                .credentialsProvider(StaticCredentialsProvider.create(credentials))
                .region(Region.US_EAST_1)
                .build();
    }

    /**
     * 上传文件
     *
     * @param key  文件路径/名称
     * @param file 文件对象
     * @return 文件访问URL
     */
    public String uploadFile(String key, File file) {
        try {
            PutObjectRequest request = PutObjectRequest.builder()
                    .bucket(bucketName)
                    .key(key)
                    .build();

            s3Client.putObject(request, RequestBody.fromFile(file));
            return getFileUrl(key);
        } catch (Exception e) {
            log.error("Upload file failed", e);
            throw new RuntimeException("Upload file failed", e);
        }
    }

    public String uploadFile(String key, InputStream inputStream,long contentLength) {
        PutObjectRequest request = PutObjectRequest.builder()
                .bucket(bucketName)
                .key(key)
                .build();

        s3Client.putObject(request, RequestBody.fromInputStream(inputStream,contentLength));
        return getFileUrl(key);
    }

    /**
     * 下载文件
     *
     * @param key 文件路径/名称
     * @return 文件输入流
     */
    public InputStream downloadFile(String key) {
        try {
            GetObjectRequest request = GetObjectRequest.builder()
                    .bucket(bucketName)
                    .key(key)
                    .build();

            ResponseInputStream<GetObjectResponse> response = s3Client.getObject(request);
            return response;
        } catch (Exception e) {
            log.error("Download file failed", e);
            throw new RuntimeException("Download file failed", e);
        }
    }

    /**
     * 删除文件
     *
     * @param key 文件路径/名称
     */
    public void deleteFile(String key) {
        try {
            DeleteObjectRequest request = DeleteObjectRequest.builder()
                    .bucket(bucketName)
                    .key(key)
                    .build();

            s3Client.deleteObject(request);
        } catch (Exception e) {
            log.error("Delete file failed", e);
            throw new RuntimeException("Delete file failed", e);
        }
    }

    /**
     * 获取文件访问URL（带签名的临时访问URL）
     *
     * @param key 文件路径/名称
     * @return 带签名的临时访问URL
     */
    public String getSignedUrl(String key, Duration expiration) {
        GetObjectPresignRequest request = GetObjectPresignRequest.builder()
                .signatureDuration(expiration)
                .getObjectRequest(GetObjectRequest.builder()
                        .bucket(bucketName)
                        .key(key)
                        .build())
                .build();

        URL url = presigner.presignGetObject(request).url();
        return url.toString();
    }

    /**
     * 获取文件访问URL（不带签名的永久访问URL）
     *
     * @param key 文件路径/名称
     * @return 文件访问URL
     */
    public String getFileUrl(String key) {
        return String.format("%s/%s/%s", endpoint, bucketName, key);
    }

    /**
     * 列出目录下的文件
     *
     * @param prefix 目录前缀
     * @return 文件列表
     */
    public List<S3Object> listFiles(String prefix) {
        try {
            ListObjectsV2Request request = ListObjectsV2Request.builder()
                    .bucket(bucketName)
                    .prefix(prefix)
                    .build();

            ListObjectsV2Response response = s3Client.listObjectsV2(request);
            return response.contents();
        } catch (Exception e) {
            log.error("List files failed", e);
            throw new RuntimeException("List files failed", e);
        }
    }

    /**
     * 检查文件是否存在
     *
     * @param key 文件路径/名称
     * @return 是否存在
     */
    public boolean doesObjectExist(String key) {
        try {
            HeadObjectRequest request = HeadObjectRequest.builder()
                    .bucket(bucketName)
                    .key(key)
                    .build();

            s3Client.headObject(request);
            return true;
        } catch (NoSuchKeyException e) {
            return false;
        }
    }
}
