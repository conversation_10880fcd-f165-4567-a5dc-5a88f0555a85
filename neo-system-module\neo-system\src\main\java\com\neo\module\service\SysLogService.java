package com.neo.module.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.neo.module.entity.SysLog;

import java.util.List;
import java.util.Map;

/**
 * 系统日志 服务类
 *
 * <AUTHOR>
 * @since 2023-05-31
 */
public interface SysLogService extends IService<SysLog> {

    IPage<SysLog> page(Map<String, String> params, QueryWrapper<SysLog> queryWrapper);

    List<SysLog> list(SysLog sysLog);

}
