package com.neo.module.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.neo.module.entity.SysDictItem;

import java.util.List;
import java.util.Map;

/**
 * 数据字典项 服务类
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
public interface SysDictItemService extends IService<SysDictItem> {

    String selectByDictTable(String dictCode,String value);

    String selectByOtherTable(String dictCode, String dictText,String dictTable, String value);

    IPage<SysDictItem> page(Map<String, String> params, QueryWrapper<SysDictItem> queryWrapper);

    List<SysDictItem> list(SysDictItem sysDictItem);

}
