<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.neo.module.mapper.SysUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.neo.module.entity.SysUser">
        <result column="id" property="id" />
        <result column="user_name" property="userName" />
        <result column="nick_name" property="nickName" />
        <result column="password" property="password" />
        <result column="user_type" property="userType" />
        <result column="avatar" property="avatar" />
        <result column="dept_id" property="deptId" />
        <result column="email" property="email" />
        <result column="phone_number" property="phoneNumber" />
        <result column="gender" property="gender" />
        <result column="status" property="status" />
        <result column="is_deleted" property="deleted" />
        <result column="create_time" property="createTime" />
        <result column="create_by" property="createBy" />
        <result column="update_time" property="updateTime" />
        <result column="update_by" property="updateBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,update_by,username, nickname, password, user_type, avatar, dept_id, email, phone_number, gender, status,is_deleted,create_time,create_by,update_time
    </sql>

</mapper>
