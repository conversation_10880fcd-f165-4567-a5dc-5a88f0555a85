package com.neo.aspect.annontation;

import com.neo.constant.CommonConstant;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface Log {

    String value() default "";

    String businessModule() default "";

    /**
     * 日志类型
     * @return
     */
    String logType() default CommonConstant.LOG_TYPE_OPER;

    /**
     * 操作日志类型
     *
     * @return （1查询，2添加，3修改，4删除，5导入，6导出，7提交审批）
     */
    String operateType() default CommonConstant.OPERATE_TYPE_QUERY;

}
