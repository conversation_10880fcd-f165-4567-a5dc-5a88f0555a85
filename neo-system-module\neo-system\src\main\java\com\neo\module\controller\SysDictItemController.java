package com.neo.module.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.neo.aspect.annontation.PrePermissions;
import com.neo.model.Result;
import com.neo.common.QueryGenerator;
import com.neo.module.entity.SysDict;
import com.neo.module.entity.SysDictItem;
import com.neo.module.service.SysDictItemService;
import com.neo.module.service.SysDictService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 数据字典项
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
@RestController
@RequestMapping("/sysDictItem")
public class SysDictItemController {

    @Resource
    private SysDictService sysDictService;
    @Resource
    private SysDictItemService sysDictItemService;

    /**
     * 分页查询
     *
     * @param params
     * @return
     */
    @PostMapping("/page")
    public Result<?> page(@RequestBody Map<String, String> params) {
        QueryWrapper<SysDictItem> queryWrapper = QueryGenerator.initQueryWrapper(SysDictItem.class, params);
        return Result.ok(sysDictItemService.page(params, queryWrapper));
    }

    /**
     * 查询
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public Result<?> get(@PathVariable String id) {
        SysDictItem sysDictItem = sysDictItemService.getById(id);
        return Result.ok(sysDictItem);
    }

    /**
     * 列表
     *
     * @param sysDictItem
     * @return
     */
    @PostMapping("/list")
    public Result<?> list(@RequestBody SysDictItem sysDictItem) {
        List<SysDictItem> list = sysDictItemService.list(sysDictItem);
        return Result.ok(list);
    }

    /**
     * 新增
     *
     * @param sysDictItem
     * @return
     */
    @PrePermissions("system:sysDictItem:add")
    @PostMapping("/add")
    public Result<?> add(@RequestBody SysDictItem sysDictItem) {
        SysDict sysDict = sysDictService.getById(sysDictItem.getDictId());
        if (sysDict == null) {
            return Result.fail("字典不存在");
        }
        sysDictItem.setDictCode(sysDict.getDictCode());
        sysDictItemService.save(sysDictItem);
        return Result.ok();
    }

    /**
     * 删除
     *
     * @param ids
     * @return
     */
    @PrePermissions("system:sysDictItem:delete")
    @DeleteMapping
    public Result<?> delete(@RequestBody Set<String> ids) {
        sysDictItemService.removeByIds(ids);
        return Result.ok();
    }

    /**
     * 更新
     *
     * @param sysDictItem
     * @return
     */
    @PrePermissions("system:sysDictItem:update")
    @PutMapping("/update")
    public Result<?> update(@RequestBody SysDictItem sysDictItem) {
        sysDictItemService.updateById(sysDictItem);
        return Result.ok();
    }
}

