package com.neo.module.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 手机信息表
 *
 * <AUTHOR>
 * @since 2025-08-22
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("neo_phone_info")
public class PhoneInfo {

    @TableId
    private Long id;

    /**
     * 手机品牌
     */
    @TableField("brand")
    private String brand;

    /**
     * 手机型号
     */
    @TableField("model")
    private String model;

    /**
     * 处理器型号
     */
    @TableField("cpu")
    private String cpu;

    /**
     * 内存大小(GB)
     */
    @TableField("memory_size")
    private Integer memorySize;

    /**
     * 存储大小(GB)
     */
    @TableField("storage_size")
    private Integer storageSize;

    /**
     * 屏幕尺寸(英寸)
     */
    @TableField("screen_size")
    private BigDecimal screenSize;

    /**
     * 屏幕分辨率
     */
    @TableField("screen_resolution")
    private String screenResolution;

    /**
     * 主摄像头像素(MP)
     */
    @TableField("camera_main")
    private Integer cameraMain;

    /**
     * 前摄像头像素(MP)
     */
    @TableField("camera_front")
    private Integer cameraFront;

    /**
     * 电池容量(mAh)
     */
    @TableField("battery_capacity")
    private Integer batteryCapacity;

    /**
     * 操作系统类型
     */
    @TableField("os_type")
    private String osType;

    /**
     * 操作系统版本
     */
    @TableField("os_version")
    private String osVersion;

    /**
     * 价格
     */
    @TableField("price")
    private BigDecimal price;

    /**
     * 发布日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField("release_date")
    private Date releaseDate;

    /**
     * 网络类型
     */
    @TableField("network_type")
    private String networkType;

    /**
     * 重量(g)
     */
    @TableField("weight")
    private BigDecimal weight;

    /**
     * 颜色
     */
    @TableField("color")
    private String color;

    /**
     * 是否支持5G(Y/N)
     */
    @TableField("is_5g_supported")
    private String is5gSupported;


}
