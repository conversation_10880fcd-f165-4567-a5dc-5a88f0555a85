package com.neo.service;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.jwt.JWTUtil;
import com.neo.constant.CacheConstants;
import com.neo.constant.TokenConstants;
import com.neo.model.TokenModel;
import com.neo.utils.RedisUtils;
import com.neo.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class TokenService {

    //token键名称
    @Value("${security.tokenName:Authorization}")
    private String tokenName;

    //token前缀
    @Value("${security.tokenPrefix:Bearer}")
    private String tokenPrefix;

    //过期时间,单位秒，默认2小时
    @Value("${security.expireTime:7200}")
    private Long expireTime;

    @Resource
    private RedisUtils redisUtils;

    private static final String USER_ID = "userId";
    private static final Long MILLIS_MINUTE_TEN = 60 * 10L;

    public TokenModel createToken(SysUserDetail sysUserDetail){

        Map<String,Object> payload = new HashMap<>();
        payload.put(USER_ID,sysUserDetail.getUserId());
        payload.put("token", RandomUtil.randomString(16));

        String token = JWTUtil.createToken(payload, TokenConstants.SECRET.getBytes());

        long loginTime = System.currentTimeMillis() / 1000;
        long loginExpireTime = loginTime + expireTime;

        TokenModel tokenModel = new TokenModel();
        tokenModel.setToken(token);
        tokenModel.setExpireTime(expireTime);

        sysUserDetail.setToken(token);
        sysUserDetail.setLoginTime(loginTime);
        sysUserDetail.setExpireTime(loginExpireTime);

        redisUtils.set(CacheConstants.LOGIN_TOKEN_KEY+token,sysUserDetail,expireTime, TimeUnit.SECONDS);

        return tokenModel;
    }

    public void verifyToken(SysUserDetail userDetail){
        long expireTime = userDetail.getExpireTime();
        long currentTime = System.currentTimeMillis()/1000;
        if (expireTime - currentTime <= MILLIS_MINUTE_TEN) {
            refreshToken(userDetail);
        }
    }

    private void refreshToken(SysUserDetail userDetail) {
        userDetail.setExpireTime(userDetail.getExpireTime()+expireTime);
        redisUtils.set(CacheConstants.LOGIN_TOKEN_KEY + userDetail.getToken(), userDetail, expireTime, TimeUnit.MINUTES);
        log.info(">>>> 用户[{}]token刷新成功 >>>>",userDetail.getUsername());
    }

    public SysUserDetail getUserDetail(HttpServletRequest request){
        String token = request.getHeader(tokenName);
        if (StringUtils.isBlank(token)){
            token = request.getParameter(tokenName);
        }
        if (StringUtils.isBlank(token)){
            return null;
        }

        token = token.substring(tokenPrefix.length()+1);
        String cacheKey = CacheConstants.LOGIN_TOKEN_KEY + token;
        Object object = redisUtils.get(cacheKey);
        if (object != null){
            return (SysUserDetail) object;
        }
        return null;
    }

    public void delToken(SysUserDetail loginUser) {
        redisUtils.delete(CacheConstants.LOGIN_TOKEN_KEY + loginUser.getToken());
    }
}
