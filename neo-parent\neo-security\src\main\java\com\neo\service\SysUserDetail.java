package com.neo.service;

import com.neo.module.dto.SysRoleDTO;
import com.neo.module.dto.SysUserDTO;
import lombok.Data;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

@Data
public class SysUserDetail implements UserDetails{

    private String token;

    private Long loginTime;

    private Long expireTime;

    private SysUserDTO sysUserDTO;

    private List<SysRoleDTO> roleDTOS;

    private List<String> roles;

    private List<String> permissions;

    /**
     * 无参构造函数，用于Jackson反序列化
     */
    public SysUserDetail() {
    }

    public SysUserDetail(SysUserDTO sysUserDTO) {
        this.sysUserDTO = sysUserDTO;
        this.roles = sysUserDTO.getRoles();
        this.permissions = sysUserDTO.getPermissions();
    }

    public String getUserId() {
        return sysUserDTO.getId();
    }

    public String getDeptId() {
        return sysUserDTO.getDeptId();
    }

    @Override
    public String getPassword() {
        return sysUserDTO.getPassword();
    }

    @Override
    public String getUsername() {
        return sysUserDTO.getUserName();
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return Collections.emptyList();
    }

    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    public boolean isEnabled() {
        return true;
    }

}
