export interface SysDict {
  id?: string;
  dictName?: string;
  dictCode?: string;
  status?: string;
  description?: string;
  createTime?: string;
  updateTime?: string;
}

export interface SysDictItem {
  id?: string;
  dictId?: string;
  dictCode?: string;
  itemText?: string;
  itemValue?: string;
  description?: string;
  sort?: number;
  status?: string;
  createTime?: string;
  updateTime?: string;
}

export interface DictPageParams extends SysDict {
  current: number;
  pageSize: number;
}

export interface DictItemPageParams extends SysDictItem {
  current: number;
  pageSize: number;
}

export interface DictListResult {
  records: SysDict[];
  total: number;
  current: number;
  pages: number;
}

export interface DictItemListResult {
  records: SysDictItem[];
  total: number;
  current: number;
  pages: number;
}