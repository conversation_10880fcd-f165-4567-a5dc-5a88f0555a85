<template>
  <t-card class="index-manager-card" :bordered="false">
    <template #header>
      <div class="card-header">
        <h4>ES索引管理</h4>
        <t-space>
          <t-button 
            theme="primary" 
            @click="handleCreateIndex"
            :loading="createLoading"
          >
            创建索引
          </t-button>
          <t-popconfirm 
            content="确定要删除索引吗？删除后所有数据将丢失！" 
            @confirm="handleDeleteIndex"
          >
            <t-button 
              theme="danger" 
              variant="outline"
              :loading="deleteLoading"
            >
              删除索引
            </t-button>
          </t-popconfirm>
        </t-space>
      </div>
    </template>

    <div class="index-info">
      <t-descriptions :data="indexDescriptions" :column="2" />
    </div>

    <div class="batch-operations">
      <t-divider>批量操作</t-divider>
      <t-space>
        <t-upload
          ref="uploadRef"
          :action="uploadAction"
          :headers="uploadHeaders"
          :data="uploadData"
          :before-upload="beforeUpload"
          :on-success="onUploadSuccess"
          :on-fail="onUploadFail"
          accept=".json"
          :show-upload-list="false"
        >
          <t-button theme="default" variant="outline">
            <template #icon>
              <t-icon name="upload" />
            </template>
            导入JSON数据
          </t-button>
        </t-upload>
        
        <t-button 
          theme="default" 
          variant="outline"
          @click="handleBatchAdd"
          :loading="batchLoading"
        >
          <template #icon>
            <t-icon name="add" />
          </template>
          批量添加示例数据
        </t-button>
      </t-space>
    </div>

    <!-- 批量添加数据弹窗 -->
    <t-dialog 
      v-model:visible="batchDialogVisible" 
      header="批量添加数据" 
      :width="600"
      :confirm-btn="{ loading: batchLoading }"
      @confirm="confirmBatchAdd"
      @cancel="cancelBatchAdd"
    >
      <t-textarea
        v-model="batchDataText"
        placeholder="请输入JSON格式的数据数组，例如：
[
  {
    &quot;brand&quot;: &quot;Apple&quot;,
    &quot;model&quot;: &quot;iPhone 15&quot;,
    &quot;cpu&quot;: &quot;A17 Pro&quot;,
    &quot;memorySize&quot;: 8,
    &quot;storageSize&quot;: 256,
    &quot;price&quot;: 7999
  }
]"
        :autosize="{ minRows: 10, maxRows: 20 }"
      />
    </t-dialog>
  </t-card>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { createEsIndex, deleteEsIndex, batchAddEsPhoneInfo } from '@/api/esPhoneInfo';
import { EsPhoneInfo } from '@/api/model/esPhoneInfoModel';

// 定义 emits
const emit = defineEmits<{
  'indexChanged': [];
}>();

const createLoading = ref(false);
const deleteLoading = ref(false);
const batchLoading = ref(false);
const batchDialogVisible = ref(false);
const batchDataText = ref('');
const uploadRef = ref();

// 索引信息描述
const indexDescriptions = ref([
  { label: '索引名称', value: 'neo_phone_info' },
  { label: '索引状态', value: '未知' },
  { label: '文档数量', value: '未知' },
  { label: '索引大小', value: '未知' },
]);

// 上传配置
const uploadAction = computed(() => '/api/example/es/phoneInfo/batchAdd');
const uploadHeaders = computed(() => ({
  'Authorization': `Bearer ${localStorage.getItem('token') || ''}`,
}));
const uploadData = computed(() => ({}));

// 创建索引
const handleCreateIndex = async () => {
  createLoading.value = true;
  try {
    const result = await createEsIndex();
    MessagePlugin.success('索引创建成功');
    emit('indexChanged');
    updateIndexStatus();
  } catch (error: any) {
    MessagePlugin.error(error.message || '索引创建失败');
  } finally {
    createLoading.value = false;
  }
};

// 删除索引
const handleDeleteIndex = async () => {
  deleteLoading.value = true;
  try {
    const result = await deleteEsIndex();
    MessagePlugin.success('索引删除成功');
    emit('indexChanged');
    updateIndexStatus();
  } catch (error: any) {
    MessagePlugin.error(error.message || '索引删除失败');
  } finally {
    deleteLoading.value = false;
  }
};

// 批量添加数据
const handleBatchAdd = () => {
  batchDataText.value = JSON.stringify([
    {
      "brand": "Apple",
      "model": "iPhone 15 Pro",
      "cpu": "A17 Pro",
      "memorySize": 8,
      "storageSize": 256,
      "screenSize": 6.1,
      "screenResolution": "2556x1179",
      "cameraMain": 48,
      "cameraFront": 12,
      "batteryCapacity": 3274,
      "osType": "iOS",
      "osVersion": "iOS 17",
      "price": 8999,
      "releaseDate": "2023-09-15",
      "networkType": "5G",
      "weight": 187,
      "color": "钛原色",
      "is5gSupported": "Y"
    },
    {
      "brand": "Samsung",
      "model": "Galaxy S24 Ultra",
      "cpu": "Snapdragon 8 Gen 3",
      "memorySize": 12,
      "storageSize": 512,
      "screenSize": 6.8,
      "screenResolution": "3120x1440",
      "cameraMain": 200,
      "cameraFront": 12,
      "batteryCapacity": 5000,
      "osType": "Android",
      "osVersion": "Android 14",
      "price": 9999,
      "releaseDate": "2024-01-17",
      "networkType": "5G",
      "weight": 232,
      "color": "钛黑色",
      "is5gSupported": "Y"
    },
    {
      "brand": "Huawei",
      "model": "Mate 60 Pro",
      "cpu": "麒麟9000S",
      "memorySize": 12,
      "storageSize": 512,
      "screenSize": 6.82,
      "screenResolution": "2720x1260",
      "cameraMain": 50,
      "cameraFront": 13,
      "batteryCapacity": 5000,
      "osType": "HarmonyOS",
      "osVersion": "HarmonyOS 4.0",
      "price": 6999,
      "releaseDate": "2023-08-29",
      "networkType": "5G",
      "weight": 225,
      "color": "雅川青",
      "is5gSupported": "Y"
    }
  ], null, 2);
  batchDialogVisible.value = true;
};

// 确认批量添加
const confirmBatchAdd = async () => {
  if (!batchDataText.value.trim()) {
    MessagePlugin.warning('请输入数据');
    return;
  }

  try {
    const data = JSON.parse(batchDataText.value) as EsPhoneInfo[];
    if (!Array.isArray(data)) {
      MessagePlugin.error('数据格式错误，请输入数组格式');
      return;
    }

    batchLoading.value = true;
    await batchAddEsPhoneInfo(data);
    MessagePlugin.success(`成功添加 ${data.length} 条数据`);
    batchDialogVisible.value = false;
    emit('indexChanged');
  } catch (error: any) {
    if (error instanceof SyntaxError) {
      MessagePlugin.error('JSON格式错误，请检查数据格式');
    } else {
      MessagePlugin.error(error.message || '批量添加失败');
    }
  } finally {
    batchLoading.value = false;
  }
};

// 取消批量添加
const cancelBatchAdd = () => {
  batchDialogVisible.value = false;
  batchDataText.value = '';
};

// 文件上传前验证
const beforeUpload = (file: File) => {
  if (!file.name.endsWith('.json')) {
    MessagePlugin.error('只支持JSON格式文件');
    return false;
  }
  if (file.size > 10 * 1024 * 1024) { // 10MB
    MessagePlugin.error('文件大小不能超过10MB');
    return false;
  }
  return true;
};

// 上传成功
const onUploadSuccess = (response: any) => {
  MessagePlugin.success('数据导入成功');
  emit('indexChanged');
};

// 上传失败
const onUploadFail = (error: any) => {
  MessagePlugin.error('数据导入失败');
};

// 更新索引状态（这里是模拟，实际需要调用相应的API）
const updateIndexStatus = () => {
  // 这里可以调用获取索引状态的API
  indexDescriptions.value = [
    { label: '索引名称', value: 'neo_phone_info' },
    { label: '索引状态', value: '正常' },
    { label: '文档数量', value: '待查询' },
    { label: '索引大小', value: '待查询' },
  ];
};

onMounted(() => {
  updateIndexStatus();
});
</script>

<style lang="less" scoped>
.index-manager-card {
  margin-bottom: 16px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    h4 {
      margin: 0;
      color: var(--td-text-color-primary);
    }
  }

  .index-info {
    margin-bottom: 24px;
  }

  .batch-operations {
    .t-divider {
      margin: 16px 0;
    }
  }
}
</style>
