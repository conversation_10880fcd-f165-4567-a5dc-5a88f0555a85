package com.neo.module.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.neo.aspect.annontation.PrePermissions;
import com.neo.model.Result;
import com.neo.common.QueryGenerator;
import com.neo.module.entity.SysLog;
import com.neo.module.service.SysLogService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 系统日志
 *
 * <AUTHOR>
 * @since 2023-05-31
 */
@RestController
@RequestMapping("/sysLog")
public class SysLogController {
    @Resource
    private SysLogService sysLogService;

    /**
     * 分页查询
     *
     * @param params
     * @return
     */
    @PrePermissions("system:sysLog:list")
    @PostMapping("/page")
    public Result<?> page(@RequestBody Map<String, String> params) {
        QueryWrapper<SysLog> queryWrapper = QueryGenerator.initQueryWrapper(SysLog.class, params);
        return Result.ok(sysLogService.page(params, queryWrapper));
    }

    /**
     * 查询
     *
     * @param id
     * @return
     */
    @PrePermissions("system:sysLog:get")
    @GetMapping("/{id}")
    public Result<?> get(@PathVariable String id) {
        SysLog sysLog = sysLogService.getById(id);
        return Result.ok(sysLog);
    }

}

