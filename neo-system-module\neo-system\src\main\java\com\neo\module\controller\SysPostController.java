package com.neo.module.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.neo.aspect.annontation.Log;
import com.neo.aspect.annontation.PrePermissions;
import com.neo.model.Result;
import com.neo.common.QueryGenerator;
import com.neo.module.entity.SysPost;
import com.neo.module.service.SysPostService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 岗位管理
 *
 * <AUTHOR>
 * @since 2023-05-24
 */
@RestController
@RequestMapping("/sysPost")
public class SysPostController {
    @Resource
    private SysPostService sysPostService;

    /**
     * 分页查询
     *
     * @param params
     * @return
     */
    @Log(value = "岗位管理-分页查询")
    @PrePermissions("system:sysPost:list")
    @PostMapping("/page")
    public Result<?> page(@RequestBody Map<String, String> params) {
        QueryWrapper<SysPost> queryWrapper = QueryGenerator.initQueryWrapper(SysPost.class, params);
        return Result.ok(sysPostService.page(params, queryWrapper));
    }

    /**
     * 查询
     *
     * @param id
     * @return
     */
    @Log(value = "岗位管理-详情查询")
    @PrePermissions("system:sysPost:get")
    @GetMapping("/{id}")
    public Result<?> get(@PathVariable String id) {
        SysPost sysPost = sysPostService.getById(id);
        return Result.ok(sysPost);
    }

    /**
     * 列表
     *
     * @param sysPost
     * @return
     */
    @Log(value = "岗位管理-列表查询")
    @PrePermissions("system:sysPost:list")
    @PostMapping("/list")
    public Result<?> list(@RequestBody SysPost sysPost) {
        List<SysPost> list = sysPostService.list(sysPost);
        return Result.ok(list);
    }

    /**
     * 新增
     *
     * @param sysPost
     * @return
     */
    @Log(value = "岗位管理-新增")
    @PrePermissions("system:sysPost:add")
    @PostMapping("/add")
    public Result<?> add(@RequestBody SysPost sysPost) {
        sysPostService.save(sysPost);
        return Result.ok();
    }

    /**
     * 删除
     *
     * @param ids
     * @return
     */
    @Log(value = "岗位管理-删除")
    @PrePermissions("system:sysPost:delete")
    @DeleteMapping
    public Result<?> delete(@RequestBody Set<String> ids) {
        sysPostService.removeByIds(ids);
        return Result.ok();
    }

    /**
     * 更新
     *
     * @param sysPost
     * @return
     */
    @Log(value = "岗位管理-更新")
    @PrePermissions("system:sysPost:update")
    @PutMapping("/update")
    public Result<?> update(@RequestBody SysPost sysPost) {
        // 参考角色管理，防止岗位编码被恶意修改
        sysPost.setCode(null);
        sysPostService.updateById(sysPost);
        return Result.ok();
    }
}

