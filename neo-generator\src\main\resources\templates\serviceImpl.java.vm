package ${package.ServiceImpl};

import ${package.Entity}.${entity};
import ${package.Mapper}.${table.mapperName};
import ${package.Service}.${table.serviceName};
import ${superServiceImplClassPackage};

import org.springframework.stereotype.Service;

/**
 * $!{table.comment} 服务实现类
 * <AUTHOR>
 * @since ${date}
 */
@Service
#if(${kotlin})
open class ${table.serviceImplName} : ${superServiceImplClass}<${table.mapperName}, ${entity}>(), ${table.serviceName} {

        }
#else
        public class ${table.serviceImplName} extends ${superServiceImplClass}<${table.mapperName}, ${entity}>implements ${table.serviceName} {

        @Resource
        private ${table.mapperName} ${table.entityPath}Mapper;

        @Override
        public IPage<${table.entityName}>page(Map<String,String>params,QueryWrapper<${entity}>queryWrapper){
        String value;
        queryWrapper
    #foreach(${field} in $table.fields)
            .eq(StringUtils.isNotBlank((value=(String)params.get("${field.propertyName}"))),"${field.name}",value)
    #end
        ;
        IPage<${table.entityName}>page= ${table.entityPath}Mapper.selectPage(QueryGenerator.initPage(params),queryWrapper);
        return page;
        }

        @Override
        public List<${table.entityName}>list(${table.entityName} ${table.entityPath}){
        QueryWrapper<${table.entityName}>queryWrapper=new QueryWrapper<>(${table.entityPath});
        List<${table.entityName}> ${table.entityPath}s= ${table.entityPath}Mapper.selectList(queryWrapper);
        return ${table.entityPath}s;
        }

        }
#end
