datasource:
  master:
    # jdbc 所有参数配置参考 https://lionli.blog.csdn.net/article/details/122018562
    # rewriteBatchedStatements=true 批处理优化 大幅提升批量插入更新删除性能
    url: *******************************************************************************************************************************************************************************
    username: root
    password: Y3Icbz65MlKjEf2W
  job:
    url: ***********************************************************************************************************************************************************************************
    username: root
    password: Y3Icbz65MlKjEf2W

spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    # 动态数据源文档 https://www.kancloud.cn/tracy5546/dynamic-datasource/content
    dynamic:
      # 严格模式 匹配不到数据源则报错
      strict: false
      hikari:
        # 最大连接池数量
        maxPoolSize: 20
        # 最小空闲线程数量
        minIdle: 10
        # 配置获取连接等待超时的时间
        connectionTimeout: 10000
        # 校验超时时间
        validationTimeout: 5000
        # 空闲连接存活最大时间，默认10分钟
        idleTimeout: 60000
        # 此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认30分钟
        maxLifetime: 900000
        # 连接测试query（配置检测连接是否有效）
        connectionTestQuery: SELECT 1