package com.neo.module.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.neo.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 附件信息表
 *
 * <AUTHOR>
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@Accessors(chain = true)
@TableName("sys_attachment")
public class SysAttachment extends BaseEntity {

    /**
     * 文件名称
     */
    @TableField("file_name")
    private String fileName;

    /**
     * 文件存储路径
     */
    @TableField("file_path")
    private String filePath;

    /**
     * 文件大小(字节)
     */
    @TableField("file_size")
    private Long fileSize;

    /**
     * 文件访问地址
     */
    @TableField("file_url")
    private String fileUrl;

    /**
     * 文件类型/后缀名
     */
    @TableField("file_type")
    private String fileType;

    /**
     * 存储类型(LOCAL:本地存储, OSS:对象存储)
     */
    @TableField("storage_type")
    private String storageType;

    /**
     * 文件MD5值
     */
    @TableField("md5")
    private String md5;

    /**
     * 状态(0:禁用,1:正常)
     */
    @TableField("status")
    private Boolean status;


}
