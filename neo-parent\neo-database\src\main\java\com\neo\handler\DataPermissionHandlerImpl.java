package com.neo.handler;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.handler.DataPermissionHandler;
import com.neo.context.DataScopeContext;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import org.springframework.stereotype.Component;

/**
 * 数据权限处理器实现
 * 实现MyBatis-Plus的数据权限拦截器
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class DataPermissionHandlerImpl implements DataPermissionHandler {

    @Override
    public Expression getSqlSegment(Expression where, String mappedStatementId) {
        try {
            // 获取数据权限上下文
            DataScopeContext context = DataScopeContext.getContext();
            
            // 如果设置了清除标志或没有数据权限SQL，直接返回原条件
            if (context.getDataScopeClearFlag() || StrUtil.isBlank(context.getDataScopeSql())) {
                return where;
            }

            // 解析数据权限SQL片段
            String dataScopeSql = context.getDataScopeSql();
            log.debug("应用数据权限SQL: {}", dataScopeSql);

            // 将数据权限SQL转换为Expression
            Expression dataScopeExpression = CCJSqlParserUtil.parseCondExpression(dataScopeSql.substring(5)); // 去掉前面的" AND "
            
            // 如果原来没有where条件，直接返回数据权限条件
            if (where == null) {
                return dataScopeExpression;
            }
            
            // 将原有条件和数据权限条件用AND连接
            return new AndExpression(where, dataScopeExpression);
            
        } catch (Exception e) {
            log.error("处理数据权限SQL失败: {}", e.getMessage(), e);
            // 发生异常时返回原有的where条件，确保系统正常运行
            return where;
        } finally {
            // 使用完毕后清除上下文，避免内存泄漏
            DataScopeContext.clearContext();
        }
    }

}