package com.neo.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * RocketMQ工具类
 *
 * <AUTHOR>
 * @since 2025-09-04
 */
@Component
@Slf4j
public class MqUtil {

    @Resource
    private RocketMQTemplate rocketMQTemplate;

    /**
     * 发送消息
     *
     * @param topic   主题
     * @param message 消息内容
     */
    public void sendMessage(String topic, String message) {
        try {
            log.info("发送消息到主题: {}, 消息内容: {}", topic, message);
            rocketMQTemplate.convertAndSend(topic, message);
        } catch (Exception e) {
            log.error("消息发送失败: {}", e.getMessage(), e);
            throw new RuntimeException("消息发送失败", e);
        }
    }

    /**
     * 发送延迟消息
     *
     * @param topic     主题
     * @param message   消息内容
     * @param delayTime 延迟时间(毫秒)
     */
    public void sendDelayMessage(String topic, String message, Long delayTime) {
        try {
            log.info("发送延迟消息到主题: {}, 消息内容: {}, 延迟时间: {}ms", topic, message, delayTime);
            Message<String> msg = MessageBuilder.withPayload(message).build();
            rocketMQTemplate.syncSend(topic, msg, 3000, 1);
        } catch (Exception e) {
            log.error("延迟消息发送失败: {}", e.getMessage(), e);
            throw new RuntimeException("延迟消息发送失败", e);
        }
    }

    /**
     * 发送带标签的消息
     *
     * @param topic   主题
     * @param tag     标签
     * @param message 消息内容
     */
    public void sendTagMessage(String topic, String tag, String message) {
        try {
            String destination = topic + ":" + tag;
            log.info("发送带标签消息到主题: {}, 标签: {}, 消息内容: {}", topic, tag, message);
            rocketMQTemplate.convertAndSend(destination, message);
        } catch (Exception e) {
            log.error("带标签消息发送失败: {}", e.getMessage(), e);
            throw new RuntimeException("带标签消息发送失败", e);
        }
    }
}
