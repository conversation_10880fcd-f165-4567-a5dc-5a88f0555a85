package com.neo.module.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.neo.module.entity.SysAttachment;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 附件信息表 服务类
 *
 * <AUTHOR>
 * @since 2025-01-06
 */
public interface SysAttachmentService extends IService<SysAttachment> {

    IPage<SysAttachment> page(Map<String, String> params, QueryWrapper<SysAttachment> queryWrapper);

    List<SysAttachment> list(SysAttachment sysAttachment);

    void deleteFileByIds(Set<String> ids) throws Exception;

    void downloadFileById(String id, HttpServletResponse response) throws Exception;

}
