export interface SysRole {
  id?: string;
  roleName?: string;
  roleKey?: string;
  menuRelation?: boolean; // 菜单树选择项是否关联显示（父子联动）
  deptRelation?: boolean; // 部门树选择项是否关联显示（父子联动）
  dataScope?: string; // 数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）
  remark?: string;
  createTime?: string;
  updateTime?: string;
  status?: string;
  menuIds?: string[]; // 菜单权限ID列表
  deptIds?: string[]; // 部门权限ID列表
}

export interface RolePageParams {
  current: number;
  pageSize: number;
  roleName?: string;
  roleKey?: string;
  status?: string;
}

export interface RoleListResult {
  records: SysRole[];
  total: number;
  current: number;
  pages: number;
}