<template>
  <t-dialog
    v-model:visible="dialogVisible"
    header="日志详情"
    width="800px"
    :close-btn="true"
    :footer="false"
  >
    <div class="log-detail-container" v-if="logData">
      <t-card title="基本信息" :bordered="false" class="info-card">
        <t-descriptions :column="2">
          <t-descriptions-item v-for="(item, index) in basicInfoData" :key="index" :label="item.label">
            {{ item.value }}
          </t-descriptions-item>
        </t-descriptions>
      </t-card>

      <t-card title="请求信息" :bordered="false" class="info-card">
        <t-descriptions :column="1">
          <t-descriptions-item v-for="(item, index) in requestInfoData" :key="index" :label="item.label">
            {{ item.value }}
          </t-descriptions-item>
        </t-descriptions>
      </t-card>

      <t-card title="响应信息" :bordered="false" class="info-card" v-if="logData.resultJson">
        <t-textarea
          v-model="formattedResultJson"
          :readonly="true"
          :autosize="{ minRows: 5, maxRows: 10 }"
          placeholder="无响应数据"
        />
      </t-card>
    </div>
  </t-dialog>
</template>

<script lang="ts" setup>
  import { computed, ref, watch } from 'vue';
  import { SysLog } from '@/api/model/logModel';

  interface Props {
    visible: boolean;
    logData?: SysLog;
  }

  interface Emits {
    (e: 'update:visible', value: boolean): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    visible: false,
  });

  const emit = defineEmits<Emits>();

  // 弹窗可见性
  const dialogVisible = computed({
    get: () => props.visible,
    set: (value: boolean) => emit('update:visible', value)
  });

  // 格式化的结果JSON
  const formattedResultJson = ref('');

  // 基本信息数据
  const basicInfoData = computed(() => {
    if (!props.logData) return [];
    
    return [
      {
        label: '用户ID',
        value: props.logData.userId || '-',
      },
      {
        label: '用户名',
        value: props.logData.username || '-',
      },
      {
        label: '业务ID',
        value: props.logData.businessId || '-',
      },
      {
        label: '业务模块',
        value: props.logData.businessModule || '-',
      },
      {
        label: '操作类型',
        value: props.logData.operateType || '-',
      },
      {
        label: '操作名称',
        value: props.logData.operateName || '-',
      },
      {
        label: 'IP地址',
        value: props.logData.ipAddress || '-',
      },
      {
        label: '执行时间',
        value: props.logData.costTime ? `${props.logData.costTime}ms` : '-',
      },
      {
        label: '创建时间',
        value: props.logData.createTime ? new Date(props.logData.createTime).toLocaleString('zh-CN') : '-',
      },
    ];
  });

  // 请求信息数据
  const requestInfoData = computed(() => {
    if (!props.logData) return [];
    
    return [
      {
        label: '方法名称',
        value: props.logData.methodName || '-',
      },
      {
        label: '请求方法',
        value: props.logData.requestMethod || '-',
      },
      {
        label: '请求路径',
        value: props.logData.requestUrl || '-',
      },
      {
        label: '请求参数',
        value: props.logData.requestParam || '无参数',
      },
    ];
  });

  // 格式化JSON字符串
  const formatJson = (jsonStr: string) => {
    try {
      const parsed = JSON.parse(jsonStr);
      return JSON.stringify(parsed, null, 2);
    } catch {
      return jsonStr;
    }
  };

  // 监听logData变化，格式化JSON
  watch(
    () => props.logData?.resultJson,
    (newValue) => {
      if (newValue) {
        formattedResultJson.value = formatJson(newValue);
      } else {
        formattedResultJson.value = '';
      }
    },
    { immediate: true }
  );
</script>

<style lang="less" scoped>
  .log-detail-container {
    .info-card {
      margin-bottom: var(--td-comp-margin-l);
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
</style>