package com.neo.module.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.neo.common.QueryGenerator;
import com.neo.module.entity.PhoneInfo;
import com.neo.module.mapper.PhoneInfoMapper;
import com.neo.module.service.PhoneInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 手机信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-22
 */
@Service
public class PhoneInfoServiceImpl extends ServiceImpl<PhoneInfoMapper, PhoneInfo> implements PhoneInfoService {

    @Resource
    private PhoneInfoMapper phoneInfoMapper;

    @Override
    public IPage<PhoneInfo> page(Map<String, String> params, QueryWrapper<PhoneInfo> queryWrapper) {
        String value;
        queryWrapper
                .eq(StringUtils.isNotBlank((value = (String) params.get("brand"))), "brand", value)
                .eq(StringUtils.isNotBlank((value = (String) params.get("model"))), "model", value)
                .eq(StringUtils.isNotBlank((value = (String) params.get("cpu"))), "cpu", value)
                .eq(StringUtils.isNotBlank((value = (String) params.get("memorySize"))), "memory_size", value)
                .eq(StringUtils.isNotBlank((value = (String) params.get("storageSize"))), "storage_size", value)
                .eq(StringUtils.isNotBlank((value = (String) params.get("screenSize"))), "screen_size", value)
                .eq(StringUtils.isNotBlank((value = (String) params.get("screenResolution"))), "screen_resolution", value)
                .eq(StringUtils.isNotBlank((value = (String) params.get("cameraMain"))), "camera_main", value)
                .eq(StringUtils.isNotBlank((value = (String) params.get("cameraFront"))), "camera_front", value)
                .eq(StringUtils.isNotBlank((value = (String) params.get("batteryCapacity"))), "battery_capacity", value)
                .eq(StringUtils.isNotBlank((value = (String) params.get("osType"))), "os_type", value)
                .eq(StringUtils.isNotBlank((value = (String) params.get("osVersion"))), "os_version", value)
                .eq(StringUtils.isNotBlank((value = (String) params.get("price"))), "price", value)
                .eq(StringUtils.isNotBlank((value = (String) params.get("releaseDate"))), "release_date", value)
                .eq(StringUtils.isNotBlank((value = (String) params.get("networkType"))), "network_type", value)
                .eq(StringUtils.isNotBlank((value = (String) params.get("weight"))), "weight", value)
                .eq(StringUtils.isNotBlank((value = (String) params.get("color"))), "color", value)
                .eq(StringUtils.isNotBlank((value = (String) params.get("is5gSupported"))), "is_5g_supported", value)
        ;
        IPage<PhoneInfo> page = phoneInfoMapper.selectPage(QueryGenerator.initPage(params), queryWrapper);
        return page;
    }

    @Override
    public List<PhoneInfo> list(PhoneInfo phoneInfo) {
        QueryWrapper<PhoneInfo> queryWrapper = new QueryWrapper<>(phoneInfo);
        return phoneInfoMapper.selectList(queryWrapper);
    }

}
