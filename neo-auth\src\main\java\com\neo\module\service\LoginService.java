package com.neo.module.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.useragent.UserAgent;
import com.neo.constant.CommonConstant;
import com.neo.exception.BusinessException;
import com.neo.model.TokenModel;
import com.neo.module.dto.SysLoginLogDTO;
import com.neo.module.model.LoginModel;
import com.neo.service.ConfigService;
import com.neo.service.SysUserDetail;
import com.neo.service.TokenService;
import com.neo.utils.IPUtils;
import com.neo.utils.SecurityUtils;
import com.neo.utils.SpringUtils;
import com.neo.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class LoginService {

    @Resource
    private ConfigService configService;

    @Resource
    private AuthenticationManager authenticationManager;

    @Resource
    private TokenService tokenService;

    @Resource
    private CaptchaService captchaService;

    public Map<String,Object> login(LoginModel loginModel){
        if (StringUtils.isBlank(loginModel.getUsername())){
            throw new BusinessException("账号不能为空");
        }
        if (StringUtils.isBlank(loginModel.getPassword())){
            throw new BusinessException("密码不能为空");
        }

        //验证码开关
        if (configService.getConfigAsBoolean("global.captcha.status",false)){
            captchaService.verifyCaptcha(loginModel.getCode(),loginModel.getCodeId());
        }

        Authentication authenticate = null;
        try {
            UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(loginModel.getUsername(),loginModel.getPassword());
            authenticate = authenticationManager.authenticate(authenticationToken);
            if (ObjectUtil.isNull(authenticate)){
                throw new BusinessException("登录失败，请检查用户名或密码是否正确");
            }
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            throw new BusinessException("登录失败，请检查用户名或密码是否正确");
        }

        SysUserDetail sysUserDetail = (SysUserDetail) authenticate.getPrincipal();

        TokenModel tokenModel = tokenService.createToken(sysUserDetail);

        Map<String,Object> loginInfo = new HashMap<>();
        loginInfo.put("token",tokenModel.getToken());
        loginInfo.put("expireTime",tokenModel.getExpireTime());
        loginInfo.put("userInfo",sysUserDetail.getSysUserDTO());

        saveLog(sysUserDetail.getUserId(),sysUserDetail.getUsername(),CommonConstant.SUCCESS,"登录成功");
        return loginInfo;
    }


    private void saveLog(String userId,String username,String status,String msg){
        log.info("[Login] username:{} ,loginResult:{}",username,msg);

        UserAgent userAgent = IPUtils.getUserAgent();
        String ipAddr = IPUtils.getIpAddr();

        SysLoginLogDTO sysLoginLogDTO  = new SysLoginLogDTO();
        sysLoginLogDTO.setUserId(userId);
        sysLoginLogDTO.setUsername(username);
        sysLoginLogDTO.setIpAddr(ipAddr);
        sysLoginLogDTO.setIsMobile(userAgent.isMobile() ? CommonConstant.YES : CommonConstant.NO);
        sysLoginLogDTO.setBrowser(userAgent.getBrowser().toString());
        sysLoginLogDTO.setBrowserVersion(userAgent.getVersion());
        sysLoginLogDTO.setPlatform(userAgent.getPlatform().toString());
        sysLoginLogDTO.setOs(userAgent.getOs().toString());
        sysLoginLogDTO.setOsVersion(userAgent.getPlatform().toString());
        sysLoginLogDTO.setEngine(userAgent.getEngine().toString());
        sysLoginLogDTO.setEngineVersion(userAgent.getEngineVersion());
        sysLoginLogDTO.setStatus(status);
        sysLoginLogDTO.setMessage(msg);

        SpringUtils.context().publishEvent(sysLoginLogDTO);
    }

    public void logout() {
        SysUserDetail loginUser = SecurityUtils.getLoginUser();
        tokenService.delToken(loginUser);
    }

}
