<template>
  <t-dialog 
    v-model:visible="dialogVisible" 
    :header="dialogTitle" 
    :width="600" 
    :confirm-btn="{ loading: formLoading }" 
    @confirm="onConfirm" 
    @cancel="onCancel"
  >
    <template v-if="dialogVisible">
      <t-form ref="form" :data="formData" :rules="rules" :label-width="80" @submit="onSubmit">
        <t-form-item label="角色名称" name="roleName">
          <t-input 
            v-model="formData.roleName" 
            placeholder="请输入角色名称"
          />
        </t-form-item>
        
        <t-form-item label="角色编码" name="roleKey">
          <t-input 
            v-model="formData.roleKey" 
            :disabled="isEdit"
            placeholder="请输入角色编码"
          />
        </t-form-item>
        
        <t-form-item label="状态" name="status">
          <t-switch 
            v-model="formData.status" 
            :custom-value="['1', '0']" 
            checked="1" 
            unchecked="0"
            size="small"
          />
        </t-form-item>
        
        <t-form-item label="菜单权限" name="menuIds">
          <div class="menu-permission-container">
            <!-- 操作按钮组 -->
            <div class="menu-actions">
              <t-button size="small" theme="primary" variant="text" @click="selectAll">全选</t-button>
              <t-button size="small" theme="primary" variant="text" @click="unselectAll">全不选</t-button>
              <t-button size="small" theme="primary" variant="text" @click="expandAll">展开全部</t-button>
              <t-button size="small" theme="primary" variant="text" @click="collapseAll">折叠全部</t-button>
              <t-space :size="8">
                <span>父子联动：</span>
                <t-switch 
                  v-model="checkStrictly" 
                  :custom-value="[false, true]"
                  size="small"
                  @change="onCheckStrictlyChange"
                />
              </t-space>
            </div>
            
            <!-- 菜单权限树 -->
            <div class="menu-tree-container">
              <div v-if="menuTreeData.length === 0" class="empty-state">
                <p>暂无菜单数据</p>
              </div>
              <t-tree
                v-else
                ref="menuTreeRef"
                v-model:value="formData.menuIds"
                :data="menuTreeData"
                :keys="{ value: 'id', label: 'name', children: 'children' }"
                checkable
                :check-strictly="checkStrictly"
                :expanded="expandedKeys"
                hover
                activable
                line
                valueMode='all'
                @expand="onExpand"
                @check="onCheck"
              />
            </div>
          </div>
        </t-form-item>
        
        <t-form-item label="数据范围" name="dataScope">
          <t-select 
            v-model="formData.dataScope" 
            :options="dataScopeOptions"
            placeholder="请选择数据范围"
            clearable
          />
        </t-form-item>
        
        <t-form-item v-if="formData.dataScope === '2'" label="部门权限" name="deptIds">
          <div class="dept-permission-container">
            <!-- 操作按钮组 -->
            <div class="dept-actions">
              <t-button size="small" theme="primary" variant="text" @click="selectAllDept">全选</t-button>
              <t-button size="small" theme="primary" variant="text" @click="unselectAllDept">全不选</t-button>
              <t-button size="small" theme="primary" variant="text" @click="expandAllDept">展开全部</t-button>
              <t-button size="small" theme="primary" variant="text" @click="collapseAllDept">折叠全部</t-button>
              <t-space :size="8">
                <span>父子联动：</span>
                <t-switch 
                  v-model="deptCheckStrictly" 
                  :custom-value="[false, true]"
                  size="small"
                  @change="onDeptCheckStrictlyChange"
                />
              </t-space>
            </div>
            
            <!-- 部门权限树 -->
            <div class="dept-tree-container">
              <div v-if="deptTreeData.length === 0" class="empty-state">
                <p>暂无部门数据</p>
              </div>
              <t-tree
                v-else
                ref="deptTreeRef"
                v-model:value="formData.deptIds"
                :data="deptTreeData"
                :keys="{ value: 'id', label: 'name', children: 'children' }"
                checkable
                valueMode='all'
                :check-strictly="deptCheckStrictly"
                :expanded="deptExpandedKeys"
                hover
                activable
                line
                @expand="onDeptExpand"
                @check="onDeptCheck"
              />
            </div>
          </div>
        </t-form-item>
        
        <t-form-item label="描述" name="remark">
          <t-textarea 
            v-model="formData.remark" 
            placeholder="请输入描述"
            :maxlength="500"
            :autosize="{ minRows: 3, maxRows: 5 }"
          />
        </t-form-item>
      </t-form>
    </template>
  </t-dialog>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { addRole, updateRole } from '@/api/role';
import { getMenuTree, SysMenu } from '@/api/menu';
import { getDeptTree } from '@/api/dept';
import type { SysDeptTree } from '@/api/model/deptModel';
import { SysRole } from '@/api/model/roleModel';

// 定义 props
interface Props {
  visible: boolean;
  isEdit: boolean;
  roleData?: SysRole;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  isEdit: false,
  roleData: undefined
});

// 定义 emits
const emit = defineEmits<{
  'update:visible': [value: boolean];
  'success': [];
}>();

// 响应式数据
const formLoading = ref(false);
const form = ref();
const menuTreeRef = ref();
const deptTreeRef = ref();
const menuTreeData = ref<SysMenu[]>([]);
const deptTreeData = ref<SysDeptTree[]>([]);
const expandedKeys = ref<string[]>([]);
const deptExpandedKeys = ref<string[]>([]);
const checkStrictly = ref(false); // 父子联动开关，false表示联动，true表示不联动
const deptCheckStrictly = ref(false); // 部门父子联动开关

// 数据范围选项
const dataScopeOptions = [
  { value: '1', label: '全部数据权限' },
  { value: '2', label: '自定数据权限' },
  { value: '3', label: '本部门数据权限' },
  { value: '4', label: '本部门及以下数据权限' },
];

const formData = ref({
  id: '',
  roleName: '',
  roleKey: '',
  status: '1',
  dataScope: '1', // 默认为全部数据权限
  remark: '',
  menuIds: [] as string[],
  deptIds: [] as string[], // 部门权限ID列表
  menuRelation: false, // 菜单树选择项是否关联显示（父子联动）
  deptRelation: false, // 部门树选择项是否关联显示（父子联动）
});

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => emit('update:visible', value)
});

const dialogTitle = computed(() => {
  return props.isEdit ? '编辑角色' : '新增角色';
});

const rules = computed(() => ({
  roleName: [
    { required: true, message: '请输入角色名称' }
  ],
  roleKey: [
    { required: true, message: '请输入角色编码' }
  ],
}));

// 初始化表单数据
const initFormData = () => {
  formData.value = {
    id: '',
    roleName: '',
    roleKey: '',
    status: '1',
    dataScope: '1',
    remark: '',
    menuIds: [],
    deptIds: [],
    menuRelation: false,
    deptRelation: false,
  };
};

// 加载菜单树数据
const loadMenuTree = async () => {
  try {
    const result = await getMenuTree({});
    menuTreeData.value = result;
    expandedKeys.value = menuTreeData.value.map(item => item.id!).filter(Boolean);
    
    // 当开启父子联动时（checkStrictly为false），过滤掉所有非叶子节点
    if (!checkStrictly.value && props.isEdit && props.roleData) {
      // 获取菜单树中的所有叶子节点ID
      const leafNodeIds = new Set<string>();
      
      const collectLeafNodes = (nodes: SysMenu[]) => {
        nodes.forEach(node => {
          if (node.children && node.children.length > 0) {
            // 有子节点，递归处理
            collectLeafNodes(node.children);
          } else if (node.id) {
            // 没有子节点，是叶子节点
            leafNodeIds.add(node.id);
          }
        });
      };
      
      collectLeafNodes(menuTreeData.value);
      
      // 过滤出叶子节点
      formData.value.menuIds = formData.value.menuIds.filter((id: string) => leafNodeIds.has(id));
    }
  } catch (error) {
    MessagePlugin.error('获取菜单数据失败');
  }
};

// 加载部门树数据
const loadDeptTree = async () => {
  try {
    const result = await getDeptTree({});
    deptTreeData.value = result;
    deptExpandedKeys.value = deptTreeData.value.map(item => item.id!).filter(Boolean);
    
    // 当开启父子联动时（deptCheckStrictly为false），过滤掉所有非叶子节点
    if (!deptCheckStrictly.value && props.isEdit && props.roleData) {
      // 获取部门树中的所有叶子节点ID
      const leafNodeIds = new Set<string>();
      
      const collectLeafNodes = (nodes: SysDeptTree[]) => {
        nodes.forEach(node => {
          if (node.children && node.children.length > 0) {
            // 有子节点，递归处理
            collectLeafNodes(node.children);
          } else if (node.id) {
            // 没有子节点，是叶子节点
            leafNodeIds.add(node.id);
          }
        });
      };
      
      collectLeafNodes(deptTreeData.value);
      
      // 过滤出叶子节点
      formData.value.deptIds = formData.value.deptIds.filter((id: string) => leafNodeIds.has(id));
    }
  } catch (error) {
    MessagePlugin.error('获取部门数据失败');
  }
};

// 加载角色详情数据
const loadRoleDetail = () => {
  if (!props.roleData) return;
  
  const { id = '', roleName = '', roleKey = '', status = '1', dataScope = '1', 
          remark = '', menuIds = [], deptIds = [], menuRelation = false, deptRelation = false } = props.roleData;
  
  formData.value = { id, roleName, roleKey, status, dataScope, remark, menuIds, deptIds, menuRelation, deptRelation };
  
  // 同步父子联动状态
  checkStrictly.value = !menuRelation;
  deptCheckStrictly.value = !deptRelation;
  
  // 当开启父子联动时（checkStrictly为false），过滤掉所有非叶子节点
  if (!checkStrictly.value && menuTreeData.value.length > 0) {
    // 获取菜单树中的所有叶子节点ID
    const leafNodeIds = new Set<string>();
    
    const collectLeafNodes = (nodes: SysMenu[]) => {
      nodes.forEach(node => {
        if (node.children && node.children.length > 0) {
          // 有子节点，递归处理
          collectLeafNodes(node.children);
        } else if (node.id) {
          // 没有子节点，是叶子节点
          leafNodeIds.add(node.id);
        }
      });
    };
    
    collectLeafNodes(menuTreeData.value);
    
    // 过滤出叶子节点
    formData.value.menuIds = menuIds.filter((id: string) => leafNodeIds.has(id));
  }
};

// 通用函数：递归获取树结构中所有节点的ID
const getAllIds = (treeData: any[]): string[] => {
  const ids: string[] = [];
  const traverse = (nodes: any[]) => {
    nodes.forEach(node => {
      if (node.id) ids.push(node.id);
      if (node.children?.length) traverse(node.children);
    });
  };
  traverse(treeData);
  return ids;
};

// 菜单操作函数
const selectAll = () => formData.value.menuIds = getAllIds(menuTreeData.value);
const unselectAll = () => { formData.value.menuIds = []; };
const expandAll = () => expandedKeys.value = getAllIds(menuTreeData.value);
const collapseAll = () => { expandedKeys.value = []; };

// 部门操作函数
const selectAllDept = () => formData.value.deptIds = getAllIds(deptTreeData.value);
const unselectAllDept = () => { formData.value.deptIds = []; };
const expandAllDept = () => deptExpandedKeys.value = getAllIds(deptTreeData.value);
const collapseAllDept = () => { deptExpandedKeys.value = []; };

// 事件处理函数
const onCheckStrictlyChange = (value: any) => {
  checkStrictly.value = value;
  formData.value.menuRelation = !value;
};

const onDeptCheckStrictlyChange = (value: any) => {
  deptCheckStrictly.value = value;
  formData.value.deptRelation = !value;
};

const onExpand = (keys: any[]) => expandedKeys.value = keys.map(String);
const onDeptExpand = (keys: any[]) => deptExpandedKeys.value = keys.map(String);
const onCheck = (keys: any[]) => formData.value.menuIds = keys.map(String);
const onDeptCheck = (keys: any[]) => formData.value.deptIds = keys.map(String);

// 监听弹窗显示状态
watch(() => props.visible, async (newVisible) => {
  if (newVisible) {
    initFormData();
    if (props.isEdit && props.roleData) loadRoleDetail();
    await Promise.all([loadMenuTree(), loadDeptTree()]);
  } else {
    // 清理数据
    menuTreeData.value = [];
    deptTreeData.value = [];
    expandedKeys.value = [];
    deptExpandedKeys.value = [];
  }
});

// 监听数据范围变化
watch(() => formData.value.dataScope, (newDataScope) => {
  if (newDataScope !== '2') formData.value.deptIds = [];
});

// 按钮事件处理
const onConfirm = () => form.value.submit();
const onCancel = () => emit('update:visible', false);

// 表单提交处理
const onSubmit = ({ validateResult, firstError }: any) => {
  checkStrictly.value = false;

  if (validateResult === true) {
    formLoading.value = true;

        // 取得所有节点
    const items = menuTreeRef.value.getItems();
    const revertSelection: any[] = [];
    items.forEach((item:any) => {
      if (item.checked || item.indeterminate) {
        // checked 为 true, 为直接选中状态
        // indeterminate 为 true, 为半选状态
        revertSelection.push(item.value);
      }
    });
    
    // 当开启父子联动时（checkStrictly为false），过滤掉所有非叶子节点
    if (!checkStrictly.value) {
      // 获取菜单树中的所有叶子节点ID
      const leafNodeIds = new Set<string>();
      
      const collectLeafNodes = (nodes: SysMenu[]) => {
        nodes.forEach(node => {
          if (node.children && node.children.length > 0) {
            // 有子节点，递归处理
            collectLeafNodes(node.children);
          } else if (node.id) {
            // 没有子节点，是叶子节点
            leafNodeIds.add(node.id);
          }
        });
      };
      
      collectLeafNodes(menuTreeData.value);
      
      // 过滤出叶子节点
      formData.value.menuIds = revertSelection.filter(id => leafNodeIds.has(id));
    } else {
      formData.value.menuIds = revertSelection;
    }
    
    console.log('提交数据:', formData.value.menuIds);

    const promise = props.isEdit ? updateRole({ ...formData.value }) : addRole({ ...formData.value });

    promise.then(() => {
      MessagePlugin.success(props.isEdit ? '更新成功' : '添加成功');
      emit('update:visible', false);
      emit('success');
    }).catch((e) => {
      MessagePlugin.error(e.message || (props.isEdit ? '更新失败' : '添加失败'));
    }).finally(() => {
      formLoading.value = false;
    });
  } else {
    MessagePlugin.warning(firstError);
  }
};
</script>

<style lang="less" scoped>
// 弹窗内容样式优化
:deep(.t-dialog__body) {
  max-height: 600px;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 16px 24px;
}

:deep(.t-dialog) {
  .t-form {
    overflow: visible;
    width: 100%;
    box-sizing: border-box;
  }
  
  // 确保表单项不会超出宽度
  .t-form-item {
    margin-bottom: 16px;
  }
  
  // 输入框宽度限制
  .t-input,
  .t-switch,
  .t-textarea {
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
  }
}

// 菜单权限容器样式
.menu-permission-container {
  width: 100%;
  border: 1px solid var(--td-border-level-1-color);
  border-radius: var(--td-radius-default);
  padding: 12px;
  background-color: var(--td-bg-color-container);
  
  .menu-actions {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--td-border-level-1-color);
    flex-wrap: wrap;
  }
  
  .menu-tree-container {
    width: 100%;
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid var(--td-border-level-1-color);
    border-radius: var(--td-radius-default);
    padding: 8px;
    background-color: transparent;
    
    .empty-state {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100px;
      color: var(--td-text-color-placeholder);
      font-size: 14px;
    }
  }
}

// 部门权限容器样式
.dept-permission-container {
  width: 100%;
  border: 1px solid var(--td-border-level-1-color);
  border-radius: var(--td-radius-default);
  padding: 12px;
  background-color: var(--td-bg-color-container);
  
  .dept-actions {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--td-border-level-1-color);
    flex-wrap: wrap;
  }
  
  .dept-tree-container {
    width: 100%;
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid var(--td-border-level-1-color);
    border-radius: var(--td-radius-default);
    padding: 8px;
    background-color: transparent;
    
    .empty-state {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100px;
      color: var(--td-text-color-placeholder);
      font-size: 14px;
    }
  }
}
</style>