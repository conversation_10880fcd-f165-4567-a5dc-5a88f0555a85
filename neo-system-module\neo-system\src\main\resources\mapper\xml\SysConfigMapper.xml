<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.neo.module.mapper.SysConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.neo.module.entity.SysConfig">

        <result column="config_name" property="configName"/>
        <result column="config_key" property="configKey"/>
        <result column="config_value" property="configValue"/>
        <result column="config_type" property="configType"/>
        <result column="remark" property="remark"/>
        <result column="id" property="id"/>
        <result column="is_deleted" property="deleted"/>
        <result column="create_time" property="createTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_by" property="updateBy"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,is_deleted,create_time,create_by,update_time,update_by,config_name, config_key, config_value, config_type, remark
    </sql>

</mapper>
