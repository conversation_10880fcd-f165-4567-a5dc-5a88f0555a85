package com.neo.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.neo.annotation.DataScope;
import com.neo.constant.CommonConstant;
import com.neo.context.DataScopeContext;
import com.neo.module.dto.SysRoleDTO;
import com.neo.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据权限服务
 * 实现根据用户角色获取数据权限范围的逻辑
 * 支持多角色权限OR逻辑连接
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class DataScopeService {

    @Resource
    private JdbcTemplate jdbcTemplate;

    /**
     * 数据权限范围常量
     */
    public static final String DATA_SCOPE_ALL = "1";          // 全部数据权限
    public static final String DATA_SCOPE_CUSTOM = "2";       // 自定数据权限  
    public static final String DATA_SCOPE_DEPT = "3";         // 本部门数据权限
    public static final String DATA_SCOPE_DEPT_AND_CHILD = "4"; // 本部门及以下数据权限

    /**
     * 获取当前用户的数据权限SQL
     *
     * @param dataScope 数据权限注解
     * @return 数据权限SQL片段
     */
    public String getDataScopeSql(DataScope dataScope) {
        try {
            // 获取当前登录用户
            SysUserDetail userDetail = SecurityUtils.getLoginUser();
            if (userDetail == null) {
                log.warn("获取数据权限失败：用户未登录");
                return " 1=0 ";
            }

            // 超管直接放行
            if (CommonConstant.ADMIN_USER_ID.equals(userDetail.getUserId())) {
                return "";
            }

            // 设置数据权限上下文
            DataScopeContext context = DataScopeContext.getContext();
            context.setUserId(userDetail.getUserId());
            context.setDeptId(userDetail.getDeptId());
            context.setDataScopeAnnotation(dataScope);
            
            // 根据多个角色权限范围生成OR连接的SQL
            return generateMultiRoleDataScopeSql(userDetail, dataScope);
        } catch (Exception e) {
            log.error("获取数据权限SQL失败", e);
            return " 1=0 ";
        }
    }

    /**
     * 获取用户所有角色的数据权限范围列表
     *
     * @param userDetail 用户详情
     * @return 数据权限范围列表
     */
    private List<String> getUserRoleDataScopes(SysUserDetail userDetail) {
        List<SysRoleDTO> roles = userDetail.getRoleDTOS();
        if (CollUtil.isEmpty(roles)) {
            return Collections.singletonList(DATA_SCOPE_DEPT);
        }

        // 查询用户所有角色的数据权限范围
        List<String> roleIds = roles.stream()
            .map(SysRoleDTO::getId)
            .collect(Collectors.toList());
        
        if (CollUtil.isEmpty(roleIds)) {
            return Collections.singletonList(DATA_SCOPE_DEPT);
        }

        String sql = "SELECT DISTINCT data_scope FROM sys_role WHERE id IN (" +
                     String.join(",", Collections.nCopies(roleIds.size(), "?")) + ") AND status = '0'";

        List<String> dataScopes = roles.stream().map(SysRoleDTO::getDataScope).collect(Collectors.toList());
        return CollUtil.isEmpty(dataScopes) ? Collections.singletonList(DATA_SCOPE_DEPT) : dataScopes;
    }

    /**
     * 根据多个角色的数据权限范围生成OR连接的SQL片段
     *
     * @param userDetail 用户详情
     * @param annotation 数据权限注解
     * @return SQL片段
     */
    private String generateMultiRoleDataScopeSql(SysUserDetail userDetail, DataScope annotation) {
        List<SysRoleDTO> roles = userDetail.getRoleDTOS();

        if (CollUtil.isEmpty(roles)) {
            return " 1=0 ";
        }
        
        // 如果只有一个权限范围，直接生成单个条件
        if (roles.size() == 1) {
            return generateSingleDataScopeSql(roles.get(0), userDetail, annotation);
        }
        
        // 多个权限范围用OR连接
        List<String> conditions = roles.stream()
            .map(role -> generateSingleDataScopeSql(role, userDetail, annotation))
            .filter(StrUtil::isNotBlank)
            .collect(Collectors.toList());
        
        if (conditions.isEmpty()) {
            return " 1=0 ";
        }
        
        if (conditions.size() == 1) {
            return conditions.get(0);
        }
        
        // 用OR连接多个条件
        return " AND (" + conditions.stream()
            .map(condition -> condition.replaceFirst("^ AND ", ""))
            .map(condition -> "(" + condition + ")")
            .collect(Collectors.joining(" OR ")) + ")";
    }
    
    /**
     * 根据单个数据权限范围生成SQL片段
     *
     * @param sysRoleDTO  角色详情
     * @param userDetail 用户详情
     * @param annotation 数据权限注解
     * @return SQL片段
     */
    private String generateSingleDataScopeSql(SysRoleDTO sysRoleDTO, SysUserDetail userDetail, DataScope annotation) {
        String permission = annotation.permission();
        String deptAlias = StrUtil.isNotBlank(annotation.deptAlias()) ? annotation.deptAlias() + "." : "";
        String userAlias = StrUtil.isNotBlank(annotation.userAlias()) ? annotation.userAlias() + "." : "";

        StringBuilder sql = new StringBuilder();

        switch (sysRoleDTO.getDataScope()) {
            case DATA_SCOPE_ALL:
                // 全部数据权限：不添加任何条件
                break;
                
            case DATA_SCOPE_CUSTOM:
                // 自定数据权限：根据角色关联的部门进行过滤
                List<String> customDeptIds = getCustomDeptIds(sysRoleDTO.getId());
                if (CollUtil.isNotEmpty(customDeptIds)) {
                    sql.append(" AND (").append(deptAlias).append(permission).append(" IN (");
                    sql.append(customDeptIds.stream()
                        .map(id -> "'" + id + "'")
                        .collect(Collectors.joining(",")));
                    sql.append(")");
                } else {
                    sql.append(" AND 1=0"); // 无自定义权限时拒绝访问
                }
                break;
                
            case DATA_SCOPE_DEPT:
                // 本部门数据权限：只能查看本部门数据
                sql.append(" AND (").append(deptAlias).append(permission).append(" = '")
                   .append(userDetail.getDeptId()).append("'");
                sql.append(")");
                break;
                
            case DATA_SCOPE_DEPT_AND_CHILD:
                // 本部门及以下数据权限：可以查看本部门及子部门数据
                List<String> childDeptIds = getDeptAndChildIds(userDetail.getDeptId());
                if (CollUtil.isNotEmpty(childDeptIds)) {
                    sql.append(" AND (").append(deptAlias).append(permission).append(" IN (");
                    sql.append(childDeptIds.stream()
                        .map(id -> "'" + id + "'")
                        .collect(Collectors.joining(",")));
                    sql.append(")");
                } else {
                    // 如果查询不到子部门，至少保证本部门权限
                    sql.append(" AND (").append(deptAlias).append(permission).append(" = '")
                       .append(userDetail.getDeptId()).append("'");
                    sql.append(")");
                }
                break;
                
            default:
                // 默认本部门权限
                sql.append(" AND (").append(deptAlias).append(permission).append(" = '")
                   .append(userDetail.getDeptId()).append("')");
                break;
        }

        return sql.toString();
    }

    /**
     * 获取用户自定义权限的部门ID列表
     *
     * @param roleId 角色id
     * @return 部门ID列表
     */
    private List<String> getCustomDeptIds(String roleId) {
        try {
            String sql = "SELECT id FROM sys_role_dept where role_id = ? ";
            return jdbcTemplate.queryForList(sql, String.class, roleId);
        } catch (Exception e) {
            log.error("查询用户自定义部门权限失败，roleId: {}", roleId, e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取部门及其所有子部门的ID列表
     *
     * @param deptId 部门ID
     * @return 部门ID列表（包含本部门和所有子部门）
     */
    private List<String> getDeptAndChildIds(String deptId) {
        try {
            String sql = "SELECT id FROM sys_dept WHERE status = '0' AND (id = ? OR ancestors LIKE ?)";
            String ancestorsPattern = "%," + deptId + ",%";
            return jdbcTemplate.queryForList(sql, String.class, deptId, ancestorsPattern);
        } catch (Exception e) {
            log.error("查询部门及子部门失败，deptId: {}", deptId, e);
            return Collections.singletonList(deptId); // 至少返回本部门
        }
    }

    /**
     * 清除数据权限上下文
     */
    public void clearDataScope() {
        DataScopeContext.clearContext();
    }
}