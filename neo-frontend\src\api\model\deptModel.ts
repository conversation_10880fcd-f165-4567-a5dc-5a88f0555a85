// 部门基础实体
export interface SysDept {
  id?: string;
  parentId?: string;
  ancestors?: string;
  name: string;
  sort?: number;
  leader?: string;
  phone?: string;
  email?: string;
  status?: string;
  createTime?: string;
  updateTime?: string;
}

// 部门树形结构
export interface SysDeptTree extends SysDept {
  children?: SysDeptTree[];
}

// 部门分页查询参数
export interface DeptPageParams {
  current?: number;
  size?: number;
  page?: number;
  pageSize?: number;
  name?: string;
  status?: string;
  leader?: string;
}

// 部门列表响应
export interface DeptListResult {
  records: SysDept[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

// 部门树形结果
export interface DeptTreeResult {
  list: Array<SysDeptTree>;
}