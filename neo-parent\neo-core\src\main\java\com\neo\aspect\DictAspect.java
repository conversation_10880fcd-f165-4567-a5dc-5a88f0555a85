package com.neo.aspect;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.neo.aspect.annontation.Dict;
import com.neo.constant.CommonConstant;
import com.neo.model.Result;
import com.neo.service.DictService;
import com.neo.utils.ConvertUtils;
import com.neo.utils.RedisUtils;
import com.neo.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.*;

@Aspect
@Component
@Slf4j
public class DictAspect {

    @Resource
    private DictService dictService;

    @Resource
    private RedisUtils redisUtils;

    /**
     * 切点，切入 controller 包下面的所有方法
     */
    @Pointcut("execution( * com.neo.*.*controller.*.*(..))")
    public void dict() {

    }

    @Around("dict()")
    public Object doAround(ProceedingJoinPoint pjp) throws Throwable {
        long time1 = System.currentTimeMillis();
        Object result = pjp.proceed();
        long time2 = System.currentTimeMillis();
        log.debug("获取JSON数据 耗时：{}ms", time2 - time1);
        long start = System.currentTimeMillis();
        this.parseDictText(result);
        long end = System.currentTimeMillis();
        log.debug("解析注入JSON数据 耗时{}ms", end - start);
        return result;
    }

    private void parseDictText(Object resultObj) {
        try {
            if (!(resultObj instanceof Result)){
                return;
            }
            Object data = ((Result) resultObj).getData();
            if (data == null) {
                return;
            }

            if (data instanceof IPage){
                List<JSONObject> records = new ArrayList<>();
                for (Object record : ((IPage) data).getRecords()) {
                    JSONObject item = this.eachField(record);
                    records.add(item);
                }
                ((IPage) data).setRecords(records);
            }else if (data instanceof List){
                List<JSONObject> list = new ArrayList<>();
                List dataList = (List) data;
                for (Object record : dataList) {
                    JSONObject item = this.eachField(record);
                    list.add(item);
                }
                ((Result) resultObj).setData(list);
            }else {
                JSONObject item = this.eachField(data);
                ((Result) resultObj).setData(item);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    private JSONObject eachField(Object record) {
        ObjectMapper mapper = new ObjectMapper();
        JSONObject item = new JSONObject();
        try {
            // 使用Jackson直接处理对象，避免JSON字符串转换过程中的字符编码问题
            JsonNode jsonNode = mapper.valueToTree(record);
            // 将JsonNode转换为Map，然后放入JSONObject中
            Map<String, Object> map = mapper.convertValue(jsonNode, Map.class);
            item.putAll(map);
        } catch (Exception e) {
            log.error("请求入参序列化失败：" + e.getMessage(), e);
            return item; // 返回空的JSONObject而不是继续处理
        }

        for (Field field : ConvertUtils.getAllFields(record.getClass())) {
            if (field.isAnnotationPresent(Dict.class)) {
                Dict dict = field.getAnnotation(Dict.class);
                String dictCode = dict.dictCode();
                String dictTable = dict.dictTable();
                String dictText = dict.dictText();
                String key = item.getString(field.getName());

                String textValue = this.translateDictValue(dictCode,dictTable,dictText,key);
                item.put(field.getName()+ CommonConstant.DICT_SUFFIX,textValue);
            }
        }
        return item;
    }

    private String translateDictValue(String dictCode, String dictTable, String dictText,String key) {
        if (StringUtils.isBlank(key)){
            return key;
        }
        return dictService.selectDict(dictCode,dictTable,dictText,key);
    }

}