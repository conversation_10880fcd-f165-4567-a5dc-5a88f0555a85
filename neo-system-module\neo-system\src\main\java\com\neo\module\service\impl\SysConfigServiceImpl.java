package com.neo.module.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.common.QueryGenerator;
import com.neo.module.entity.SysConfig;
import com.neo.module.mapper.SysConfigMapper;
import com.neo.module.service.SysConfigService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 系统配置 服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
@Service
public class SysConfigServiceImpl extends ServiceImpl<SysConfigMapper, SysConfig> implements SysConfigService {

    @Resource
    private SysConfigMapper sysConfigMapper;

    @Override
    public IPage<SysConfig> page(Map<String, String> params, QueryWrapper<SysConfig> queryWrapper) {
        String value;
        queryWrapper
                .eq(StringUtils.isNotBlank((value = params.get("configName"))), "config_name", value)
                .eq(StringUtils.isNotBlank((value = params.get("configKey"))), "config_key", value)
                .eq(StringUtils.isNotBlank((value = params.get("configValue"))), "config_value", value)
                .eq(StringUtils.isNotBlank((value = params.get("configType"))), "config_type", value)
                .eq(StringUtils.isNotBlank((value = params.get("remark"))), "remark", value)
        ;
        IPage<SysConfig> page = sysConfigMapper.selectPage(QueryGenerator.initPage(params), queryWrapper);
        return page;
    }

    @Override
    public List<SysConfig> list(SysConfig sysConfig) {
        QueryWrapper<SysConfig> queryWrapper = new QueryWrapper<>(sysConfig);
        List<SysConfig> sysConfigs = sysConfigMapper.selectList(queryWrapper);
        return sysConfigs;
    }

    @Override
    public SysConfig getConfigByKey(String configKey) {
        LambdaQueryWrapper<SysConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysConfig::getConfigKey,configKey);
        return sysConfigMapper.selectOne(queryWrapper);
    }

}
