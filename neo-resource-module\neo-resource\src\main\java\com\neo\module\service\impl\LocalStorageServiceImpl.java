package com.neo.module.service.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.neo.exception.BusinessException;
import com.neo.module.config.properties.FileConfigProperties;
import com.neo.module.entity.SysAttachment;
import com.neo.module.entity.vo.FileVO;
import com.neo.module.service.SysAttachmentService;
import com.neo.module.service.StorageService;
import com.neo.module.utils.FileUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

@Slf4j
@Service("localStorageService")
@RequiredArgsConstructor
public class LocalStorageServiceImpl implements StorageService {

    private final FileConfigProperties properties;
    private final SysAttachmentService sysAttachmentService;

    @Override
    public FileVO upload(MultipartFile file) {
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
            String id = IdWorker.getIdStr();
            String name = FileUtils.upload(properties.getLocal().getPath(),file.getOriginalFilename(), inputStream);
            String url = properties.getDomain() + properties.getPrefix() + "/" + name;

            FileVO fileVO = new FileVO();
            fileVO.setId(id);
            fileVO.setFileName(file.getOriginalFilename());
            fileVO.setUrl(url);

            SysAttachment attachment = new SysAttachment();
            attachment.setId(id);
            attachment.setFileName(fileVO.getFileName());
            attachment.setFilePath(name);  // 只存储相对路径
            attachment.setFileSize(file.getSize());
            attachment.setFileUrl(fileVO.getUrl());
            attachment.setFileType(FileUtils.getExtension(fileVO.getFileName()));
            attachment.setStorageType("local");
            attachment.setMd5(DigestUtils.md5Hex(inputStream));
            attachment.setStatus(true);

            sysAttachmentService.save(attachment);
            return fileVO;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException("上传失败");
        } finally {
            if (inputStream != null){
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error(e.getMessage(),e);
                }
            }
        }
    }

    @Override
    public void download(String filePath, HttpServletResponse response) throws Exception {
        String realPath = properties.getLocal().getPath() + File.separator + filePath;
        Path path = Paths.get(realPath);
        if (!Files.exists(path)) {
            throw new FileNotFoundException("文件不存在");
        }

        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;filename=" + filePath);

        try (InputStream inputStream = Files.newInputStream(path);
             OutputStream outputStream = response.getOutputStream()) {
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            outputStream.flush();
        }
    }

    @Override
    public boolean delete(String filePath) throws Exception {
        String realPath = properties.getLocal().getPath() + File.separator + filePath;
        Path path = Paths.get(realPath);
        return Files.deleteIfExists(path);
    }

    @Override
    public InputStream getFileStream(String filePath) throws Exception {
        String realPath = properties.getLocal().getPath() + File.separator + filePath;
        Path path = Paths.get(realPath);
        if (!Files.exists(path)) {
            throw new FileNotFoundException("文件不存在");
        }
        return Files.newInputStream(path);
    }

}

