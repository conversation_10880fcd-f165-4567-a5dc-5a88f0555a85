<template>
  <t-dialog
    v-model:visible="dialogVisible"
    header="字典项管理"
    width="80%"
    :footer="false"
    @closed="onDialogClosed"
  >
    <template v-if="dialogVisible">
      <div class="dict-item-header">
        <div class="left-operation-container">
          <t-button theme="primary" @click="handleAddDictItem">
            <template #icon> <add-icon /></template>
            新增字典项
          </t-button>
          <t-button theme="danger" :disabled="!selectedRowKeys.length" @click="handleDeleteDictItem">
            <template #icon> <delete-icon /></template>
            删除字典项
          </t-button>
        </div>
      </div>

      <t-table
        :data="dataSource"
        :columns="columns"
        :row-key="rowKey"
        :selected-row-keys="selectedRowKeys"
        :loading="loading"
        :pagination="pagination"
        vertical-align="top"
        :maxHeight="getTableHeight(200)"
        @page-change="handlePageChange"
        @select-change="onSelectChange"
      >
        <template #status="{ row }">
          <t-tag v-if="row.status === '1'" theme="success" variant="light">启用</t-tag>
          <t-tag v-else theme="danger" variant="light">禁用</t-tag>
        </template>
        <template #op="slotProps">
          <t-space>
            <t-link theme="primary" @click="handleEdit(slotProps.row)">编辑</t-link>
            <t-popconfirm 
              content="确定要删除吗？" 
              @confirm="handleDelete(slotProps.row)"
            >
              <t-link theme="danger">删除</t-link>
            </t-popconfirm>
          </t-space>
        </template>
      </t-table>
    </template>

    <!-- 字典项编辑弹窗 -->
    <EditDictItemModel
      v-model:visible="itemFormVisible" 
      :is-edit="isEdit" 
      :dict-item-data="editDictItemData"
      :dict-id="dictData.id"
      :dict-code="dictData.dictCode"
      @success="onFormSuccess"
    />
  </t-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import type { PageInfo, PrimaryTableCol } from 'tdesign-vue-next';
import { AddIcon, DeleteIcon } from 'tdesign-icons-vue-next';
import { getDictItemPage, deleteDictItem } from '@/api/dict';
import type { SysDict, SysDictItem, DictItemPageParams } from '@/api/model/dictModel';
import EditDictItemModel from './EditDictItemModel.vue';

// 定义 props
interface Props {
  visible: boolean;
  dictData: SysDict;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  dictData: () => ({})
});

// 定义 emits
const emit = defineEmits<{
  'update:visible': [value: boolean];
}>();

// 表格高度计算函数
const getTableHeight = (offset = 100) => {
  const clientHeight = document.documentElement.clientHeight || document.body.clientHeight;
  return clientHeight - offset;
};

// 定义表格列
const columns: PrimaryTableCol[] = [
  { colKey: 'row-select', type: 'multiple' as const, width: 60, fixed: 'left' as const },
  { title: '字典项文本', colKey: 'itemText', width: 150, ellipsis: true },
  { title: '字典项值', colKey: 'itemValue', width: 150, ellipsis: true },
  { title: '排序', colKey: 'sort', width: 100 },
  { title: '状态', colKey: 'status', width: 100 },
  { title: '描述', colKey: 'description', width: 200, ellipsis: true },
  { title: '创建时间', colKey: 'createTime', width: 200 },
  { title: '操作', colKey: 'op', width: 150, fixed: 'right' as const },
];

// 响应式数据
const dataSource = ref<SysDictItem[]>([]);
const loading = ref(false);
const selectedRowKeys = ref<string[]>([]);
const rowKey = 'id';

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
});

// 弹窗相关
const itemFormVisible = ref(false);
const isEdit = ref(false);
const editDictItemData = ref<SysDictItem>();

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => emit('update:visible', value)
});

// 获取字典项列表
const fetchDictItemList = async () => {
  if (!props.dictData.id) return;
  
  loading.value = true;
  try {
    const params: DictItemPageParams = {
      dictId: props.dictData.id,
      current: pagination.current,
      pageSize: pagination.pageSize,
    };
    const res = await getDictItemPage(params);
    dataSource.value = res.records;
    pagination.total = res.total;
    pagination.current = res.current;
  } catch (error) {
    MessagePlugin.error('获取字典项列表失败');
  } finally {
    loading.value = false;
  }
};

// 查询
const searchQuery = () => {
  pagination.current = 1;
  fetchDictItemList();
};

// 分页变化
const handlePageChange = (pageInfo: PageInfo) => {
  pagination.current = pageInfo.current;
  pagination.pageSize = pageInfo.pageSize;
  fetchDictItemList();
};

// 选择变化
const onSelectChange = (selectedKeys: (string | number)[]) => {
  selectedRowKeys.value = selectedKeys as string[];
};

// 新增字典项
const handleAddDictItem = () => {
  isEdit.value = false;
  editDictItemData.value = undefined;
  itemFormVisible.value = true;
};

// 编辑字典项
const handleEdit = (row: SysDictItem) => {
  isEdit.value = true;
  editDictItemData.value = row;
  itemFormVisible.value = true;
};

// 删除单个字典项
const handleDelete = async (row: SysDictItem) => {
  try {
    await deleteDictItem([row.id || '']);
    MessagePlugin.success('删除成功');
    searchQuery();
  } catch (error) {
    MessagePlugin.error('删除失败');
  }
};

// 批量删除字典项
const handleDeleteDictItem = async () => {
  try {
    await deleteDictItem(selectedRowKeys.value);
    MessagePlugin.success('删除字典项成功');
    selectedRowKeys.value = [];
    searchQuery();
  } catch (error) {
    MessagePlugin.error('删除字典项失败');
  }
};

// 表单操作成功回调
const onFormSuccess = () => {
  searchQuery();
};

// 弹窗关闭处理
const onDialogClosed = () => {
  selectedRowKeys.value = [];
  dataSource.value = [];
  pagination.current = 1;
  pagination.pageSize = 10;
  pagination.total = 0;
};

// 监听弹窗显示状态
watch(() => props.visible, (newVisible) => {
  if (newVisible && props.dictData.id) {
    // 重置分页
    pagination.current = 1;
    pagination.pageSize = 10;
    fetchDictItemList();
  }
});
</script>

<style lang="less" scoped>
.dict-item-header {
  margin-bottom: 16px;

  .left-operation-container {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 16px;
  }

  .dict-info {
    padding: 16px;
    background-color: #f5f5f5;
    border-radius: 4px;
  }
}
</style>