export interface SysUser {
  id?: string;
  userName: string;
  nickName: string;
  userType?: string;
  avatar?: string;
  deptId?: string; // 修改为字符串类型，避免精度丢失
  email?: string;
  phoneNumber?: string;
  gender?: string;
  status?: string;
  roleIds?: string[];
  postIds?: string[];
  remark?: string;
  createTime?: string;
  updateTime?: string;
}

export interface UserListResult {
  list: Array<SysUser>;
}

export interface UserPageResult {
  records: Array<SysUser>;
  total: number;
  current: number;
  size: number;
}

export interface UserPageParams {
  current: number;
  pageSize: number;
  userName?: string;
  deptId?: string;
  filterUserName?: string;
  filterUserCode?: string;
  filterPhoneNumber?: string;
  filterStatus?: string;
  createBeginTime?: string;
  createendTime?: string;
}