//package com.neo.module.controller;
//
//import com.neo.model.Result;
//import com.neo.utils.MqUtil;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestParam;
//import org.springframework.web.bind.annotation.RestController;
//
//import javax.annotation.Resource;
//
///**
// * 消息队列控制器
// *
// * <AUTHOR>
// * @since 2025-09-04
// */
//@RestController
//@RequestMapping("/mq")
//public class MqController {
//
//    @Resource
//    private MqUtil mqUtil;
//
//    /**
//     * 发送消息到指定主题
//     *
//     * @param topic 主题
//     * @param message 消息内容
//     * @return 操作结果
//     */
//    @PostMapping("/send")
//    public Result<?> send(@RequestParam String topic, @RequestParam String message) {
//        mqUtil.sendMessage(topic, message);
//        return Result.ok("消息发送成功");
//    }
//
//    /**
//     * 发送延迟消息
//     *
//     * @param topic 主题
//     * @param message 消息内容
//     * @param delayTime 延迟时间(毫秒)
//     * @return 操作结果
//     */
//    @PostMapping("/sendDelay")
//    public Result<?> sendDelay(@RequestParam String topic,
//                             @RequestParam String message,
//                             @RequestParam Long delayTime) {
//        mqUtil.sendDelayMessage(topic, message, delayTime);
//        return Result.ok("延迟消息发送成功");
//    }
//
//    /**
//     * 发送带标签的消息
//     *
//     * @param topic 主题
//     * @param tag 标签
//     * @param message 消息内容
//     * @return 操作结果
//     */
//    @PostMapping("/sendTag")
//    public Result<?> sendTag(@RequestParam String topic,
//                           @RequestParam String tag,
//                           @RequestParam String message) {
//        mqUtil.sendTagMessage(topic, tag, message);
//        return Result.ok("带标签消息发送成功");
//    }
//}
