<template>
  <t-dialog
    v-model:visible="dialogVisible"
    :header="dialogTitle"
    :width="600"
    :confirm-btn="{ loading: formLoading }"
    @confirm="onConfirm"
    @cancel="onCancel"
  >
    <template v-if="dialogVisible">
      <t-form ref="formRef" :data="formData" :rules="formRules" label-align="right" :label-width="100" @submit="onSubmit">
        <t-form-item label="菜单类型" name="menuType">
          <t-radio-group v-model="formData.menuType">
            <t-radio value="M">目录</t-radio>
            <t-radio value="C">菜单</t-radio>
            <t-radio value="F">按钮</t-radio>
          </t-radio-group>
        </t-form-item>
        
        <t-form-item label="上级菜单" name="parentId">
          <t-tree-select
            v-model="formData.parentId"
            :data="menuSelectOptions"
            clearable
            placeholder="请选择上级菜单"
          />
        </t-form-item>
        
        <t-form-item label="菜单名称" name="name">
          <t-input v-model="formData.name" placeholder="请输入菜单名称" />
        </t-form-item>
        
        <t-form-item label="菜单排序" name="sort">
          <t-input-number v-model="formData.sort" />
        </t-form-item>
        
        <t-form-item v-if="formData.menuType !== 'F'" label="路由地址" name="path">
          <t-input v-model="formData.path" placeholder="请输入路由地址" />
        </t-form-item>
        
        <t-form-item v-if="formData.menuType === 'C'" label="组件路径" name="component">
          <t-input v-model="formData.component" placeholder="请输入组件路径" />
        </t-form-item>
        
        <t-form-item label="权限标识" name="permissionCode">
          <t-input v-model="formData.permissionCode" placeholder="请输入权限标识" />
        </t-form-item>
        
        <t-form-item label="显示状态" name="visible">
          <t-radio-group v-model="formData.visible">
            <t-radio value="1">显示</t-radio>
            <t-radio value="0">隐藏</t-radio>
          </t-radio-group>
        </t-form-item>
        
        <t-form-item label="菜单状态" name="status">
          <t-radio-group v-model="formData.status">
            <t-radio value="1">正常</t-radio>
            <t-radio value="0">停用</t-radio>
          </t-radio-group>
        </t-form-item>
        
        <t-form-item label="备注" name="remark">
          <t-textarea v-model="formData.remark" placeholder="请输入备注" />
        </t-form-item>
      </t-form>
    </template>
  </t-dialog>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { 
  addMenu, 
  updateMenu, 
  getMenuById,
  type SysMenu
} from '@/api/menu';

// 定义 props
interface Props {
  visible: boolean;
  isEdit: boolean;
  menuData?: SysMenu;
  menuSelectOptions: any[];
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  isEdit: false,
  menuData: undefined,
  menuSelectOptions: () => []
});

// 定义 emits
const emit = defineEmits<{
  'update:visible': [value: boolean];
  'success': [];
}>();

// 响应式数据
const formLoading = ref(false);
const formRef = ref();

const formData = ref<SysMenu>({
  menuType: 'M',
  visible: '1',
  status: '1',
  sort: 0,
  name: '',
  parentId: '',
  component: ''
});

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => emit('update:visible', value)
});

const dialogTitle = computed(() => {
  return props.isEdit ? '编辑菜单' : '新增菜单';
});

// 表单验证规则
const formRules = {
  name: [{ required: true, message: '请输入菜单名称' }],
  menuType: [{ required: true, message: '请选择菜单类型' }],
};

// 初始化表单数据
const initFormData = () => {
  formData.value = {
    menuType: 'M',
    visible: '1',
    status: '1',
    sort: 0,
    name: '',
    parentId: ''
  } as SysMenu;
};

// 加载菜单详情数据
const loadMenuDetail = async () => {
  if (props.isEdit && props.menuData?.id) {
    try {
      const res = await getMenuById(props.menuData.id);
      formData.value = res;
    } catch (error) {
      MessagePlugin.error('获取菜单详情失败');
    }
  }
};

// 监听弹窗显示状态
watch(() => props.visible, async (newVisible) => {
  if (newVisible) {
    initFormData();
    
    // 如果menuData包含parentId，说明是新增子菜单
    if (!props.isEdit && props.menuData?.parentId) {
      formData.value.parentId = props.menuData.parentId;
    } else if (props.isEdit && props.menuData) {
      // 编辑模式，加载菜单详情
      await loadMenuDetail();
    }
  }
});

// 确认按钮处理
const onConfirm = () => {
  formRef.value.submit();
};

// 取消按钮处理
const onCancel = () => {
  emit('update:visible', false);
};

// 表单提交处理
const onSubmit = async ({ validateResult, firstError }: any) => {
  if (validateResult === true) {
    formLoading.value = true;
    
    try {
      if (props.isEdit) {
        await updateMenu(formData.value);
        MessagePlugin.success('更新成功');
      } else {
        await addMenu(formData.value);
        MessagePlugin.success('新增成功');
      }
      
      emit('update:visible', false);
      emit('success');
    } catch (error) {
      MessagePlugin.error(props.isEdit ? '更新失败' : '新增失败');
    } finally {
      formLoading.value = false;
    }
  } else {
    console.log('Errors: ', validateResult);
    MessagePlugin.warning(firstError);
  }
};
</script>

<style lang="less" scoped>
// 表单样式优化
:deep(.t-dialog__body) {
  max-height: 500px;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 16px 24px;
}

:deep(.t-dialog) {
  .t-form {
    overflow: visible;
    width: 100%;
    box-sizing: border-box;
  }
  
  .t-form-item {
    margin-bottom: 16px;
  }
  
  .t-input,
  .t-input-number,
  .t-tree-select,
  .t-radio-group,
  .t-textarea {
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
  }
}
</style>