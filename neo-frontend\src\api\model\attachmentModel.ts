export interface SysAttachment {
  id?: string;
  fileName?: string;        // 文件名称
  filePath?: string;        // 文件存储路径
  fileSize?: number;        // 文件大小(字节)
  fileUrl?: string;         // 文件访问地址
  fileType?: string;        // 文件类型/后缀名
  storageType?: string;     // 存储类型(LOCAL:本地存储, OSS:对象存储)
  md5?: string;             // 文件MD5值
  status?: boolean;         // 状态(false:禁用,true:正常)
  createTime?: string;      // 创建时间
  updateTime?: string;      // 更新时间
  createBy?: string;        // 创建人
  updateBy?: string;        // 更新人
}

export interface AttachmentPageParams {
  current: number;
  pageSize: number;
  fileName?: string;        // 文件名称
  fileType?: string;        // 文件类型
  storageType?: string;     // 存储类型
  status?: boolean;         // 状态
  createBeginTime?: string; // 创建开始时间
  createEndTime?: string;   // 创建结束时间
}

export interface AttachmentListResult {
  records: SysAttachment[];
  total: number;
  current: number;
  size: number;
  pages: number;
}

export interface FileUploadResult {
  id: string;
  fileName: string;
  fileUrl: string;
  fileSize: number;
  fileType: string;
}
