package com.neo.module.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.neo.module.entity.SysDictItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 数据字典项 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
@Mapper
public interface SysDictItemMapper extends BaseMapper<SysDictItem> {

    String selectByDictTable(@Param("dictCode") String dictCode, @Param("value") String value);

    String selectByOtherTable(@Param("dictCode") String dictCode, @Param("dictText") String dictText, @Param("dictTable") String dictTable,@Param("value") String value);

}
