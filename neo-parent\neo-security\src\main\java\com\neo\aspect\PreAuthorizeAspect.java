package com.neo.aspect;

import com.neo.aspect.annontation.PreLogin;
import com.neo.aspect.annontation.PrePermissions;
import com.neo.aspect.annontation.PreRoles;
import com.neo.utils.SecurityUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

@Aspect
@Component
public class PreAuthorizeAspect {

    @Pointcut(" @annotation(com.neo.aspect.annontation.PreLogin)" +
            "@annotation(com.neo.aspect.annontation.PreRoles) ||" +
            "@annotation(com.neo.aspect.annontation.PrePermissions)")
    public void PreAuthorizeCut() {

    }

    @Around("PreAuthorizeCut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        // 注解鉴权
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        checkMethodAnnotation(signature.getMethod());
        try {
            // 执行原有逻辑
            Object obj = joinPoint.proceed();
            return obj;
        } catch (Throwable e) {
            throw e;
        }
    }

    public void checkMethodAnnotation(Method method) {

        // 校验 @PreLogin 注解
        PreLogin preLogin = method.getAnnotation(PreLogin.class);
        if (preLogin != null) {
            SecurityUtils.checkLogin();
        }

        // 校验 @PreRoles 注解
        PreRoles preRoles = method.getAnnotation(PreRoles.class);
        if (preRoles != null) {
            SecurityUtils.checkRole(preRoles);
        }

        // 校验 @PrePermissions 注解
        PrePermissions prePermissions = method.getAnnotation(PrePermissions.class);
        if (prePermissions != null) {
            SecurityUtils.checkPermission(prePermissions);
        }

    }

}
