spring:
  servlet:
    multipart:
      file-size-threshold: 2KB
      enabled: true
      # 整个请求大小限制
      max-request-size: 20MB
      # 上传单个文件大小限制
      max-file-size: 10MB
  mvc:
    format:
      date-time: yyyy-MM-dd HH:mm:ss
  #jackson配置
  jackson:
    # 日期格式化
    date-format: yyyy-MM-dd HH:mm:ss
    serialization:
      # 格式化输出
      INDENT_OUTPUT: false
      # 忽略无法转换的对象
      fail_on_empty_beans: false
    deserialization:
      # 允许对象忽略json中不存在的属性
      fail_on_unknown_properties: false
  # redis通用配置 子服务可以自行配置进行覆盖
  redis:
    host: **************
    port: 16379
    # 密码(如没有密码请注释掉)
    password: l49cqVAFJlj7IUHU
    database: 0
    timeout: 10s
    ssl: false
  elasticsearch:
    rest:
      uris: **************:9200

# MyBatisPlus配置
# https://baomidou.com/config/
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl #开启sql日志
  # 对应的 XML 文件位置
  mapperPackage: com.neo.**.mapper
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.neo.**.entity
  global-config:
    # 是否打印 Logo banner
    banner: false
    db-config:
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)

dubbo:
  application:
    logger: slf4j
    # 元数据中心 local 本地 remote 远程 这里使用远程便于其他服务获取
    metadataType: remote
    # 可选值 interface、instance、all，默认是 all，即接口级地址、应用级地址都注册
    register-mode: instance
    service-discovery:
      # FORCE_INTERFACE，只消费接口级地址，如无地址则报错，单订阅 2.x 地址
      # APPLICATION_FIRST，智能决策接口级/应用级地址，双订阅
      # FORCE_APPLICATION，只消费应用级地址，如无地址则报错，单订阅 3.x 地址
      migration: FORCE_APPLICATION
    qos-enable: false
  metadata:
    report:
      expire-time: 86400000
  protocol:
    # 设置为 tri 即可使用 Triple 3.0 新协议
    # 性能对比 dubbo 协议并没有提升 但基于 http2 用于多语言异构等 http 交互场景
    # 使用 dubbo 协议通信
    name: dubbo
    # dubbo 协议端口(-1表示自增端口,从20880开始)
    port: -1
    # 指定dubbo协议注册ip
    # host: *************
  # 注册中心配置
  registry:
    address: nacos://${spring.cloud.nacos.server-addr}
    group: DUBBO_GROUP
    parameters:
      namespace: ${spring.profiles.active}
  # 消费者相关配置
  consumer:
    # 结果缓存(LRU算法)
    # 会有数据不一致问题 建议在注解局部开启
    cache: false
    # 支持校验注解
    validation: true
    # 超时时间
    timeout: 3000
    # 初始化检查
    check: false
  scan:
    # 接口实现类扫描
    base-packages: com.neo.**.dubbo
  # 自定义配置
  custom:
    # 全局请求log
    request-log: true
    # info 基础信息 param 参数信息 full 全部
    log-level: info

rocketmq:
  consumer:
    group: neo-consumer-group
    # 一次拉取消息最大值，注意是拉取消息的最大值而非消费最大值
    pull-batch-size: 10
  name-server: **************:9876
  producer:
    # 发送同一类消息的设置为同一个group，保证唯一
    group: neo-producer-group
    # 发送消息超时时间，默认3000
    sendMessageTimeout: 10000
    # 发送消息失败重试次数，默认2
    retryTimesWhenSendFailed: 2
    # 异步消息重试此处，默认2
    retryTimesWhenSendAsyncFailed: 2
    # 消息最大长度，默认1024 * 1024 * 4(默认4M)
    maxMessageSize: 4096
    # 压缩消息阈值，默认4k(1024 * 4)
    compressMessageBodyThreshold: 4096
    # 是否在内部发送失败时重试另一个broker，默认false
    retryNextServer: false

# springdoc-openai 配置
springdoc:
  api-docs:
    # 是否开启接口文档
    enabled: true
    # OpenAPI文档的路径
    path: /v3/api-docs
  swagger-ui:
    # swagger-ui路径
    path: /swagger-ui.html
    # 持久化认证数据
    persistAuthorization: true
  info:
    # 标题
    title: '标题：SpringDoc后台管理系统_接口文档'
    # 描述
    description: '描述：用于管理集团旗下公司的人员信息,具体包括XXX,XXX模块...'
    # 版本
    version: '版本号: 1.0.0'
    # 作者信息
    contact:
      name: hwj
      email: <EMAIL>
      url: https://gitee.com
  components:
    # 鉴权方式配置
    security-schemes:
      apiKey:
        type: APIKEY
        in: HEADER
        name: Authorization
  # 分组配置
  group-configs:
    - group: 默认接口文档
      packages-to-scan: com.neo

xxl:
  job:
    admin:
      addresses: http://**************:9900/xxl-job-admin
    executor:
      appname: ${spring.application.name}
      address:
      ip:
      port: -1
      logpath: /logs/xxl-job/jobhandler
      logretentiondays: 30
    accessToken: default_token

file:
  prefix: /files
  domain: http://localhost:5003
  storage-type: oss
  local:
    path: D:/home/<USER>
  oss:
    endpoint: http://**************:9000
    access-key: n1LpYmCz6am916Z5n1hl
    secret-key: tZDUrU7qLTJYtICkB8VcxydWi9jmXUsY9IC0txwX
    bucket: neo