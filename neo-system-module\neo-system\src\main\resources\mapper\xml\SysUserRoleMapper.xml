<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.neo.module.mapper.SysUserRoleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.neo.module.entity.SysUserRole">
        <result column="user_id" property="userId" />
        <result column="role_id" property="roleId" />
    </resultMap>
    <select id="selectRolesByUserId" resultType="java.lang.String">
        SELECT r.role_key
        FROM sys_role r
                 JOIN sys_user_role ur ON r.id = ur.role_id
        WHERE ur.user_id = #{userId}
    </select>

</mapper>
