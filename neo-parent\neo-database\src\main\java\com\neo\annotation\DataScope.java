package com.neo.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 数据权限过滤注解
 * 用于标记需要进行数据权限过滤的方法
 * 
 * <AUTHOR>
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DataScope {

    /**
     * 部门表的别名
     * 默认为空，表示自动识别
     */
    String deptAlias() default "";

    /**
     * 用户表的别名  
     * 默认为空，表示自动识别
     */
    String userAlias() default "";

    /**
     * 权限字段名
     * 默认dept_id
     */
    String permission() default "dept_id";

}