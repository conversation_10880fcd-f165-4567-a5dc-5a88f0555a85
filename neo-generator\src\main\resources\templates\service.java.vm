package ${package.Service};

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import ${package.Entity}.${entity};
import ${superServiceClassPackage};

import java.util.List;
import java.util.Map;

/**
 * $!{table.comment} 服务类
 * <AUTHOR>
 * @since ${date}
 */
#if(${kotlin})
interface ${table.serviceName} : ${superServiceClass}<${entity}>
#else
public interface ${table.serviceName} extends

    ${superServiceClass}<${entity}> {

    IPage<${table.entityName}> page (Map < String, String > params, QueryWrapper <${entity}>queryWrapper);

    List<${table.entityName}> list (${table.entityName} ${table.entityPath});

}
#end
