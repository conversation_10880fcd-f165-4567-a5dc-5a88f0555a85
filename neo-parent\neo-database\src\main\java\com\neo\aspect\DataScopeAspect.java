package com.neo.aspect;

import cn.hutool.core.util.StrUtil;
import com.neo.annotation.DataScope;
import com.neo.context.DataScopeContext;
import com.neo.service.DataScopeService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * 数据权限AOP切面
 * 拦截带有@DataScope注解的方法，实现数据权限过滤
 * 
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
public class DataScopeAspect {

    @Autowired
    private DataScopeService dataScopeService;

    /**
     * 定义切入点：拦截带有@DataScope注解的方法
     */
    @Pointcut("@annotation(com.neo.annotation.DataScope)")
    public void dataScopePointcut() {
        // 切入点方法
    }

    /**
     * 方法执行前处理数据权限
     */
    @Before("dataScopePointcut()")
    public void before(JoinPoint joinPoint) {
        try {
            // 获取方法和注解
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            DataScope dataScope = method.getAnnotation(DataScope.class);
            
            if (dataScope == null) {
                return;
            }

            log.debug("开始处理数据权限，方法：{}", method.getName());

            // 生成数据权限SQL并设置到上下文
            String dataScopeSql = dataScopeService.getDataScopeSql(dataScope);
            
            DataScopeContext context = DataScopeContext.getContext();
            context.setDataScopeSql(dataScopeSql);
            
            if (StrUtil.isNotBlank(dataScopeSql)) {
                log.debug("数据权限SQL：{}", dataScopeSql);
            }
            
        } catch (Exception e) {
            log.error("处理数据权限失败，方法：{}", joinPoint.getSignature().getName(), e);
            // 异常时设置清除标志，确保不影响正常业务
            DataScopeContext context = DataScopeContext.getContext();
            context.setDataScopeClearFlag(true);
        }
    }

    /**
     * 方法执行后清理数据权限上下文
     */
    @After("dataScopePointcut()")
    public void after(JoinPoint joinPoint) {
        try {
            log.debug("清理数据权限上下文，方法：{}", joinPoint.getSignature().getName());
            DataScopeContext.clearContext();
        } catch (Exception e) {
            log.error("清理数据权限上下文失败，方法：{}", joinPoint.getSignature().getName(), e);
        }
    }

}