package com.neo.module.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.crypto.digest.BCrypt;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.common.QueryGenerator;
import com.neo.exception.BusinessException;
import com.neo.module.dto.SysUserDTO;
import com.neo.module.entity.SysUser;
import com.neo.module.entity.SysUserPost;
import com.neo.module.entity.SysUserRole;
import com.neo.module.entity.vo.SysUserVO;
import com.neo.module.mapper.SysUserMapper;
import com.neo.module.mapper.SysUserPostMapper;
import com.neo.module.mapper.SysUserRoleMapper;
import com.neo.module.service.SysMenuService;
import com.neo.module.service.SysPostService;
import com.neo.module.service.SysRoleService;
import com.neo.module.service.SysUserService;
import org.springframework.beans.BeanUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2024-07-08
 */
@Service
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements SysUserService {

    @Resource
    private SysUserMapper sysUserMapper;
    @Resource
    private SysRoleService sysRoleService;
    @Resource
    private SysMenuService sysMenuService;
    @Resource
    private SysPostService sysPostService;
    @Resource
    private SysUserPostMapper sysUserPostMapper;
    @Resource
    private SysUserRoleMapper sysUserRoleMapper;
    @Resource
    private JdbcTemplate jdbcTemplate;

    @Override
    public IPage<SysUser> page(Map<String, String> params, QueryWrapper<SysUser> queryWrapper) {
        String value;
        queryWrapper
                .eq(StringUtils.isNotBlank((value = params.get("userName"))), "user_name", value)
                .eq(StringUtils.isNotBlank((value = params.get("nickName"))), "nick_name", value)
                .eq(StringUtils.isNotBlank((value = params.get("password"))), "password", value)
                .eq(StringUtils.isNotBlank((value = params.get("userType"))), "user_type", value)
                .eq(StringUtils.isNotBlank((value = params.get("avatar"))), "avatar", value)
                .eq(StringUtils.isNotBlank((value = params.get("deptId"))), "dept_id", value)
                .eq(StringUtils.isNotBlank((value = params.get("email"))), "email", value)
                .eq(StringUtils.isNotBlank((value = params.get("phoneNumber"))), "phone_number", value)
                .eq(StringUtils.isNotBlank((value = params.get("gender"))), "gender", value)
                .eq(StringUtils.isNotBlank((value = params.get("status"))), "status", value)
                .ge(StringUtils.isNotBlank((value = params.get("createBeginTime"))), "create_time", value)
                .le(StringUtils.isNotBlank((value = params.get("createEndTime"))), "create_time", value)
        ;
        IPage<SysUser> page = sysUserMapper.selectPage(QueryGenerator.initPage(params), queryWrapper);
        return page;
    }

    @Override
    public SysUserDTO getUserByUsername(String username) {
        SysUser sysUser = this.getOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getUserName, username));
        if (sysUser == null) {
            return null;
        }
        SysUserDTO sysUserDTO = new SysUserDTO();
        BeanUtil.copyProperties(sysUser, sysUserDTO);
        if (sysUser.isAdmin()){
            sysUserDTO.setRoles(Collections.singletonList("*"));
            sysUserDTO.setPermissions(Collections.singletonList("*"));
        }else{
            List<String> roles = sysRoleService.getRolesByUserId(sysUser.getId());
            sysUserDTO.setRoles(roles);
            List<String> permissions = sysMenuService.getPermissionsByUserId(sysUser.getId());
            sysUserDTO.setPermissions(permissions);
        }
        return sysUserDTO;
    }

    @Override
    public SysUserVO getUserInfo(String id) {
        SysUser sysUser = getById(id);
        if (sysUser == null) {
            return null;
        }
        SysUserVO sysUserVO = new SysUserVO();
        BeanUtils.copyProperties(sysUser,sysUserVO);
        sysUserVO.setRoleIds(sysRoleService.getRolesIdsByUserId(sysUser.getId()));
        sysUserVO.setPostIds(sysPostService.getPostIdsByUserId(sysUser.getId()));
        return sysUserVO;
    }

    @Override
    public boolean updateUser(SysUserVO sysUserVO) {
        SysUser sysUser = new SysUser();
        sysUser.setId(sysUserVO.getId());
        sysUser.setNickName(sysUserVO.getNickName());
        sysUser.setAvatar(sysUserVO.getAvatar());
        sysUser.setDeptId(sysUserVO.getDeptId());
        sysUser.setEmail(sysUserVO.getEmail());
        sysUser.setPhoneNumber(sysUserVO.getPhoneNumber());
        sysUser.setGender(sysUserVO.getGender());
        sysUser.setStatus(sysUserVO.getStatus());
        sysUser.setUpdateTime(new Date());
        sysUser.setUpdateBy(sysUserVO.getUpdateBy());

        //岗位
        sysUserPostMapper.delete(new LambdaQueryWrapper<SysUserPost>().eq(SysUserPost::getUserId, sysUserVO.getId()));
        if (sysUserVO.getPostIds() != null && !sysUserVO.getPostIds().isEmpty()) {
            for (String postId : sysUserVO.getPostIds()) {
                SysUserPost sysUserPost = new SysUserPost();
                sysUserPost.setUserId(sysUserVO.getId());
                sysUserPost.setPostId(postId);
                sysUserPostMapper.insert(sysUserPost);
            }
        }

        //角色
        sysUserRoleMapper.delete(new LambdaQueryWrapper<SysUserRole>().eq(SysUserRole::getUserId, sysUserVO.getId()));
        if (sysUserVO.getRoleIds() != null && !sysUserVO.getRoleIds().isEmpty()) {
            for (String roleId : sysUserVO.getRoleIds()) {
                SysUserRole sysUserRole = new SysUserRole();
                sysUserRole.setUserId(sysUserVO.getId());
                sysUserRole.setRoleId(roleId);
                sysUserRoleMapper.insert(sysUserRole);
            }
        }

        return this.updateById(sysUser);
    }

    @Override
    public boolean addUser(SysUserVO sysUserVO) {
        if (com.neo.utils.StringUtils.isBlank(sysUserVO.getUserName())) {
            throw new BusinessException("用户账号不能为空");
        }
        if (this.count(new QueryWrapper<SysUser>().eq("user_name", sysUserVO.getUserName())) > 0) {
            throw new BusinessException("账号【" + sysUserVO.getUserName() + "】已存在");
        }
        if (!ReUtil.isMatch("^[A-Za-z0-9]{4,18}$", sysUserVO.getUserName())) {
            throw new BusinessException("账号格式不正确，账号格式：英文或数数字 4-18位");
        }
        if (com.neo.utils.StringUtils.isBlank(sysUserVO.getNickName())) {
            throw new BusinessException("用户昵称不能为空");
        }
        if (com.neo.utils.StringUtils.isBlank(sysUserVO.getPassword())) {
            throw new BusinessException("用户密码不能为空");
        }
        if (!ReUtil.isMatch("^(?=.*[a-z])(?=.*[A-Z])(?=.*[~!@&%#_])[a-zA-Z0-9~!@&%#_]{8,16}$", sysUserVO.getPassword())) {
            throw new BusinessException("密码格式，包含一个大写，一个小写字母，一个数字，一个特殊字符，且长度为8到16位");
        }

        SysUser sysUser = new SysUser();
        sysUser.setId(getMaxUserId().toString());
        sysUser.setUserName(sysUserVO.getUserName());
        sysUser.setNickName(sysUserVO.getNickName());
        sysUser.setPassword(BCrypt.hashpw(sysUser.getPassword()));
        sysUser.setUserType("system");
        sysUser.setDeptId(sysUserVO.getDeptId());
        sysUser.setEmail(sysUserVO.getEmail());
        sysUser.setPhoneNumber(sysUserVO.getPhoneNumber());
        sysUser.setGender(sysUserVO.getGender());
        sysUser.setStatus(sysUserVO.getStatus());
        sysUser.setAvatar(sysUserVO.getAvatar());
        sysUser.setCreateTime(new Date());
        sysUser.setCreateBy(sysUserVO.getCreateBy());

        //岗位
        if (sysUserVO.getPostIds() != null && !sysUserVO.getPostIds().isEmpty()) {
            for (String postId : sysUserVO.getPostIds()) {
                SysUserPost sysUserPost = new SysUserPost();
                sysUserPost.setUserId(sysUserVO.getId());
                sysUserPost.setPostId(postId);
                sysUserPostMapper.insert(sysUserPost);
            }
        }

        //角色
        if (sysUserVO.getRoleIds() != null && !sysUserVO.getRoleIds().isEmpty()) {
            for (String roleId : sysUserVO.getRoleIds()) {
                SysUserRole sysUserRole = new SysUserRole();
                sysUserRole.setUserId(sysUserVO.getId());
                sysUserRole.setRoleId(roleId);
                sysUserRoleMapper.insert(sysUserRole);
            }
        }

        return this.save(sysUser);
    }

    private Long getMaxUserId() {
        Long userId = jdbcTemplate.queryForObject("select max(id) from sys_user", Long.class);
        return ++userId;
    }

}
