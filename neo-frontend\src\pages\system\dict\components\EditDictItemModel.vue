<template>
  <t-dialog 
    v-model:visible="dialogVisible" 
    :header="dialogTitle" 
    :width="500" 
    :confirm-btn="{ loading: formLoading }" 
    @confirm="onConfirm" 
    @cancel="onCancel"
  >
    <template v-if="dialogVisible">
      <t-form ref="form" :data="formData" :rules="rules" :label-width="80" @submit="onSubmit">
        <t-form-item label="字典文本" name="itemText">
          <t-input 
            v-model="formData.itemText" 
            placeholder="请输入字典项文本"
          />
        </t-form-item>
        
        <t-form-item label="字典值" name="itemValue">
          <t-input 
            v-model="formData.itemValue" 
            placeholder="请输入字典项值"
          />
        </t-form-item>
        
        <t-form-item label="排序" name="sort">
          <t-input-number 
            v-model="formData.sort" 
            placeholder="请输入排序"
            :min="0"
          />
        </t-form-item>
        
        <t-form-item label="状态" name="status">
          <t-switch 
            v-model="formData.status" 
            :custom-value="['1', '0']" 
            checked="1" 
            unchecked="0"
            size="small"
          />
        </t-form-item>
        
        <t-form-item label="描述" name="description">
          <t-textarea 
            v-model="formData.description" 
            placeholder="请输入描述"
            :maxlength="500"
            :autosize="{ minRows: 3, maxRows: 5 }"
          />
        </t-form-item>
      </t-form>
    </template>
  </t-dialog>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { addDictItem, updateDictItem } from '@/api/dict';
import type { SysDictItem } from '@/api/model/dictModel';

// 定义 props
interface Props {
  visible: boolean;
  isEdit: boolean;
  dictItemData?: SysDictItem;
  dictId?: string;
  dictCode?: string;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  isEdit: false,
  dictItemData: undefined,
  dictId: '',
  dictCode: ''
});

// 定义 emits
const emit = defineEmits<{
  'update:visible': [value: boolean];
  'success': [];
}>();

// 响应式数据
const formLoading = ref(false);
const form = ref();

const formData = ref({
  id: '',
  dictId: '',
  dictCode: '',
  itemText: '',
  itemValue: '',
  sort: 0,
  status: '1',
  description: '',
});

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => emit('update:visible', value)
});

const dialogTitle = computed(() => {
  return props.isEdit ? '编辑字典项' : '新增字典项';
});

const rules = computed(() => ({
  itemText: [
    { required: true, message: '请输入字典项文本' }
  ],
  itemValue: [
    { required: true, message: '请输入字典项值' }
  ],
}));

// 初始化表单数据
const initFormData = () => {
  formData.value = {
    id: '',
    dictId: props.dictId || '',
    dictCode: props.dictCode || '',
    itemText: '',
    itemValue: '',
    sort: 0,
    status: '1',
    description: '',
  };
};

// 加载字典项详情数据
const loadDictItemDetail = () => {
  if (props.dictItemData) {
    // 使用父组件传递的字典项数据
    formData.value = { 
      id: props.dictItemData.id || '',
      dictId: props.dictItemData.dictId || props.dictId || '',
      dictCode: props.dictItemData.dictCode || props.dictCode || '',
      itemText: props.dictItemData.itemText || '',
      itemValue: props.dictItemData.itemValue || '',
      sort: props.dictItemData.sort || 0,
      status: props.dictItemData.status || '1',
      description: props.dictItemData.description || ''
    };
  }
};

// 监听弹窗显示状态
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    initFormData();
    
    if (props.isEdit && props.dictItemData) {
      // 编辑模式，使用父组件传递的字典项数据
      loadDictItemDetail();
    }
  }
});

// 确认按钮处理
const onConfirm = () => {
  form.value.submit();
};

// 取消按钮处理
const onCancel = () => {
  emit('update:visible', false);
};

// 表单提交处理
const onSubmit = ({ validateResult, firstError }: any) => {
  if (validateResult === true) {
    formLoading.value = true;
    const data = { ...formData.value };

    const promise = props.isEdit ? updateDictItem(data) : addDictItem(data);

    promise.then(() => {
      MessagePlugin.success(props.isEdit ? '更新成功' : '添加成功');
      emit('update:visible', false);
      emit('success');
    }).catch((e) => {
      MessagePlugin.error(e.message || (props.isEdit ? '更新失败' : '添加失败'));
    }).finally(() => {
      formLoading.value = false;
    });
  } else {
    console.log('Errors: ', validateResult);
    MessagePlugin.warning(firstError);
  }
};
</script>

<style lang="less" scoped>
// 弹窗内容样式优化
:deep(.t-dialog__body) {
  max-height: 400px;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 16px 24px;
}

:deep(.t-dialog) {
  .t-form {
    overflow: visible;
    width: 100%;
    box-sizing: border-box;
  }
  
  // 确保表单项不会超出宽度
  .t-form-item {
    margin-bottom: 16px;
  }
  
  // 输入框宽度限制
  .t-input,
  .t-input-number,
  .t-switch,
  .t-textarea {
    width: 100%;
  }
}
</style>