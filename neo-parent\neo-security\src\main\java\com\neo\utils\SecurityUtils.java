package com.neo.utils;

import com.neo.aspect.annontation.PrePermissions;
import com.neo.aspect.annontation.PreRoles;
import com.neo.exception.BusinessException;
import com.neo.exception.NotLoginException;
import com.neo.exception.NotPermissionException;
import com.neo.exception.NotRoleException;
import com.neo.service.SysUserDetail;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.List;
import java.util.Set;

@Slf4j
public class SecurityUtils {

    public static SysUserDetail getLoginUser(){
        try {
            Authentication authentication = getAuthentication();
            if (authentication == null || !authentication.isAuthenticated() || "anonymousUser".equals(authentication.getPrincipal())) {
                throw new NotLoginException("用户未登录");
            }
            return (SysUserDetail) authentication.getPrincipal();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException("获取用户信息异常");
        }
    }

    public static String getUserId(){
        try {
            return getLoginUser().getUserId();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException("获取用户信息异常");
        }
    }

    public static String getUsername(){
        try {
            return getLoginUser().getUsername();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException("获取用户信息异常");
        }
    }

    public static String getUserDeptId(){
        try {
            return getLoginUser().getDeptId();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException("获取用户信息异常");
        }
    }

    /**
     * 获取Authentication
     */
    public static Authentication getAuthentication() {
        return SecurityContextHolder.getContext().getAuthentication();
    }

    /**
     * 检查用户是否已登录
     */
    public static void checkLogin() {
        getLoginUser(); // 复用getLoginUser方法中的登录检查逻辑
    }

    /**
     * 检查用户是否具有指定角色权限
     *
     * @param preRoles 预设的角色权限注解
     */
    public static void checkRole(PreRoles preRoles) {
        try {
            checkLogin();
            validatePermissionsOrRoles(getLoginUser().getRoles(), preRoles.value(), "角色");
        } catch (BusinessException e) {
            throw new NotRoleException(preRoles.value());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException("权限校验异常");
        }
    }

    /**
     * 检查用户是否具有指定权限
     *
     * @param prePermissions 预设的权限注解
     */
    public static void checkPermission(PrePermissions prePermissions) {
        try {
            checkLogin();
            validatePermissionsOrRoles(getLoginUser().getPermissions(), prePermissions.value(), "权限");
        } catch (BusinessException e) {
            throw new NotPermissionException(prePermissions.value());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException("权限校验异常");
        }
    }

    /**
     * 验证用户是否具有指定的权限或角色
     *
     * @param userItems 用户拥有的权限或角色集合
     * @param requiredItems 需要的权限或角色数组
     * @param itemType 项类型（"权限"或"角色"）
     */
    private static void validatePermissionsOrRoles(List<String> userItems, String[] requiredItems, String itemType) {
        //超管放行
        if (userItems.contains("*")){
            return;
        }

        for (String requiredItem : requiredItems) {
            if (userItems.contains(requiredItem)) {
                return;
            }
        }

        throw new BusinessException("用户权限不足，需要指定" + itemType);
    }
}
