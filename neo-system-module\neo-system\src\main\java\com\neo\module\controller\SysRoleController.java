package com.neo.module.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.neo.aspect.annontation.Log;
import com.neo.model.Result;
import com.neo.common.QueryGenerator;
import com.neo.module.entity.SysRole;
import com.neo.module.entity.vo.SysRoleVO;
import com.neo.module.service.SysRoleService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 角色信息
 *
 * <AUTHOR>
 * @since 2023-03-25
 */
@RestController
@RequestMapping("/sysRole")
public class SysRoleController {
    @Resource
    private SysRoleService sysRoleService;

    /**
     * 分页查询
     *
     * @param params
     * @return
     */
    @Log(value = "角色信息-分页查询")
    @PostMapping("/page")
    public Result<?> page(@RequestBody Map<String, String> params) {
        QueryWrapper<SysRole> queryWrapper = QueryGenerator.initQueryWrapper(SysRole.class, params);
        return Result.ok(sysRoleService.page(params, queryWrapper));
    }

    /**
     * 查询
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public Result<?> get(@PathVariable String id) {
        SysRoleVO sysRole = sysRoleService.getRoleById(id);
        return Result.ok(sysRole);
    }

    /**
     * 列表
     *
     * @param sysRole
     * @return
     */
    @PostMapping("/list")
    public Result<?> list(@RequestBody SysRole sysRole) {
        List<SysRole> list = sysRoleService.list(sysRole);
        return Result.ok(list);
    }

    /**
     * 新增
     *
     * @param sysRole
     * @return
     */
    @PostMapping("/add")
    public Result<?> add(@RequestBody SysRole sysRole) {
        sysRoleService.save(sysRole);
        return Result.ok();
    }

    /**
     * 删除
     *
     * @param ids
     * @return
     */
    @DeleteMapping
    public Result<?> delete(@RequestBody Set<String> ids) {
        sysRoleService.removeByIds(ids);
        return Result.ok();
    }

    /**
     * 更新
     *
     * @param sysRoleVO
     * @return
     */
    @PutMapping("/update")
    public Result<?> update(@RequestBody SysRoleVO sysRoleVO) {
        sysRoleVO.setRoleKey(null);
        sysRoleService.updateRole(sysRoleVO);
        return Result.ok();
    }
}

