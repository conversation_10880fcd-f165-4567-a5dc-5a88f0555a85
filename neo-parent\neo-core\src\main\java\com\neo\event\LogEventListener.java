package com.neo.event;

import com.neo.model.LogModel;
import com.neo.module.dto.SysLogDTO;
import com.neo.module.rpc.SysLogRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class LogEventListener {

    @DubboReference
    private SysLogRpcService sysLogRpcService;

    @EventListener
    @Async("asyncServiceExecutor")
    public void saveLog(LogModel logModel){
        SysLogDTO sysLogDTO = new SysLogDTO();
        BeanUtils.copyProperties(logModel,sysLogDTO);
        sysLogRpcService.saveLog(sysLogDTO);
    }

}
