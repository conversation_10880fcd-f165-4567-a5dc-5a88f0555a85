import { request } from '@/utils/request';
import {
  SysDict,
  SysDictItem,
  DictListResult,
  DictItemListResult,
  DictPageParams,
  DictItemPageParams,
} from './model/dictModel';

enum Api {
  DictList = '/system/sysDict/page',
  DictInfo = '/system/sysDict',
  AddDict = '/system/sysDict/add',
  UpdateDict = '/system/sysDict/update',
  DeleteDict = '/system/sysDict',
  
  DictItemList = '/system/sysDictItem/page',
  DictItemInfo = '/system/sysDictItem',
  AddDictItem = '/system/sysDictItem/add',
  UpdateDictItem = '/system/sysDictItem/update',
  DeleteDictItem = '/system/sysDictItem',
}

/**
 * 分页查询字典列表
 * @param params
 */
export const getDictPage = (params: DictPageParams) => {
  return request.post<DictListResult>({
    url: Api.DictList,
    params,
  });
};

/**
 * 获取字典详情
 * @param id
 */
export const getDictInfo = (id: string) => {
  return request.get<SysDict>({
    url: `${Api.DictInfo}/${id}`,
  });
};

/**
 * 添加字典
 * @param params
 */
export const addDict = (params: SysDict) => {
  return request.post({
    url: Api.AddDict,
    params,
  });
};

/**
 * 更新字典
 * @param params
 */
export const updateDict = (params: SysDict) => {
  return request.put({
    url: Api.UpdateDict,
    params,
  });
};

/**
 * 删除字典
 * @param ids
 */
export const deleteDict = (ids: string[]) => {
  return request.delete({
    url: Api.DeleteDict,
    params: ids,
  });
};

/**
 * 分页查询字典项列表
 * @param params
 */
export const getDictItemPage = (params: DictItemPageParams) => {
  return request.post<DictItemListResult>({
    url: Api.DictItemList,
    params,
  });
};

/**
 * 获取字典项详情
 * @param id
 */
export const getDictItemInfo = (id: string) => {
  return request.get<SysDictItem>({
    url: `${Api.DictItemInfo}/${id}`,
  });
};

/**
 * 添加字典项
 * @param params
 */
export const addDictItem = (params: SysDictItem) => {
  return request.post({
    url: Api.AddDictItem,
    params,
  });
};

/**
 * 更新字典项
 * @param params
 */
export const updateDictItem = (params: SysDictItem) => {
  return request.put({
    url: Api.UpdateDictItem,
    params,
  });
};

/**
 * 删除字典项
 * @param ids
 */
export const deleteDictItem = (ids: string[]) => {
  return request.delete({
    url: Api.DeleteDictItem,
    params: ids,
  });
};