<template>
  <div class="attachment-management-container">
    <!-- 筛选表单 -->
    <t-card class="filter-card" :bordered="false">
      <t-form :data="searchFormState" :label-width="80" colon @reset="onReset" @submit="onSubmit">
        <t-row>
          <t-col :span="10">
            <t-row :gutter="[24, 24]">
              <t-col :span="4">
                <t-form-item label="文件名称" name="fileName">
                  <t-input
                    v-model="searchFormState.fileName"
                    class="form-item-content"
                    type="search"
                    placeholder="请输入文件名称"
                    clearable
                  />
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="文件类型" name="fileType">
                  <t-input
                    v-model="searchFormState.fileType"
                    class="form-item-content"
                    placeholder="请输入文件类型"
                    clearable
                  />
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="存储类型" name="storageType">
                  <t-select
                    v-model="searchFormState.storageType"
                    class="form-item-content"
                    :options="storageTypeOptions"
                    placeholder="请选择存储类型"
                    clearable
                  />
                </t-form-item>
              </t-col>
            </t-row>
          </t-col>

          <t-col :span="2" class="operation-container">
            <t-button theme="primary" type="submit" :style="{ marginLeft: 'var(--td-comp-margin-s)' }">
              查询
            </t-button>
            <t-button type="reset" variant="base" theme="default">
              重置
            </t-button>
          </t-col>
        </t-row>
      </t-form>
    </t-card>

    <!-- 表格容器 -->
    <t-card class="table-card" :bordered="false">
      <div class="table-header">
        <div class="left-operation-container">
          <t-button @click="handleUpload">上传文件</t-button>
          <t-button 
            theme="danger" 
            variant="outline" 
            @click="handleBatchDelete"
            :disabled="selectedRowKeys.length === 0"
          >
            批量删除
          </t-button>
        </div>
      </div>

      <t-table
        :data="paginationData.dataSource.value"
        :columns="columns"
        :row-key="rowKey"
        vertical-align="top"
        :hover="true"
        :pagination="paginationData.tableConfig.value"
        :loading="paginationData.loading.value"
        :selected-row-keys="selectedRowKeys"
        @select-change="onSelectChange"
        @page-change="(pageInfo: any) => paginationData.handlePageChange(pageInfo, loadAttachmentData, searchFormState)"
      >
        <template #fileName="{ row }">
          <div class="file-info">
            <t-icon :name="getFileIcon(row.fileType)" size="20px" style="margin-right: 8px;" />
            <span>{{ row.fileName }}</span>
          </div>
        </template>

        <template #fileSize="{ row }">
          {{ formatFileSize(row.fileSize) }}
        </template>

        <template #status="{ row }">
          <t-tag v-if="row.status" theme="success" variant="light">
            正常
          </t-tag>
          <t-tag v-else theme="danger" variant="light">
            禁用
          </t-tag>
        </template>
        
        <template #op="slotProps">
          <t-space>
            <t-link theme="primary" @click="handleDownload(slotProps.row)">下载</t-link>
            <t-link theme="primary" @click="handleEdit(slotProps.row)">编辑</t-link>
            <t-popconfirm 
              content="确定要删除吗？" 
              @confirm="handleDelete(slotProps.row)"
            >
              <t-link theme="danger">删除</t-link>
            </t-popconfirm>
          </t-space>
        </template>
      </t-table>
    </t-card>

    <!-- 文件上传弹窗 -->
    <UploadModel 
      v-model:visible="uploadVisible" 
      @success="onUploadSuccess"
    />

    <!-- 文件编辑弹窗 -->
    <EditModel 
      v-model:visible="formVisible" 
      :is-edit="isEdit" 
      :attachment-data="editAttachmentData"
      @success="onFormSuccess"
    />
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted } from 'vue';
  import { MessagePlugin } from 'tdesign-vue-next';
  import {
    getAttachmentPage,
    deleteAttachment,
    downloadFile,
  } from '@/api/attachment';
  import { SysAttachment, AttachmentPageParams, AttachmentListResult } from '@/api/model/attachmentModel';
  import { usePagination, CommonPageParams, CommonPageResult } from '@/hooks';
  import { formatFileSize, getFileIcon, downloadFileFromBlob } from '@/utils/file';
  import EditModel from './components/EditModel.vue';
  import UploadModel from './components/UploadModel.vue';

  defineOptions({
    name: 'AttachmentManagement',
  });

  // 存储类型选项
  const storageTypeOptions = [
    { value: 'LOCAL', label: '本地存储' },
    { value: 'OSS', label: '对象存储' },
  ];

  // 定义表格列
  const columns = [
    {
      title: '文件名称',
      colKey: 'fileName',
      width: 300,
    },
    {
      title: '文件大小',
      colKey: 'fileSize',
      width: 120,
    },
    {
      title: '文件类型',
      colKey: 'fileType',
      width: 100,
    },
    {
      title: '存储类型',
      colKey: 'storageType',
      width: 100,
    },
    {
      title: '状态',
      colKey: 'status',
      width: 80,
    },
    {
      title: '创建时间',
      colKey: 'createTime',
      width: 180,
    },
    {
      title: '操作',
      colKey: 'op',
      width: 200,
      fixed: 'right',
    },
  ];

  // 使用通用分页 hook
  const paginationData = usePagination<SysAttachment>({
    defaultCurrent: 1,
    defaultPageSize: 10,
    paramType: 'default'
  });

  // 搜索表单
  const searchFormState = reactive<Omit<AttachmentPageParams, 'current' | 'pageSize'>>({
    fileName: '',
    fileType: '',
    storageType: '',
  });

  // 对话框相关
  const formVisible = ref(false);
  const uploadVisible = ref(false);
  const isEdit = ref(false);
  const editAttachmentData = ref<SysAttachment>();

  // 表格选择
  const selectedRowKeys = ref<string[]>([]);

  // 数据加载函数
  const loadAttachmentData = async (params: CommonPageParams): Promise<CommonPageResult<SysAttachment>> => {
    const res = await getAttachmentPage(params as AttachmentPageParams);
    return {
      records: res.records,
      total: res.total,
      current: res.current,
      size: res.size,
    };
  };

  // 查询
  const searchQuery = () => {
    paginationData.resetToFirstPage(loadAttachmentData, searchFormState);
  };

  // 表单提交
  const onSubmit = () => {
    searchQuery();
  };

  // 表单重置
  const onReset = () => {
    searchFormState.fileName = '';
    searchFormState.fileType = '';
    searchFormState.storageType = '';
    searchQuery();
  };

  const rowKey = 'id';

  // 上传文件
  const handleUpload = () => {
    uploadVisible.value = true;
  };

  // 编辑
  const handleEdit = (record: SysAttachment) => {
    isEdit.value = true;
    editAttachmentData.value = record;
    formVisible.value = true;
  };

  // 下载文件
  const handleDownload = async (record: SysAttachment) => {
    try {
      const response = await downloadFile(record.id || '');
      const blob = new Blob([response]);
      downloadFileFromBlob(blob, record.fileName || 'download');
      MessagePlugin.success('下载成功');
    } catch (error) {
      console.error('下载失败:', error);
      MessagePlugin.error('下载失败');
    }
  };

  // 删除
  const handleDelete = async (record: SysAttachment) => {
    try {
      await deleteAttachment([record.id || '']);
      MessagePlugin.success('删除成功');
      searchQuery();
    } catch (error) {
      console.error('删除失败:', error);
      MessagePlugin.error('删除失败');
    }
  };

  // 批量删除
  const handleBatchDelete = async () => {
    try {
      await deleteAttachment(selectedRowKeys.value);
      MessagePlugin.success('批量删除成功');
      selectedRowKeys.value = [];
      searchQuery();
    } catch (error) {
      console.error('批量删除失败:', error);
      MessagePlugin.error('批量删除失败');
    }
  };

  // 表格选择变化
  const onSelectChange = (value: string[]) => {
    selectedRowKeys.value = value;
  };

  // 上传成功回调
  const onUploadSuccess = () => {
    paginationData.refreshData(loadAttachmentData, searchFormState);
  };

  // 表单操作成功回调
  const onFormSuccess = () => {
    paginationData.refreshData(loadAttachmentData, searchFormState);
  };



  onMounted(() => {
    paginationData.loadData(paginationData.buildPageParams(searchFormState), loadAttachmentData);
  });
</script>

<style lang="less" scoped>
  .attachment-management-container {
    background-color: var(--td-bg-color-container);
    padding: var(--td-comp-paddingTB-xxl) var(--td-comp-paddingLR-xxl);
    border-radius: var(--td-radius-medium);
    
    .filter-card {
      margin-bottom: var(--td-comp-margin-xxl);
    }
    
    .table-card {
      .table-header {
        margin-bottom: var(--td-comp-margin-xl);
        
        .left-operation-container {
          display: flex;
          align-items: center;
          gap: 10px;
        }
      }
    }
  }

  .form-item-content {
    width: 100%;
  }

  .operation-container {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }

  .file-info {
    display: flex;
    align-items: center;
  }
</style>
