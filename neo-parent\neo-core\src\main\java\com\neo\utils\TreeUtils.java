package com.neo.utils;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class TreeUtils {

    private static class Holder {
        private static final TreeUtils treeUtils = new TreeUtils();
    }

    private TreeUtils() {

    }

    public static TreeUtils getInstance() {
        return Holder.treeUtils;
    }

    public <T, K> List<K> convertTree(List<T> data, Class<K> clazz, Object rootId) {
        if (CollectionUtil.isEmpty(data)) {
            return Collections.emptyList();
        }
        try {
            //根节点
            List<K> roots = new ArrayList<>();
            //子节点
            List<K> nodes = new ArrayList<>();

            for (T t : data) {
                K k = clazz.newInstance();
                BeanUtils.copyProperties(t, k);
                Object id = clazz.getMethod("get" + StrUtil.upperFirst("parentId")).invoke(k);
                if (id == null || id.equals(rootId)) {
                    roots.add(k);
                } else {
                    nodes.add(k);
                }
            }

            Method getParentIdMethod = clazz.getMethod("get" + StrUtil.upperFirst("parentId"));

            Map<Object, List<K>> group = nodes.parallelStream()
                    //筛选出父ID不为空的
                    .filter(k -> {
                        try {
                            Object obj = getParentIdMethod.invoke(k);
                            if (obj == null) {
                                return false;
                            }
                            if (obj instanceof String) {
                                return StrUtil.isNotBlank(obj.toString());
                            }
                            return true;
                        } catch (Exception e) {
                            log.error(e.getMessage());
                            return false;
                        }
                    })
                    //分组
                    .collect(Collectors.groupingBy(k -> {
                        try {
                            return getParentIdMethod.invoke(k);
                        } catch (Exception e) {
                            log.error(e.getMessage());
                            return null;
                        }
                    }));

            for (K root : roots) {
                forEach(group, root);
            }
            return roots;
        } catch (Exception e) {
            log.error(e.getMessage());
        }

        return Collections.emptyList();
    }

    private <K> void forEach(Map<Object, List<K>> group, K root) throws Exception {
        Class<?> clazz = root.getClass();
        Object id = clazz.getMethod("get" + StrUtil.upperFirst("id")).invoke(root);
        List<K> treeNodelist = group.get(id);
        if (group.get(id) != null) {
            Field field = clazz.getDeclaredField("children");
            field.setAccessible(true);
            field.set(root, treeNodelist);
            List list = (List) clazz.getMethod("get" + StrUtil.upperFirst("Children")).invoke(root);
            list.forEach(t -> {
                try {
                    forEach(group, (K) t);
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            });
        }
    }


}
