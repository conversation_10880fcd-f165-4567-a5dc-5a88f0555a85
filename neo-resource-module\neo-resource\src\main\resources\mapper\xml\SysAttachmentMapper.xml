<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.neo.module.mapper.SysAttachmentMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.neo.module.entity.SysAttachment">

                    <result column="file_name" property="fileName"/>
                    <result column="file_path" property="filePath"/>
                    <result column="file_size" property="fileSize"/>
                    <result column="file_url" property="fileUrl"/>
                    <result column="file_type" property="fileType"/>
                    <result column="storage_type" property="storageType"/>
                    <result column="md5" property="md5"/>
                    <result column="status" property="status"/>
                <result column="id" property="id"/>
                <result column="create_by" property="createBy"/>
                <result column="create_time" property="createTime"/>
                <result column="update_by" property="updateBy"/>
                <result column="update_time" property="updateTime"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
id,create_by,create_time,update_by,update_time,file_name, file_path, file_size, file_url, file_type, storage_type, md5, status
        </sql>

</mapper>
