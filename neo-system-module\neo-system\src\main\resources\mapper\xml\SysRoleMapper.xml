<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.neo.module.mapper.SysRoleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.neo.module.entity.SysRole">

        <result column="role_name" property="roleName" />
        <result column="role_key" property="roleKey" />
        <result column="role_sort" property="roleSort" />
        <result column="data_scope" property="dataScope" />
        <result column="menu_relation" property="menuRelation" />
        <result column="dept_relation" property="deptRelation" />
        <result column="status" property="status" />
        <result column="remark" property="remark" />
        <result column="id" property="id" />
        <result column="is_deleted" property="deleted" />
        <result column="create_time" property="createTime" />
        <result column="create_by" property="createBy" />
        <result column="update_time" property="updateTime" />
        <result column="update_by" property="updateBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,is_deleted,create_time,create_by,update_time,update_by,role_name, role_key, role_sort, data_scope, menu_relation, dept_relation, status, remark
    </sql>

</mapper>
