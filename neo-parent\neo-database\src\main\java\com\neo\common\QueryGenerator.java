package com.neo.common;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@Slf4j
public class QueryGenerator {

    /**
     * 页数
     */
    private static final String CURRENT = "current";
    /**
     * 数量
     */
    private static final String PAGE_SIZE = "pageSize";
    /**
     * 最大数量
     */
    private static final Integer PAGE_MAX_SIZE = 500;

    public static <T> QueryWrapper<T> initQueryWrapper(Class clazz, Map<String, String> parameterMap) {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        initWrapper(queryWrapper, clazz, parameterMap);
        return queryWrapper;
    }

    private static <T> void initWrapper(QueryWrapper<T> queryWrapper, Class clazz, Map<String, String> parameterMap) {

    }

    public static Page initPage(Map<String, String> params) {
        int current = params.get(CURRENT) == null ? 1 : Integer.parseInt(params.get(CURRENT));
        int pageSize = params.get(PAGE_SIZE) == null ? 10 : Integer.parseInt(params.get(PAGE_SIZE));
        //限制最大查询数量为500条每页
        if (pageSize > PAGE_MAX_SIZE) {
            pageSize = PAGE_MAX_SIZE;
        }
        return new Page<>(current, pageSize);
    }
}
