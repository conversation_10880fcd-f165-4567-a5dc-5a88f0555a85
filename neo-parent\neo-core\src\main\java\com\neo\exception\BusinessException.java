package com.neo.exception;

public class BusinessException extends RuntimeException {
    private static final long serialVersionUID = 1L;

    /**
     * 错误编码
     */
    private String errorCode;
    /**
     * 错误信息占位参数
     */
    private String[] args;

    /**
     * 错误信息
     *
     * @param message
     */
    public BusinessException(String message) {
        super(message);
        this.errorCode = message;
    }

    public BusinessException(String message, String... args) {
        super(message);
        this.errorCode = message;
        this.args = args;
    }

    public BusinessException(Throwable cause) {
        super(cause);
    }

    public BusinessException(String message, Throwable cause, String... args) {
        super(message, cause);
        this.errorCode = message;
        this.args = args;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String[] getArgs() {
        return args;
    }

    public void setArgs(String[] args) {
        this.args = args;
    }
}
