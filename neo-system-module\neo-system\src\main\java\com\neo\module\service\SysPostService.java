package com.neo.module.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.neo.module.entity.SysPost;

import java.util.List;
import java.util.Map;

/**
 * 岗位 服务类
 *
 * <AUTHOR>
 * @since 2023-05-24
 */
public interface SysPostService extends IService<SysPost> {

    IPage<SysPost> page(Map<String, String> params, QueryWrapper<SysPost> queryWrapper);

    List<SysPost> list(SysPost sysPost);

    List<String> getPostIdsByUserId(String id);

}
