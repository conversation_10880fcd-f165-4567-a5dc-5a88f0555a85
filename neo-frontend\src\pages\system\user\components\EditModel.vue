<template>
  <t-dialog 
    v-model:visible="dialogVisible" 
    :header="dialogTitle" 
    :width="700" 
    :confirm-btn="{ loading: formLoading }" 
    @confirm="onConfirm" 
    @cancel="onCancel"
  >
    <template v-if="dialogVisible">
      <t-form ref="form" :data="formData" :rules="rules" :label-width="80" @submit="onSubmit">
        <t-row :gutter="[16, 16]" class="form-row">
          <t-col :span="6">
            <t-form-item label="用户账号" name="userName">
              <t-input v-model="formData.userName" :disabled="isEdit" placeholder="请输入用户账号" />
            </t-form-item>
          </t-col>
          <t-col :span="6">
            <t-form-item label="用户昵称" name="nickName">
              <t-input v-model="formData.nickName" placeholder="请输入用户昵称" />
            </t-form-item>
          </t-col>
        </t-row>
        
        <t-row :gutter="[16, 16]" class="form-row">
          <t-col :span="6">
            <t-form-item label="密码" name="password">
              <t-input v-model="formData.password" type="password" :placeholder="isEdit ? '留空则不修改密码' : '请输入密码'" />
            </t-form-item>
          </t-col>
          <t-col :span="6">
            <t-form-item label="邮箱" name="email">
              <t-input v-model="formData.email" placeholder="请输入邮箱" />
            </t-form-item>
          </t-col>
        </t-row>
        
        <t-row :gutter="[16, 16]" class="form-row">
          <t-col :span="6">
            <t-form-item label="手机号" name="phoneNumber">
              <t-input v-model="formData.phoneNumber" placeholder="请输入手机号" />
            </t-form-item>
          </t-col>
          <t-col :span="6">
            <t-form-item label="性别" name="gender">
              <t-radio-group v-model="formData.gender">
                <t-radio value="1">男</t-radio>
                <t-radio value="2">女</t-radio>
              </t-radio-group>
            </t-form-item>
          </t-col>
        </t-row>
        
        <t-row :gutter="[16, 16]" class="form-row">
          <t-col :span="6">
            <t-form-item label="状态" name="status">
              <t-switch v-model="formData.status" :custom-value="['1', '0']" checked="1" unchecked="0" />
            </t-form-item>
          </t-col>
          <t-col :span="6">
            <t-form-item label="部门" name="deptId">
              <t-tree-select
                v-model="formData.deptId"
                :data="props.deptTreeData"
                placeholder="请选择部门"
                clearable
                :tree-props="{
                  keys: { value: 'id', label: 'name', children: 'children' },
                  checkable: false
                }"
              />
            </t-form-item>
          </t-col>
        </t-row>
        
        <t-row :gutter="[16, 16]" class="form-row">
          <t-col :span="6">
            <t-form-item label="角色" name="roleIds">
              <t-select
                v-model="formData.roleIds"
                :options="roleOptions"
                placeholder="请选择角色"
                multiple
                clearable
              />
            </t-form-item>
          </t-col>
          <t-col :span="6">
            <t-form-item label="岗位" name="postIds">
              <t-select
                v-model="formData.postIds"
                :options="postOptions"
                placeholder="请选择岗位"
                multiple
                clearable
              />
            </t-form-item>
          </t-col>
        </t-row>
        
        <t-row :gutter="[16, 16]" class="form-row">
          <t-col :span="12">
            <t-form-item label="备注" name="remark">
              <t-textarea
                v-model="formData.remark"
                placeholder="请输入用户备注"
                :maxlength="500"
                :autosize="{ minRows: 3, maxRows: 5 }"
              />
            </t-form-item>
          </t-col>
        </t-row>
      </t-form>
    </template>
  </t-dialog>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { getDeptTree } from '@/api/dept';
import { getRoleSelectList } from '@/api/role';
import { getPostSelectList } from '@/api/post';
import { addUser, updateUser } from '@/api/user';
import { SysUser } from '@/api/model/userModel';

// 定义 props
interface Props {
  visible: boolean;
  isEdit: boolean;
  userData?: SysUser;
  deptTreeData?: any[];
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  isEdit: false,
  userData: undefined,
  deptTreeData: () => []
});

// 定义 emits
const emit = defineEmits<{
  'update:visible': [value: boolean];
  'success': [];
}>();

// 响应式数据
const formLoading = ref(false);
const form = ref();
const roleOptions = ref([]);
const postOptions = ref([]);

const formData = ref({
  id: '',
  userName: '',
  nickName: '',
  password: '',
  email: '',
  phoneNumber: '',
  gender: '0',
  status: '1',
  deptId: '', // 保持字符串类型
  roleIds: [],
  postIds: [],
  remark: '',
});

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => emit('update:visible', value)
});

const dialogTitle = computed(() => {
  return props.isEdit ? '编辑用户' : '新增用户';
});

const rules = computed(() => ({
  userName: [
    { required: true, message: '请输入用户账号' },
  ],
  nickName: [{ required: true, message: '请输入用户昵称' }],
  password: [
    { required: !props.isEdit, message: '请输入密码' }
  ],
  email: [
    { email: true, message: '请输入正确的邮箱地址' }
  ],
  phoneNumber: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' }
  ],
}));



// 获取角色选项
const fetchRoleOptions = async () => {
  try {
    const res = await getRoleSelectList();
    roleOptions.value = res.map(role => ({
      label: role.roleName,
      value: role.id
    }));
  } catch (e) {
    console.log(e);
  }
};

// 获取岗位选项
const fetchPostOptions = async () => {
  try {
    const res = await getPostSelectList();
    postOptions.value = res.map((post: any) => ({
      label: post.name,
      value: post.id
    }));
  } catch (e) {
    console.log(e);
  }
};

// 初始化表单数据
const initFormData = () => {
  formData.value = {
    id: '',
    userName: '',
    nickName: '',
    password: '',
    email: '',
    phoneNumber: '',
    gender: '0',
    status: '1',
    deptId: '', // 保持字符串类型
    roleIds: [],
    postIds: [],
    remark: '',
  };
};

// 加载用户详情数据
const loadUserDetail = () => {
  if (props.userData) {
    // 使用父组件传递的用户数据
    formData.value = { 
      id: props.userData.id || '',
      userName: props.userData.userName || '',
      nickName: props.userData.nickName || '',
      password: '', // 编辑时不展示密码
      email: props.userData.email || '',
      phoneNumber: props.userData.phoneNumber || '',
      gender: props.userData.gender || '0',
      status: props.userData.status || '1',
      // deptId 保持字符串类型，避免精度丢失
      deptId: props.userData.deptId ? String(props.userData.deptId) : '',
      roleIds: props.userData.roleIds || [],
      postIds: props.userData.postIds || [],
      remark: props.userData.remark || ''
    };
  }
};

// 监听弹窗显示状态
watch(() => props.visible, async (newVisible) => {
  if (newVisible) {
    initFormData();
    
    try {
      if (props.isEdit && props.userData) {
        // 编辑模式，使用父组件传递的用户数据
        loadUserDetail();
      }

      // 弹窗打开时获取最新的角色、岗位数据
      await Promise.all([
        fetchRoleOptions(),
        fetchPostOptions()
      ]);

    } catch (e) {
      console.error('数据加载失败:', e);
      MessagePlugin.error('数据加载失败');
    }
  }
});

// 确认按钮处理
const onConfirm = () => {
  form.value.submit();
};

// 取消按钮处理
const onCancel = () => {
  emit('update:visible', false);
};

// 表单提交处理
const onSubmit = ({ validateResult, firstError }: any) => {
  if (validateResult === true) {
    formLoading.value = true;
    const data = { 
      ...formData.value,
      // deptId 保持字符串类型，避免精度丢失
      deptId: formData.value.deptId || undefined
    };
    
    // 编辑模式下，如果密码为空则不发送密码字段
    if (props.isEdit && !data.password) {
      delete data.password;
    }

    const promise = props.isEdit ? updateUser(data) : addUser(data);

    promise.then(() => {
      MessagePlugin.success(props.isEdit ? '更新成功' : '添加成功');
      emit('update:visible', false);
      emit('success');
    }).catch((e) => {
      MessagePlugin.error(e.message || (props.isEdit ? '更新失败' : '添加失败'));
    }).finally(() => {
      formLoading.value = false;
    });
  } else {
    console.log('Errors: ', validateResult);
    MessagePlugin.warning(firstError);
  }
};
</script>

<style lang="less" scoped>
// 加载状态样式
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  padding: 40px 0;
}

// 增加表单行间距
.form-row {
  margin-bottom: 24px;
  width: 100%;
  &:last-child {
    margin-bottom: 0;
  }
}

// 弹窗内容样式优化
:deep(.t-dialog__body) {
  max-height: 500px;
  overflow-y: auto;
  overflow-x: hidden; // 隐藏水平滚动条
  padding: 16px 24px;
}

:deep(.t-dialog) {
  .t-form {
    overflow: visible;
    width: 100%;
    box-sizing: border-box;
  }
  
  // 确保表单行不会超出容器宽度
  .t-row {
    margin-left: 0;
    margin-right: 0;
    width: 100%;
  }
  
  // 调整列的间距，防止超出容器
  .t-col {
    padding-left: 12px;
    padding-right: 12px;
  }
  
  // 确保表单项不会超出宽度
  .t-form-item {
    margin-bottom: 16px;
  }
  
  // 输入框宽度限制
  .t-input,
  .t-select,
  .t-tree-select,
  .t-textarea {
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
  }
}
</style>