package com.neo.exception.handler;

import com.neo.exception.NotLoginException;
import com.neo.exception.NotPermissionException;
import com.neo.exception.NotRoleException;
import com.neo.model.Result;
import com.neo.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;

/**
 * 全局异常处理器
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(BusinessException.class)
    public Result handleBusinessException(BusinessException e) {
        return Result.fail(e.getMessage());
    }

    @ExceptionHandler(Exception.class)
    public Result handleException(Exception e) {
        log.error(e.getMessage(), e);
        return Result.fail("未知错误");
    }

    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public Result handleHttpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',不支持'{}'请求", requestURI, e.getMethod());
        return Result.fail(e.getMessage());
    }

    @ExceptionHandler(value = {NotLoginException.class})
    public Result handleNotLoginException(NotLoginException e) {
        return Result.fail(401,"未登录");
    }

    @ExceptionHandler(value = {NotPermissionException.class})
    public Result handleNotPermissionException(NotPermissionException e) {
        log.error("无操作访问权限：{}", e.getMessage());
        return Result.fail(403,"无权访问");
    }

    @ExceptionHandler(value = {NotRoleException.class})
    public Result handleNotRoleException(NotRoleException e) {
        log.error("无用户访问权限：{}", e.getMessage());
        return Result.fail(403,"无权访问");
    }

}
