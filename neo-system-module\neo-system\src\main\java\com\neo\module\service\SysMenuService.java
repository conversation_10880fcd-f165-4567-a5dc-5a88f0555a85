package com.neo.module.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.neo.module.entity.SysMenu;
import com.neo.module.entity.vo.SysMenuTree;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 菜单权限表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-28
 */
public interface SysMenuService extends IService<SysMenu> {

    /**
     * 根据条件查询列表
     * @param sysMenu
     * @return
     */
    List<SysMenu> list(SysMenu sysMenu);

    /**
     * 根据条件查询树形列表
     * @param sysMenu
     * @return
     */
    List<SysMenuTree> listTree(SysMenu sysMenu);

    /**
     * 根据用户ID获取菜单
     * @param userId
     * @return
     */
    List<SysMenuTree> getMenuByUserId(String userId);

    /**
     * 获取所有菜单列表
     * @return
     */
    List<SysMenuTree> getAllMenuList();

    List<String> getPermissionsByUserId(String id);

}
