package com.neo.module.service.impl;

import cn.hutool.captcha.CaptchaUtil;
import cn.hutool.captcha.LineCaptcha;
import cn.hutool.core.util.IdUtil;
import com.neo.constant.CacheConstants;
import com.neo.exception.BusinessException;
import com.neo.model.Result;
import com.neo.module.service.CaptchaService;
import com.neo.utils.RedisUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Service
public class CaptchaServiceImpl implements CaptchaService {

    @Resource
    private RedisUtils redisUtils;

    @Override
    public Result createCode() {
        LineCaptcha lineCaptcha = CaptchaUtil.createLineCaptcha(200, 100);
        String id = IdUtil.randomUUID();
        redisUtils.set(CacheConstants.CAPTCHA_CODE_KEY+id,lineCaptcha.getCode(),3L, TimeUnit.MINUTES);

        Map<String,String> data = new HashMap<>();
        data.put("img",lineCaptcha.getImageBase64());
        data.put("id",id);
        return Result.ok(data);
    }

    @Override
    public void verifyCaptcha(String code,String id) {
        if (StringUtils.isBlank(code) || StringUtils.isBlank(id)){
            throw new BusinessException("验证码不能为空");
        }
        String cacheKey = CacheConstants.CAPTCHA_CODE_KEY + id;
        String value = redisUtils.get(cacheKey);
        if (StringUtils.isBlank(value)){
            throw new BusinessException("验证码已过期");
        }
        redisUtils.delete(cacheKey);
        if (!code.equals(value)){
            throw new BusinessException("验证码不正确");
        }
    }

}
