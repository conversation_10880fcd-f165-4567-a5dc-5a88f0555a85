package com.neo.module.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.common.QueryGenerator;
import com.neo.module.entity.SysDept;
import com.neo.module.mapper.SysDeptMapper;
import com.neo.module.service.SysDeptService;
import com.neo.module.entity.vo.SysDeptTree;
import com.neo.utils.TreeUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-21
 */
@Service
public class SysDeptServiceImpl extends ServiceImpl<SysDeptMapper, SysDept> implements SysDeptService {

    @Resource
    private SysDeptMapper sysDeptMapper;

    @Override
    public IPage<SysDept> page(Map<String, String> params, QueryWrapper<SysDept> queryWrapper) {
        String value;
        queryWrapper
                .eq(StringUtils.isNotBlank((value = params.get("parentId"))), "parent_id", value)
                .eq(StringUtils.isNotBlank((value = params.get("ancestors"))), "ancestors", value)
                .eq(StringUtils.isNotBlank((value = params.get("name"))), "name", value)
                .eq(StringUtils.isNotBlank((value = params.get("sort"))), "sort", value)
                .eq(StringUtils.isNotBlank((value = params.get("leader"))), "leader", value)
                .eq(StringUtils.isNotBlank((value = params.get("phone"))), "phone", value)
                .eq(StringUtils.isNotBlank((value = params.get("email"))), "email", value)
                .eq(StringUtils.isNotBlank((value = params.get("status"))), "status", value)
        ;
        IPage<SysDept> page = sysDeptMapper.selectPage(QueryGenerator.initPage(params), queryWrapper);
        return page;
    }

    @Override
    public List<SysDept> list(SysDept sysDept) {
        QueryWrapper<SysDept> queryWrapper = new QueryWrapper<>(sysDept);
        queryWrapper.orderByAsc("sort");
        List<SysDept> sysDepts = sysDeptMapper.selectList(queryWrapper);
        return sysDepts;
    }

    @Override
    public List<SysDeptTree> listTree(SysDept sysDept) {
        List<SysDept> depts = this.list(sysDept);
        if (CollectionUtil.isEmpty(depts)) {
            return Collections.emptyList();
        }
        return TreeUtils.getInstance().convertTree(depts, SysDeptTree.class, "0");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveDept(SysDept sysDept) {
        // 构建 ancestors 字段
        buildAncestors(sysDept);
        return this.save(sysDept);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDept(SysDept sysDept) {
        // 获取更新前的部门信息
        SysDept oldDept = this.getById(sysDept.getId());
        if (oldDept == null) {
            return false;
        }

        // 构建新的 ancestors 字段
        buildAncestors(sysDept);
        
        // 检查父部门是否发生变化
        boolean parentChanged = !StrUtil.equals(oldDept.getParentId(), sysDept.getParentId());
        
        // 更新当前部门
        boolean result = this.updateById(sysDept);
        
        // 如果父部门发生变化，需要更新所有子部门的 ancestors
        if (result && parentChanged) {
            updateChildrenAncestors(sysDept.getId(), sysDept.getAncestors());
        }
        
        return result;
    }

    /**
     * 构建部门的 ancestors 字段
     *
     * @param sysDept 部门对象
     */
    private void buildAncestors(SysDept sysDept) {
        String parentId = sysDept.getParentId();
        
        // 如果没有父部门或父部门ID为0，则设置为根节点
        if (StrUtil.isBlank(parentId) || "0".equals(parentId)) {
            sysDept.setAncestors("0");
            return;
        }
        
        // 获取父部门信息
        SysDept parentDept = this.getById(parentId);
        if (parentDept == null) {
            // 父部门不存在，设置为根节点
            sysDept.setAncestors("0");
            sysDept.setParentId("0");
        } else {
            // 构建 ancestors：父部门的ancestors + "," + 父部门ID
            String parentAncestors = StrUtil.isBlank(parentDept.getAncestors()) ? "0" : parentDept.getAncestors();
            sysDept.setAncestors(parentAncestors + "," + parentId);
        }
    }

    /**
     * 更新所有子部门的 ancestors 字段
     *
     * @param deptId 部门ID
     * @param newAncestors 新的 ancestors
     */
    private void updateChildrenAncestors(String deptId, String newAncestors) {
        // 查找所有子部门
        QueryWrapper<SysDept> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("parent_id", deptId);
        List<SysDept> children = this.list(queryWrapper);
        
        for (SysDept child : children) {
            // 更新子部门的 ancestors
            String childAncestors = newAncestors + "," + deptId;
            child.setAncestors(childAncestors);
            this.updateById(child);
            
            // 递归更新子部门的子部门
            updateChildrenAncestors(child.getId(), childAncestors);
        }
    }

}
