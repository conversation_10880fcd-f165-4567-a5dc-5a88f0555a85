/**
 * 文件相关工具函数
 */

/**
 * 格式化文件大小
 * @param size 文件大小（字节）
 * @returns 格式化后的文件大小字符串
 */
export const formatFileSize = (size: number | undefined): string => {
  if (!size || size === 0) return '0 B';
  
  const units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
  let index = 0;
  let fileSize = size;
  
  while (fileSize >= 1024 && index < units.length - 1) {
    fileSize /= 1024;
    index++;
  }
  
  return `${fileSize.toFixed(2)} ${units[index]}`;
};

/**
 * 获取文件类型对应的图标名称
 * @param fileName 文件名或文件类型
 * @returns 图标名称
 */
export const getFileIcon = (fileName: string | undefined): string => {
  if (!fileName) return 'file';
  
  // 提取文件扩展名
  const extension = fileName.includes('.') 
    ? fileName.split('.').pop()?.toLowerCase() 
    : fileName.toLowerCase();
    
  if (!extension) return 'file';
  
  // 图片文件
  if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'ico'].includes(extension)) {
    return 'image';
  }
  
  // PDF文件
  if (['pdf'].includes(extension)) {
    return 'file-pdf';
  }
  
  // Word文档
  if (['doc', 'docx'].includes(extension)) {
    return 'file-word';
  }
  
  // Excel表格
  if (['xls', 'xlsx', 'csv'].includes(extension)) {
    return 'file-excel';
  }
  
  // PowerPoint演示文稿
  if (['ppt', 'pptx'].includes(extension)) {
    return 'file-powerpoint';
  }
  
  // 压缩文件
  if (['zip', 'rar', '7z', 'tar', 'gz', 'bz2'].includes(extension)) {
    return 'file-zip';
  }
  
  // 视频文件
  if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv', 'webm'].includes(extension)) {
    return 'video';
  }
  
  // 音频文件
  if (['mp3', 'wav', 'flac', 'aac', 'ogg', 'wma'].includes(extension)) {
    return 'music';
  }
  
  // 代码文件
  if (['js', 'ts', 'jsx', 'tsx', 'vue', 'html', 'css', 'scss', 'less', 'json', 'xml', 'yaml', 'yml'].includes(extension)) {
    return 'code';
  }
  
  // 文本文件
  if (['txt', 'md', 'log'].includes(extension)) {
    return 'file-text';
  }
  
  return 'file';
};

/**
 * 获取文件类型的中文描述
 * @param fileType 文件类型
 * @returns 中文描述
 */
export const getFileTypeDescription = (fileType: string | undefined): string => {
  if (!fileType) return '未知类型';
  
  const type = fileType.toLowerCase();
  
  const typeMap: Record<string, string> = {
    // 图片
    'jpg': 'JPEG图片',
    'jpeg': 'JPEG图片',
    'png': 'PNG图片',
    'gif': 'GIF图片',
    'bmp': 'BMP图片',
    'webp': 'WebP图片',
    'svg': 'SVG矢量图',
    'ico': '图标文件',
    
    // 文档
    'pdf': 'PDF文档',
    'doc': 'Word文档',
    'docx': 'Word文档',
    'xls': 'Excel表格',
    'xlsx': 'Excel表格',
    'ppt': 'PowerPoint演示文稿',
    'pptx': 'PowerPoint演示文稿',
    'txt': '文本文件',
    'md': 'Markdown文档',
    
    // 压缩文件
    'zip': 'ZIP压缩包',
    'rar': 'RAR压缩包',
    '7z': '7Z压缩包',
    'tar': 'TAR归档文件',
    'gz': 'GZ压缩文件',
    
    // 媒体文件
    'mp4': 'MP4视频',
    'avi': 'AVI视频',
    'mov': 'MOV视频',
    'wmv': 'WMV视频',
    'mp3': 'MP3音频',
    'wav': 'WAV音频',
    'flac': 'FLAC音频',
    
    // 代码文件
    'js': 'JavaScript文件',
    'ts': 'TypeScript文件',
    'vue': 'Vue组件',
    'html': 'HTML文件',
    'css': 'CSS样式表',
    'json': 'JSON数据文件',
  };
  
  return typeMap[type] || `${type.toUpperCase()}文件`;
};

/**
 * 下载文件
 * @param blob 文件Blob对象
 * @param fileName 文件名
 */
export const downloadFileFromBlob = (blob: Blob, fileName: string): void => {
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = fileName;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  window.URL.revokeObjectURL(url);
};

/**
 * 通过URL下载文件
 * @param url 文件URL
 * @param fileName 文件名
 */
export const downloadFileFromUrl = (url: string, fileName?: string): void => {
  const link = document.createElement('a');
  link.href = url;
  if (fileName) {
    link.download = fileName;
  }
  link.target = '_blank';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

/**
 * 验证文件类型是否允许
 * @param fileName 文件名
 * @param allowedTypes 允许的文件类型数组
 * @returns 是否允许
 */
export const isFileTypeAllowed = (fileName: string, allowedTypes: string[]): boolean => {
  const extension = fileName.split('.').pop()?.toLowerCase();
  return extension ? allowedTypes.includes(extension) : false;
};

/**
 * 验证文件大小是否符合要求
 * @param fileSize 文件大小（字节）
 * @param maxSize 最大允许大小（字节）
 * @returns 是否符合要求
 */
export const isFileSizeValid = (fileSize: number, maxSize: number): boolean => {
  return fileSize <= maxSize;
};

/**
 * 获取文件扩展名
 * @param fileName 文件名
 * @returns 文件扩展名（小写）
 */
export const getFileExtension = (fileName: string): string => {
  return fileName.split('.').pop()?.toLowerCase() || '';
};

/**
 * 判断是否为图片文件
 * @param fileName 文件名或文件类型
 * @returns 是否为图片
 */
export const isImageFile = (fileName: string): boolean => {
  const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'ico'];
  const extension = getFileExtension(fileName);
  return imageTypes.includes(extension);
};

/**
 * 判断是否为视频文件
 * @param fileName 文件名或文件类型
 * @returns 是否为视频
 */
export const isVideoFile = (fileName: string): boolean => {
  const videoTypes = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv', 'webm'];
  const extension = getFileExtension(fileName);
  return videoTypes.includes(extension);
};

/**
 * 判断是否为音频文件
 * @param fileName 文件名或文件类型
 * @returns 是否为音频
 */
export const isAudioFile = (fileName: string): boolean => {
  const audioTypes = ['mp3', 'wav', 'flac', 'aac', 'ogg', 'wma'];
  const extension = getFileExtension(fileName);
  return audioTypes.includes(extension);
};
