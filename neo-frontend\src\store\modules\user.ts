import { defineStore } from 'pinia';

import { login as loginApi, logout as logoutApi } from '@/api/login';
import { usePermissionStore } from '@/store';
import type { UserInfo } from '@/types/interface';

const InitUserInfo: UserInfo = {
  name: '', // 用户名，用于展示在页面右上角头像处
  roles: [], // 前端权限模型使用 如果使用请配置modules/permission-fe.ts使用
};

export const useUserStore = defineStore('user', {
  state: () => ({
    token: 'main_token', // 默认token不走权限
    userInfo: { ...InitUserInfo },
  }),
  getters: {
    roles: (state: any) => {
      return state.userInfo?.roles;
    },
  },
  actions: {
    async login(userInfo: Record<string, unknown>) {
      try {
        const { account, password, phone, verifyCode, code, codeId } = userInfo;
        
        // 根据登录类型构造请求参数，匹配后端LoginModel
        let loginParams;
        if (phone) {
          loginParams = {
            account: phone as string, // 手机号作为account
            password: verifyCode as string, // 验证码作为password
          };
        } else {
          loginParams = {
            account: account as string,
            password: password as string,
            code: code as string,
            codeId: codeId as string,
          };
        }

        const res = await loginApi(loginParams);
        this.token = res.token;
        this.userInfo = {
          name: res.userInfo.nickname || res.userInfo.username,
          roles: res.userInfo.roles || ['all'],
        };
      } catch (error: any) {
        console.error('登录失败:', error);
        throw new Error(error.message || '登录失败');
      }
    },
    async getUserInfo() {
      const mockRemoteUserInfo = async (token: string) => {
        if (token === 'main_token') {
          return {
            name: 'Tencent',
            roles: ['all'], // 前端权限模型使用 如果使用请配置modules/permission-fe.ts使用
          };
        }
        return {
          name: 'td_dev',
          roles: ['UserIndex', 'DashboardBase', 'login'], // 前端权限模型使用 如果使用请配置modules/permission-fe.ts使用
        };
      };
      const res = await mockRemoteUserInfo(this.token);

      this.userInfo = res;
    },
    async logout() {
      try {
        // await logoutApi();
      } catch (error) {
        console.error('登出失败:', error);
      } finally {
        this.token = '';
        this.userInfo = { ...InitUserInfo };
      }
    },
  },
  persist: {
    afterRestore: () => {
      const permissionStore = usePermissionStore();
      permissionStore.initRoutes();
    },
    key: 'user',
    paths: ['token'],
  },
});