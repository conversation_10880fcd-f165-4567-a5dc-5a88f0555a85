package com.neo.module.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.neo.model.Result;
import com.neo.common.QueryGenerator;
import com.neo.module.core.StorageServiceFactory;
import com.neo.module.entity.SysAttachment;
import com.neo.module.entity.vo.FileVO;
import com.neo.module.service.SysAttachmentService;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.annotation.*;

import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;
import java.util.Set;

/**
 * 附件信息表
 *
 * <AUTHOR>
 * @since 2025-01-06
 */
@RestController
@RequestMapping("/sysAttachment")
public class SysAttachmentController {

    @Resource
    private SysAttachmentService sysAttachmentService;
    @Resource
    private StorageServiceFactory storageServiceFactory;

    /**
     * 分页查询
     *
     * @param params
     * @return
     */
    @PostMapping("/page")
    public Result<?> page(@RequestBody Map<String, String> params) {
        QueryWrapper<SysAttachment> queryWrapper = QueryGenerator.initQueryWrapper(SysAttachment.class, params);
        return Result.ok(sysAttachmentService.page(params, queryWrapper));
    }

    /**
     * 查询
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public Result<?> get(@PathVariable String id) {
        SysAttachment sysAttachment = sysAttachmentService.getById(id);
        return Result.ok(sysAttachment);
    }

    /**
     * 删除
     *
     * @param ids
     * @return
     */
    @DeleteMapping
    public Result<?> delete(@RequestBody Set<String> ids) throws Exception {
        sysAttachmentService.deleteFileByIds(ids);
        return Result.ok();
    }

    /**
     * 上传文件
     *
     * @param file
     * @return
     * @throws Exception
     */
    @PostMapping("/upload")
    public Result upload(@RequestParam("file") MultipartFile file) throws Exception {
        FileVO fileVO = storageServiceFactory.getStorageService().upload(file);
        return Result.ok(fileVO);
    }

    /**
     * 下载文件
     *
     * @param id
     * @param response
     * @throws Exception
     */
    @GetMapping("/download/{id}")
    public void download(@PathVariable String id, HttpServletResponse response) throws Exception {
        sysAttachmentService.downloadFileById(id,response);
    }

}

