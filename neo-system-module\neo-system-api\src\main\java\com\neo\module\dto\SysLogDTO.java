package com.neo.module.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class SysLogDTO  implements Serializable {

    /**
     * 用户id
     */
    private String userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 业务ID
     */
    private String businessId;

    /**
     * 业务模块
     */
    private String businessModule;

    /**
     * 操作类型
     */
    private String operateType;

    /**
     * 操作名称
     */
    private String operateName;

    /**
     * 方法名称
     */
    private String methodName;

    /**
     * 请求路径
     */
    private String requestUrl;

    /**
     * 请求参数
     */
    private String requestParam;

    /**
     * 请求方法
     */
    private String requestMethod;

    /**
     * 返回json
     */
    private String resultJson;

    /**
     * 请求ip
     */
    private String ipAddress;

    /**
     * 执行时间
     */
    private Long costTime;

    /**
     * 创建时间
     */
    private Date createTime;

}
