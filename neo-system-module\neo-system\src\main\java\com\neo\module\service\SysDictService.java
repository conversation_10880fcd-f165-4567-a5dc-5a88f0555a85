package com.neo.module.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.neo.module.entity.SysDict;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 数据字典 服务类
 *
 * <AUTHOR>
 * @since 2023-05-25
 */
public interface SysDictService extends IService<SysDict> {

    IPage<SysDict> page(Map<String, String> params, QueryWrapper<SysDict> queryWrapper);

    List<SysDict> list(SysDict sysDict);

}
