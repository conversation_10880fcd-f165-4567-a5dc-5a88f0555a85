//package com.neo.gateway.filter;
//
//import org.springframework.cloud.gateway.filter.GatewayFilterChain;
//import org.springframework.cloud.gateway.filter.GlobalFilter;
//import org.springframework.core.Ordered;
//import org.springframework.core.io.buffer.DataBuffer;
//import org.springframework.core.io.buffer.DataBufferFactory;
//import org.springframework.core.io.buffer.DataBufferUtils;
//import org.springframework.http.HttpHeaders;
//import org.springframework.http.HttpMethod;
//import org.springframework.http.MediaType;
//import org.springframework.http.server.reactive.ServerHttpRequest;
//import org.springframework.http.server.reactive.ServerHttpRequestDecorator;
//import org.springframework.stereotype.Component;
//import org.springframework.util.LinkedMultiValueMap;
//import org.springframework.util.MultiValueMap;
//import org.springframework.web.server.ServerWebExchange;
//import reactor.core.publisher.Flux;
//import reactor.core.publisher.Mono;
//
//import java.nio.charset.StandardCharsets;
//import java.util.regex.Pattern;
//
//@Component
//public class XssFilter implements GlobalFilter, Ordered {
//
//    // XSS攻击模式
//    private static final Pattern[] XSS_PATTERNS = {
//        Pattern.compile("<script[^>]*>.*?</script>", Pattern.CASE_INSENSITIVE),
//        Pattern.compile("<iframe[^>]*>.*?</iframe>", Pattern.CASE_INSENSITIVE),
//        Pattern.compile("<object[^>]*>.*?</object>", Pattern.CASE_INSENSITIVE),
//        Pattern.compile("<embed[^>]*>.*?</embed>", Pattern.CASE_INSENSITIVE),
//        Pattern.compile("<applet[^>]*>.*?</applet>", Pattern.CASE_INSENSITIVE),
//        Pattern.compile("<meta[^>]*>", Pattern.CASE_INSENSITIVE),
//        Pattern.compile("<link[^>]*>", Pattern.CASE_INSENSITIVE),
//        Pattern.compile("<style[^>]*>.*?</style>", Pattern.CASE_INSENSITIVE),
//        Pattern.compile("javascript:", Pattern.CASE_INSENSITIVE),
//        Pattern.compile("vbscript:", Pattern.CASE_INSENSITIVE),
//        Pattern.compile("onload\\s*=", Pattern.CASE_INSENSITIVE),
//        Pattern.compile("onerror\\s*=", Pattern.CASE_INSENSITIVE),
//        Pattern.compile("onclick\\s*=", Pattern.CASE_INSENSITIVE),
//        Pattern.compile("onmouseover\\s*=", Pattern.CASE_INSENSITIVE),
//        Pattern.compile("onmouseout\\s*=", Pattern.CASE_INSENSITIVE),
//        Pattern.compile("onfocus\\s*=", Pattern.CASE_INSENSITIVE),
//        Pattern.compile("onblur\\s*=", Pattern.CASE_INSENSITIVE),
//        Pattern.compile("onkeypress\\s*=", Pattern.CASE_INSENSITIVE),
//        Pattern.compile("onkeydown\\s*=", Pattern.CASE_INSENSITIVE),
//        Pattern.compile("onkeyup\\s*=", Pattern.CASE_INSENSITIVE),
//        Pattern.compile("onsubmit\\s*=", Pattern.CASE_INSENSITIVE),
//        Pattern.compile("onreset\\s*=", Pattern.CASE_INSENSITIVE),
//        Pattern.compile("onselect\\s*=", Pattern.CASE_INSENSITIVE),
//        Pattern.compile("onchange\\s*=", Pattern.CASE_INSENSITIVE),
//        Pattern.compile("eval\\(", Pattern.CASE_INSENSITIVE),
//        Pattern.compile("expression\\(", Pattern.CASE_INSENSITIVE),
//        Pattern.compile("url\\(", Pattern.CASE_INSENSITIVE),
//        Pattern.compile("<\\s*img[^>]+src[\\s]*=[\\s]*[\"\\']?[\\s]*javascript:", Pattern.CASE_INSENSITIVE)
//    };
//
//    @Override
//    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
//        ServerHttpRequest request = exchange.getRequest();
//
//        // 只处理POST、PUT、PATCH请求的body内容
//        if (shouldFilter(request)) {
//            return filterRequestBody(exchange, chain);
//        }
//
//        // 处理GET请求的查询参数
//        if (HttpMethod.GET.equals(request.getMethod())) {
//            ServerHttpRequest filteredRequest = filterQueryParams(request);
//            ServerWebExchange filteredExchange = exchange.mutate().request(filteredRequest).build();
//            return chain.filter(filteredExchange);
//        }
//
//        return chain.filter(exchange);
//    }
//
//    /**
//     * 判断是否需要过滤请求体
//     */
//    private boolean shouldFilter(ServerHttpRequest request) {
//        HttpMethod method = request.getMethod();
//        if (!HttpMethod.POST.equals(method) && !HttpMethod.PUT.equals(method) && !HttpMethod.PATCH.equals(method)) {
//            return false;
//        }
//
//        MediaType contentType = request.getHeaders().getContentType();
//        return contentType != null &&
//               (MediaType.APPLICATION_JSON.isCompatibleWith(contentType) ||
//                MediaType.APPLICATION_FORM_URLENCODED.isCompatibleWith(contentType) ||
//                MediaType.TEXT_PLAIN.isCompatibleWith(contentType));
//    }
//
//    /**
//     * 过滤请求体内容
//     */
//    private Mono<Void> filterRequestBody(ServerWebExchange exchange, GatewayFilterChain chain) {
//        ServerHttpRequest request = exchange.getRequest();
//        DataBufferFactory bufferFactory = exchange.getResponse().bufferFactory();
//
//        Flux<DataBuffer> body = request.getBody();
//
//        return DataBufferUtils.join(body)
//            .flatMap(dataBuffer -> {
//                byte[] bytes = new byte[dataBuffer.readableByteCount()];
//                dataBuffer.read(bytes);
//                DataBufferUtils.release(dataBuffer);
//
//                String bodyString = new String(bytes, StandardCharsets.UTF_8);
//                String filteredBody = cleanXSS(bodyString);
//
//                DataBuffer filteredBuffer = bufferFactory.wrap(filteredBody.getBytes(StandardCharsets.UTF_8));
//                Flux<DataBuffer> filteredBodyFlux = Flux.just(filteredBuffer);
//
//                ServerHttpRequest filteredRequest = new ServerHttpRequestDecorator(request) {
//                    @Override
//                    public Flux<DataBuffer> getBody() {
//                        return filteredBodyFlux;
//                    }
//
//                    @Override
//                    public HttpHeaders getHeaders() {
//                        HttpHeaders headers = new HttpHeaders();
//                        headers.putAll(super.getHeaders());
//                        headers.setContentLength(filteredBody.getBytes(StandardCharsets.UTF_8).length);
//                        return headers;
//                    }
//                };
//
//                ServerWebExchange filteredExchange = exchange.mutate().request(filteredRequest).build();
//                return chain.filter(filteredExchange);
//            });
//    }
//
//    /**
//     * 过滤GET请求的查询参数
//     */
//    private ServerHttpRequest filterQueryParams(ServerHttpRequest request) {
//        MultiValueMap<String, String> queryParams = request.getQueryParams();
//        boolean hasXss = false;
//
//        for (String key : queryParams.keySet()) {
//            for (String value : queryParams.get(key)) {
//                if (value != null && containsXSS(value)) {
//                    hasXss = true;
//                    break;
//                }
//            }
//            if (hasXss) break;
//        }
//
//        if (!hasXss) {
//            return request;
//        }
//
//        return new ServerHttpRequestDecorator(request) {
//            @Override
//            public MultiValueMap<String, String> getQueryParams() {
//                MultiValueMap<String, String> filteredParams = new LinkedMultiValueMap<>(request.getQueryParams());
//                filteredParams.forEach((key, values) -> {
//                    for (int i = 0; i < values.size(); i++) {
//                        String value = values.get(i);
//                        if (value != null) {
//                            values.set(i, cleanXSS(value));
//                        }
//                    }
//                });
//                return filteredParams;
//            }
//        };
//    }
//
//    /**
//     * 检查字符串是否包含XSS攻击代码
//     */
//    private boolean containsXSS(String value) {
//        if (value == null || value.trim().isEmpty()) {
//            return false;
//        }
//
//        String cleanValue = value.toLowerCase().replaceAll("\\s+", " ");
//
//        for (Pattern pattern : XSS_PATTERNS) {
//            if (pattern.matcher(cleanValue).find()) {
//                return true;
//            }
//        }
//
//        return false;
//    }
//
//    /**
//     * 清理XSS攻击代码
//     */
//    private String cleanXSS(String value) {
//        if (value == null) {
//            return null;
//        }
//
//        String cleanValue = value;
//
//        // 移除XSS攻击模式
//        for (Pattern pattern : XSS_PATTERNS) {
//            cleanValue = pattern.matcher(cleanValue).replaceAll("");
//        }
//
//        // HTML实体编码
//        cleanValue = cleanValue.replace("<", "&lt;")
//                              .replace(">", "&gt;")
//                              .replace("\"", "&quot;")
//                              .replace("'", "&#x27;")
//                              .replace("/", "&#x2F;")
//                              .replace("&", "&amp;");
//
//        return cleanValue;
//    }
//
//    @Override
//    public int getOrder() {
//        return -100;
//    }
//
//}
