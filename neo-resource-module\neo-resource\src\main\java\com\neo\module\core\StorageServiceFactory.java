package com.neo.module.core;

import com.neo.module.service.StorageService;
import com.neo.module.config.properties.FileConfigProperties;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Map;

@Component
public class StorageServiceFactory {

    @Resource
    private FileConfigProperties fileConfigProperties;

    @Resource
    private Map<String, StorageService> storageServices;

    @Getter
    private StorageService storageService;

    @PostConstruct
    public void init() {
        String storageType = fileConfigProperties.getStorageType().toLowerCase();
        storageService = storageServices.get(storageType + "StorageService");
        if (storageService == null) {
            throw new RuntimeException("未找到对应的文件存储服务实现");
        }
    }

}