package com.neo.module.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.common.QueryGenerator;
import com.neo.module.entity.SysLoginLog;
import com.neo.module.mapper.SysLoginLogMapper;
import com.neo.module.service.SysLoginLogService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 登录日志 服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
@Service
public class SysLoginLogServiceImpl extends ServiceImpl<SysLoginLogMapper, SysLoginLog> implements SysLoginLogService {

    @Resource
    private SysLoginLogMapper sysLoginLogMapper;

    @Override
    public IPage<SysLoginLog> page(Map<String, String> params, QueryWrapper<SysLoginLog> queryWrapper) {
        String value;
        queryWrapper
                .eq(StringUtils.isNotBlank((value = params.get("userId"))), "user_id", value)
                .eq(StringUtils.isNotBlank((value = params.get("username"))), "username", value)
                .eq(StringUtils.isNotBlank((value = params.get("ipAddress"))), "ip_address", value)
                .eq(StringUtils.isNotBlank((value = params.get("browser"))), "browser", value)
                .eq(StringUtils.isNotBlank((value = params.get("browserVersion"))), "browser_version", value)
                .eq(StringUtils.isNotBlank((value = params.get("engine"))), "engine", value)
                .eq(StringUtils.isNotBlank((value = params.get("engineVersion"))), "engine_version", value)
                .eq(StringUtils.isNotBlank((value = params.get("os"))), "os", value)
                .eq(StringUtils.isNotBlank((value = params.get("osName"))), "os_name", value)
                .eq(StringUtils.isNotBlank((value = params.get("isMobile"))), "is_mobile", value)
                .eq(StringUtils.isNotBlank((value = params.get("status"))), "status", value)
                .eq(StringUtils.isNotBlank((value = params.get("message"))), "message", value)
        ;
        IPage<SysLoginLog> page = sysLoginLogMapper.selectPage(QueryGenerator.initPage(params), queryWrapper);
        return page;
    }

    @Override
    public List<SysLoginLog> list(SysLoginLog sysLoginLog) {
        QueryWrapper<SysLoginLog> queryWrapper = new QueryWrapper<>(sysLoginLog);
        List<SysLoginLog> sysLoginLogs = sysLoginLogMapper.selectList(queryWrapper);
        return sysLoginLogs;
    }

}
