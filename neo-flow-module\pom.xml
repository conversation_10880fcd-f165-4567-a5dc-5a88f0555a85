<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.neo</groupId>
        <artifactId>neo-parent</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>neo-flow-module</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>neo-flow</module>
        <module>neo-flow-api</module>
    </modules>
    <version>${revision}</version>

    <properties>
        <revision>1.0.0</revision>
    </properties>

    <dependencies>
<!--        <dependency>-->
<!--            <groupId>org.flowable</groupId>-->
<!--            <artifactId>flowable-spring-boot</artifactId>-->
<!--            <version>7.0.1</version>-->
<!--        </dependency>-->
    </dependencies>

</project>