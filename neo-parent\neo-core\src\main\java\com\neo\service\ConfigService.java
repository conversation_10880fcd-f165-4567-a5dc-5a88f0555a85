package com.neo.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.neo.constant.CacheConstants;
import com.neo.module.dto.SysConfigDTO;
import com.neo.module.rpc.SysConfigRpcService;
import com.neo.utils.RedisUtils;
import com.neo.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * 配置服务类
 * 提供多级配置获取：系统属性 -> Redis缓存 -> 数据库
 */
@Slf4j
@Service
public class ConfigService {

    @Resource
    private RedisUtils redisUtils;

    @DubboReference
    private SysConfigRpcService sysConfigRpcService;

    /**
     * 获取配置值
     * @param configKey 配置键
     * @return 配置值，如果不存在返回空字符串
     */
    public String getConfig(String configKey) {
        return getConfig(configKey, "");
    }

    /**
     * 获取配置值
     * @param configKey 配置键
     * @param defaultValue 默认值
     * @return 配置值，如果不存在返回默认值
     */
    public String getConfig(String configKey, String defaultValue) {
        // 参数校验
        if (StringUtils.isBlank(configKey)) {
            log.warn("配置键不能为空");
            return defaultValue;
        }

        try {
            // 1. 优先从系统属性获取
            String value = getConfigBySystem(configKey);
            if (StringUtils.isNotBlank(value)) {
                log.debug("从系统属性获取配置: {} = {}", configKey, value);
                return value;
            }

            // 2. 从Redis缓存获取
            String cacheKey = CacheConstants.SYS_CONFIG_KEY + configKey;
            value = getConfigByCache(cacheKey);
            if (StringUtils.isNotBlank(value)) {
                log.debug("从缓存获取配置: {} = {}", configKey, value);
                return value;
            }

            // 3. 从数据库获取并缓存
            value = getConfigByDb(configKey, cacheKey);
            if (StringUtils.isNotBlank(value)) {
                log.debug("从数据库获取配置: {} = {}", configKey, value);
                return value;
            }

            log.debug("配置不存在，返回默认值: {} = {}", configKey, defaultValue);
            return defaultValue;
            
        } catch (Exception e) {
            log.error("获取配置失败: configKey={}, error={}", configKey, e.getMessage(), e);
            return defaultValue;
        }
    }

    /**
     * 从数据库获取配置并缓存
     */
    private String getConfigByDb(String configKey, String cacheKey) {
        try {
            SysConfigDTO sysConfigDTO = sysConfigRpcService.getConfig(configKey);
            if (sysConfigDTO != null && StringUtils.isNotBlank(sysConfigDTO.getConfigValue())) {
                String configValue = sysConfigDTO.getConfigValue();
                // 缓存配置，设置过期时间为12小时
                redisUtils.set(cacheKey, configValue, CacheConstants.EXPIRATION, TimeUnit.MINUTES);
                return configValue;
            }
        } catch (Exception e) {
            log.error("从数据库获取配置失败: configKey={}, error={}", configKey, e.getMessage(), e);
        }
        return null;
    }

    /**
     * 从Redis缓存获取配置
     * 优化：直接获取值，避免先检查再获取的两次操作
     */
    private String getConfigByCache(String cacheKey) {
        try {
            return redisUtils.get(cacheKey);
        } catch (Exception e) {
            log.error("从缓存获取配置失败: cacheKey={}, error={}", cacheKey, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 从系统属性获取配置
     */
    private String getConfigBySystem(String configKey) {
        return System.getProperty(configKey);
    }

    /**
     * 清除指定配置的缓存
     * @param configKey 配置键
     */
    public void clearConfigCache(String configKey) {
        if (StringUtils.isNotBlank(configKey)) {
            String cacheKey = CacheConstants.SYS_CONFIG_KEY + configKey;
            try {
                redisUtils.delete(cacheKey);
                log.info("清除配置缓存: {}", configKey);
            } catch (Exception e) {
                log.error("清除配置缓存失败: configKey={}, error={}", configKey, e.getMessage(), e);
            }
        }
    }

    /**
     * 刷新配置缓存
     * @param configKey 配置键
     * @return 刷新后的配置值
     */
    public String refreshConfig(String configKey) {
        if (StringUtils.isBlank(configKey)) {
            return null;
        }
        
        // 先清除缓存
        clearConfigCache(configKey);
        
        // 重新获取配置
        return getConfig(configKey);
    }

    /**
     * 获取整数类型配置
     * @param configKey 配置键
     * @param defaultValue 默认值
     * @return 整数配置值
     */
    public Integer getConfigAsInteger(String configKey, Integer defaultValue) {
        try {
            String value = getConfig(configKey);
            return StringUtils.isNotBlank(value) ? Integer.valueOf(value) : defaultValue;
        } catch (NumberFormatException e) {
            log.error("配置值转换为整数失败: configKey={}, value={}", configKey, getConfig(configKey), e);
            return defaultValue;
        }
    }

    /**
     * 获取双精度类型配置
     * @param configKey 配置键
     * @param defaultValue 默认值
     * @return 双精度配置值
     */
    public Double getConfigAsDouble(String configKey, Double defaultValue) {
        try {
            String value = getConfig(configKey);
            return StringUtils.isNotBlank(value) ? Double.valueOf(value) : defaultValue;
        } catch (NumberFormatException e) {
            log.error("配置值转换为双精度失败: configKey={}, value={}", configKey, getConfig(configKey), e);
            return defaultValue;
        }
    }

    /**
     * 获取布尔类型配置
     * @param configKey 配置键
     * @param defaultValue 默认值
     * @return 布尔配置值
     */
    public Boolean getConfigAsBoolean(String configKey, Boolean defaultValue) {
        String value = getConfig(configKey);
        if (StringUtils.isBlank(value)) {
            return defaultValue;
        }
        return "true".equalsIgnoreCase(value) || "1".equals(value) || "yes".equalsIgnoreCase(value);
    }

    /**
     * 获取JSON对象配置
     * @param configKey 配置键
     * @param clazz 目标类型
     * @param defaultValue 默认值
     * @return JSON对象
     */
    public <T> T getConfigAsJson(String configKey, Class<T> clazz, T defaultValue) {
        try {
            String value = getConfig(configKey);
            if (StringUtils.isBlank(value)) {
                return defaultValue;
            }
            // 这里需要引入JSON解析库，如Jackson或Gson
            ObjectMapper mapper = new ObjectMapper();
            return mapper.readValue(value, clazz);
        } catch (Exception e) {
            log.error("配置值转换为JSON对象失败: configKey={}, value={}", configKey, getConfig(configKey), e);
            return defaultValue;
        }
    }

    /**
     * 根据配置类型自动转换获取配置值
     * @param configKey 配置键
     * @param defaultValue 默认值
     * @return 转换后的配置值
     */
    public Object getConfigWithAutoConvert(String configKey, Object defaultValue) {
        // 首先获取配置项信息，包含类型
        SysConfigDTO configDTO = sysConfigRpcService.getConfig(configKey);
        if (configDTO == null || StringUtils.isBlank(configDTO.getConfigValue())) {
            return defaultValue;
        }

        String configType = configDTO.getConfigType();
        String configValue = configDTO.getConfigValue();

        try {
            switch (configType) {
                case "number":
                    return configValue.contains(".") ? Double.parseDouble(configValue) : Integer.parseInt(configValue);
                case "boolean":
                    return "true".equalsIgnoreCase(configValue) || "1".equals(configValue);
                case "json":
                    ObjectMapper mapper = new ObjectMapper();
                    return mapper.readValue(configValue, Object.class);
                case "text":
                default:
                    return configValue;
            }
        } catch (Exception e) {
            log.error("配置值自动转换失败: configKey={}, configType={}, value={}",
                    configKey, configType, configValue, e);
            return defaultValue;
        }
    }

}
