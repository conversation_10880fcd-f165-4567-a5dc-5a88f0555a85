package com.neo.module.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.neo.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

;

/**
 * 数据字典项
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@Accessors(chain = true)
@TableName("sys_dict_item")
public class SysDictItem extends BaseEntity {

    /**
     * 字典代码
     */
    @TableField("dict_id")
    private String dictId;

    /**
     * 字典代码
     */
    @TableField("dict_code")
    private String dictCode;

    /**
     * 字典项文本
     */
    @TableField("item_text")
    private String itemText;

    /**
     * 字典项值
     */
    @TableField("item_value")
    private String itemValue;

    /**
     * 字典项描述
     */
    @TableField("description")
    private String description;

    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 状态
     */
    @TableField("status")
    private String status;


}
