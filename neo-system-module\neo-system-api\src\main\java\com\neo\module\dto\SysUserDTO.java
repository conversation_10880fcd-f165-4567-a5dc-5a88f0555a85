package com.neo.module.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

@Data
public class SysUserDTO implements Serializable {

    private String id;

    /**
     * 用户账号
     */
    private String userName;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 密码
     */
    private String password;

    /**
     * 用户类型（sys系统用户）
     */
    private String userType;

    /**
     * 头像地址
     */
    private String avatar;

    /**
     * 部门ID
     */
    private String deptId;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 手机号码
     */
    private String phoneNumber;

    /**
     * 用户性别（1男 2女 0未知）
     */
    private String gender;

    /**
     * 帐号状态（0停用 1正常）
     */
    private String status;

    /**
     * 权限列表
     */
    private List<String> permissions;

    /**
     * 角色列表
     */
    private List<SysRoleDTO> roleDTOS;

    /**
     * 角色列表
     */
    private List<String> roles;

}
