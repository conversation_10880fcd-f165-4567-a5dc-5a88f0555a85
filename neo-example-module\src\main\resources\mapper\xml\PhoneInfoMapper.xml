<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.neo.module.mapper.PhoneInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.neo.module.entity.PhoneInfo">
        <result column="id" property="id"/>
        <result column="brand" property="brand"/>
        <result column="model" property="model"/>
        <result column="cpu" property="cpu"/>
        <result column="memory_size" property="memorySize"/>
        <result column="storage_size" property="storageSize"/>
        <result column="screen_size" property="screenSize"/>
        <result column="screen_resolution" property="screenResolution"/>
        <result column="camera_main" property="cameraMain"/>
        <result column="camera_front" property="cameraFront"/>
        <result column="battery_capacity" property="batteryCapacity"/>
        <result column="os_type" property="osType"/>
        <result column="os_version" property="osVersion"/>
        <result column="price" property="price"/>
        <result column="release_date" property="releaseDate"/>
        <result column="network_type" property="networkType"/>
        <result column="weight" property="weight"/>
        <result column="color" property="color"/>
        <result column="is_5g_supported" property="is5gSupported"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,brand, model, cpu, memory_size, storage_size, screen_size, screen_resolution, camera_main, camera_front, battery_capacity, os_type, os_version, price, release_date, network_type, weight, color, is_5g_supported
    </sql>

</mapper>
