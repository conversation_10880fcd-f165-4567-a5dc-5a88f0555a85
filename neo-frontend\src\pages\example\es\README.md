# ES手机信息管理页面

## 功能概述

本页面基于 `pages/example/db` 页面参考实现，提供了完整的ElasticSearch手机信息管理功能。

## 主要功能

### 1. ES索引管理
- **索引创建**：创建ES索引结构
- **索引删除**：删除ES索引及所有数据
- **索引状态查看**：显示索引基本信息
- **批量数据导入**：支持JSON文件上传和手动输入

### 2. 搜索功能
- **关键词搜索**：支持品牌、型号、处理器等全文搜索
- **精确筛选**：品牌、操作系统、5G支持等精确匹配
- **范围搜索**：价格范围、内存范围等区间搜索
- **组合搜索**：支持多条件组合搜索

### 3. 数据管理
- **分页展示**：支持分页查看数据
- **新增数据**：添加新的手机信息到ES
- **编辑数据**：修改现有手机信息
- **删除数据**：删除指定手机信息

## 技术实现

### 文件结构
```
neo-frontend/src/pages/example/es/
├── index.vue                    # 主页面
├── components/
│   ├── EditModel.vue           # 编辑弹窗组件
│   └── IndexManager.vue        # 索引管理组件
└── README.md                   # 说明文档

neo-frontend/src/api/
├── esPhoneInfo.ts              # ES API接口
└── model/
    └── esPhoneInfoModel.ts     # ES数据模型
```

### API接口
- `GET /example/es/phoneInfo/{id}` - 根据ID查询
- `POST /example/es/phoneInfo/page` - 分页查询
- `POST /example/es/phoneInfo/list` - 列表查询
- `POST /example/es/phoneInfo/add` - 新增数据
- `POST /example/es/phoneInfo/batchAdd` - 批量新增
- `PUT /example/es/phoneInfo/update` - 更新数据
- `DELETE /example/es/phoneInfo` - 删除数据
- `POST /example/es/phoneInfo/createIndex` - 创建索引
- `POST /example/es/phoneInfo/deleteIndex` - 删除索引

### 数据模型
基于 `PhoneInfo` 模型扩展，增加了ES特有的搜索参数：
- `keyword` - 全文搜索关键词
- `priceMin/priceMax` - 价格范围
- `memorySizeMin/memorySizeMax` - 内存范围
- `storageSizeMin/storageSizeMax` - 存储范围

## 使用说明

### 1. 初始化
1. 访问页面后，首先点击"创建索引"按钮创建ES索引
2. 使用"批量添加示例数据"或"导入JSON数据"添加测试数据

### 2. 搜索数据
1. 在搜索表单中输入搜索条件
2. 支持关键词搜索、精确匹配、范围搜索等多种方式
3. 点击"搜索"按钮执行查询

### 3. 管理数据
1. 点击"新增手机信息"添加新数据
2. 点击表格中的"编辑"按钮修改数据
3. 点击"删除"按钮删除数据

### 4. 批量操作
1. 使用索引管理区域的批量功能
2. 支持JSON文件上传和手动输入数据
3. 提供示例数据模板

## 特色功能

### ES特有搜索能力
- **全文搜索**：利用ES的分词和搜索能力
- **范围查询**：数值类型的范围搜索
- **组合查询**：多条件组合搜索
- **实时搜索**：基于ES的实时搜索能力

### 用户体验优化
- **响应式设计**：适配不同屏幕尺寸
- **加载状态**：操作过程中的加载提示
- **错误处理**：友好的错误提示信息
- **数据验证**：表单数据验证

## 注意事项

1. **索引管理**：删除索引会清空所有数据，请谨慎操作
2. **数据格式**：批量导入时请确保JSON格式正确
3. **网络连接**：确保ES服务正常运行
4. **权限控制**：根据实际需求配置相应权限

## 扩展建议

1. **聚合查询**：添加统计分析功能
2. **高亮显示**：搜索结果关键词高亮
3. **导出功能**：支持搜索结果导出
4. **搜索历史**：保存用户搜索历史
5. **自动完成**：搜索输入自动补全
