package com.neo.module.controller;

import com.neo.aspect.annontation.PrePermissions;
import com.neo.common.BaseController;
import com.neo.model.Result;
import com.neo.module.entity.SysMenu;
import com.neo.module.entity.vo.SysMenuTree;
import com.neo.module.service.SysMenuService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * 菜单权限
 *
 * <AUTHOR>
 * @since 2023-03-28
 */
@RestController
@RequestMapping("/sysMenu")
public class SysMenuController extends BaseController {
    @Resource
    private SysMenuService sysMenuService;

    /**
     * 查询
     *
     * @param id
     * @return
     */
    @PrePermissions("system:sysMenu:get")
    @GetMapping("/{id}")
    public Result<?> get(@PathVariable String id) {
        SysMenu sysMenu = sysMenuService.getById(id);
        return Result.ok(sysMenu);
    }

    /**
     * 列表
     *
     * @param sysMenu
     * @return
     */
    @PrePermissions("system:sysMenu:list")
    @PostMapping("/list")
    public Result<?> list(@RequestBody SysMenu sysMenu) {
        List<SysMenu> list = sysMenuService.list(sysMenu);
        return Result.ok(list);
    }

    /**
     * 树形列表
     *
     * @param sysMenu
     * @return
     */
    @PrePermissions("system:sysMenu:list")
    @PostMapping("/listTree")
    public Result<List<SysMenuTree>> listTree(@RequestBody SysMenu sysMenu) {
        List<SysMenuTree> list = sysMenuService.listTree(sysMenu);
        return Result.ok(list);
    }

    /**
     * 新增
     *
     * @param sysMenu
     * @return
     */
    @PrePermissions("system:sysMenu:add")
    @PostMapping("/add")
    public Result<?> add(@RequestBody SysMenu sysMenu) {
        sysMenuService.save(sysMenu);
        return Result.ok();
    }

    /**
     * 删除
     *
     * @param ids
     * @return
     */
    @PrePermissions("system:sysMenu:delete")
    @DeleteMapping
    public Result<?> delete(@RequestBody Set<String> ids) {
        sysMenuService.removeByIds(ids);
        return Result.ok();
    }

    /**
     * 更新
     *
     * @param sysMenu
     * @return
     */
    @PrePermissions("system:sysMenu:update")
    @PutMapping("/update")
    public Result<?> update(@RequestBody SysMenu sysMenu) {
        sysMenuService.updateById(sysMenu);
        return Result.ok();
    }

    /**
     * 获取用户菜单
     *
     * @return
     */
    @PostMapping("/getMenu")
    public Result<List<SysMenuTree>> getMenu(){
        return Result.ok(sysMenuService.getMenuByUserId(getUserId()));
    }

}