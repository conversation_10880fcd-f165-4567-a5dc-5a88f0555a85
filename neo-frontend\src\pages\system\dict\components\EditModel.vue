<template>
  <t-dialog 
    v-model:visible="dialogVisible" 
    :header="dialogTitle" 
    :width="500" 
    :confirm-btn="{ loading: formLoading }" 
    @confirm="onConfirm" 
    @cancel="onCancel"
  >
    <template v-if="dialogVisible">
      <t-form ref="form" :data="formData" :rules="rules" :label-width="80" @submit="onSubmit">
        <t-form-item label="字典名称" name="dictName">
          <t-input 
            v-model="formData.dictName" 
            placeholder="请输入字典名称"
          />
        </t-form-item>
        
        <t-form-item label="字典编码" name="dictCode">
          <t-input 
            v-model="formData.dictCode" 
            :disabled="isEdit"
            placeholder="请输入字典编码"
          />
        </t-form-item>
        
        <t-form-item label="状态" name="status">
          <t-switch 
            v-model="formData.status" 
            :custom-value="['1', '0']" 
            checked="1" 
            unchecked="0"
            size="small"
          />
        </t-form-item>
        
        <t-form-item label="描述" name="description">
          <t-textarea 
            v-model="formData.description" 
            placeholder="请输入描述"
            :maxlength="500"
            :autosize="{ minRows: 3, maxRows: 5 }"
          />
        </t-form-item>
      </t-form>
    </template>
  </t-dialog>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { addDict, updateDict } from '@/api/dict';
import type { SysDict } from '@/api/model/dictModel';

// 定义 props
interface Props {
  visible: boolean;
  isEdit: boolean;
  dictData?: SysDict;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  isEdit: false,
  dictData: undefined
});

// 定义 emits
const emit = defineEmits<{
  'update:visible': [value: boolean];
  'success': [];
}>();

// 响应式数据
const formLoading = ref(false);
const form = ref();

const formData = ref({
  id: '',
  dictName: '',
  dictCode: '',
  status: '1',
  description: '',
});

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => emit('update:visible', value)
});

const dialogTitle = computed(() => {
  return props.isEdit ? '编辑字典' : '新增字典';
});

const rules = computed(() => ({
  dictName: [
    { required: true, message: '请输入字典名称' }
  ],
  dictCode: [
    { required: true, message: '请输入字典编码' }
  ],
}));

// 初始化表单数据
const initFormData = () => {
  formData.value = {
    id: '',
    dictName: '',
    dictCode: '',
    status: '1',
    description: '',
  };
};

// 加载字典详情数据
const loadDictDetail = () => {
  if (props.dictData) {
    // 使用父组件传递的字典数据
    formData.value = { 
      id: props.dictData.id || '',
      dictName: props.dictData.dictName || '',
      dictCode: props.dictData.dictCode || '',
      status: props.dictData.status || '1',
      description: props.dictData.description || ''
    };
  }
};

// 监听弹窗显示状态
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    initFormData();
    
    if (props.isEdit && props.dictData) {
      // 编辑模式，使用父组件传递的字典数据
      loadDictDetail();
    }
  }
});

// 确认按钮处理
const onConfirm = () => {
  form.value.submit();
};

// 取消按钮处理
const onCancel = () => {
  emit('update:visible', false);
};

// 表单提交处理
const onSubmit = ({ validateResult, firstError }: any) => {
  if (validateResult === true) {
    formLoading.value = true;
    const data = { ...formData.value };

    const promise = props.isEdit ? updateDict(data) : addDict(data);

    promise.then(() => {
      MessagePlugin.success(props.isEdit ? '更新成功' : '添加成功');
      emit('update:visible', false);
      emit('success');
    }).catch((e) => {
      MessagePlugin.error(e.message || (props.isEdit ? '更新失败' : '添加失败'));
    }).finally(() => {
      formLoading.value = false;
    });
  } else {
    console.log('Errors: ', validateResult);
    MessagePlugin.warning(firstError);
  }
};
</script>

<style lang="less" scoped>
// 弹窗内容样式优化
:deep(.t-dialog__body) {
  max-height: 400px;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 16px 24px;
}

:deep(.t-dialog) {
  .t-form {
    overflow: visible;
    width: 100%;
    box-sizing: border-box;
  }
  
  // 确保表单项不会超出宽度
  .t-form-item {
    margin-bottom: 16px;
  }
  
  // 输入框宽度限制
  .t-input,
  .t-switch,
  .t-textarea {
    width: 100%;
  }
}
</style>