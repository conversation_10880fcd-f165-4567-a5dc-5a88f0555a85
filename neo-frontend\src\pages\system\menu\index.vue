<template>
  <div class="table-container">
    <t-card class="list-card-container" :bordered="false">
      <t-row justify="space-between">
        <div class="left-operation-container">
          <t-button @click="handleAdd">新增菜单</t-button>
          <t-button variant="base" theme="default" @click="expandAll(true)">展开全部</t-button>
          <t-button variant="base" theme="default" @click="expandAll(false)">折叠全部</t-button>
        </div>
        <div class="search-input">
          <t-input 
            v-model="filterText" 
            placeholder="请输入菜单名称" 
            clearable
            @change="onFilter"
          >
            <template #suffix-icon>
              <search-icon size="16px" />
            </template>
          </t-input>
        </div>
      </t-row>

      <t-enhanced-table
        ref="tableRef"
        :columns="columns"
        :data="filteredMenuData"
        :tree="{ childrenKey: 'children' }"
        :loading="loading"
        :hover="true"
        :stripe="true"
        v-model:expanded-tree-nodes="expandedNodes"
        row-key="id"
        @expand-change="onExpandChange"
      >
        <template #menuType="{ row }">
          <t-tag v-if="row.menuType === 'M'" theme="primary" variant="light">目录</t-tag>
          <t-tag v-else-if="row.menuType === 'C'" theme="success" variant="light">菜单</t-tag>
          <t-tag v-else-if="row.menuType === 'F'" theme="warning" variant="light">按钮</t-tag>
        </template>
        
        <template #status="{ row }">
          <t-switch 
            :model-value="row.status" 
            :custom-value="['1', '0']" 
            @change="(value: any) => handleStatusChange(row, String(value))"
            size="small"
          />
        </template>
        
        <template #visible="{ row }">
          <t-switch 
            :model-value="row.visible" 
            :custom-value="['1', '0']"
            size="small"
            @change="(value: any) => handleVisibleChange(row, String(value))"
          />
        </template>
        
        <template #op="{ row }">
          <t-space :size="8">
            <t-link theme="primary" hover="color" @click="handleEdit(row)">编辑</t-link>
            <t-link 
              v-if="row.menuType !== 'F'" 
              theme="success" 
              hover="color" 
              @click="handleAddChild(row)"
            >
              新增子菜单
            </t-link>
            <t-popconfirm content="确认删除吗？" @confirm="handleDelete(row)">
              <t-link theme="danger" hover="color">删除</t-link>
            </t-popconfirm>
          </t-space>
        </template>
      </t-enhanced-table>
    </t-card>

    <!-- 菜单编辑弹窗组件 -->
    <EditModel
      v-model:visible="formVisible"
      :is-edit="isEdit"
      :menu-data="currentMenuData"
      :menu-select-options="menuSelectOptions"
      @success="refresh"
    />
  </div>
</template>

<script setup lang="ts">
import { SearchIcon } from 'tdesign-icons-vue-next';
import { MessagePlugin, DialogPlugin, EnhancedTable as TEnhancedTable } from 'tdesign-vue-next';
import { onMounted, ref, computed } from 'vue';
import type { PrimaryTableCol, TreeNodeValue, ExpandOptions } from 'tdesign-vue-next';

import { 
  getMenuTree, 
  deleteMenu,
  updateMenu, 
  type SysMenu
} from '@/api/menu';
import EditModel from './components/EditModel.vue';

defineOptions({
  name: 'MenuManage',
});

// 表格相关
const tableRef = ref();
const loading = ref(false);
const menuData = ref<SysMenu[]>([]);
const expandedNodes = ref<TreeNodeValue[]>([]);
const filterText = ref('');

// 表格列定义
const columns: PrimaryTableCol[] = [
  { 
    colKey: 'name', 
    title: '菜单名称', 
    width: 200,
    ellipsis: true
  },
  { 
    colKey: 'menuType', 
    title: '菜单类型', 
    width: 80,
    align: 'center'
  },
  { 
    colKey: 'icon', 
    title: '图标', 
    width: 80,
    align: 'center'
  },
  { 
    colKey: 'sort', 
    title: '排序', 
    width: 80,
    align: 'center'
  },
  { 
    colKey: 'permissionCode', 
    title: '权限标识', 
    width: 150,
    ellipsis: true
  },
  { 
    colKey: 'path', 
    title: '路由地址', 
    width: 150,
    ellipsis: true
  },
  { 
    colKey: 'visible', 
    title: '显示状态', 
    width: 100,
    align: 'center'
  },
  { 
    colKey: 'status', 
    title: '菜单状态', 
    width: 100,
    align: 'center'
  },
  { 
    colKey: 'op', 
    title: '操作', 
    width: 200, 
    fixed: 'right',
    align: 'center'
  },
];

// 过滤后的菜单数据
const filteredMenuData = computed(() => {
  if (!filterText.value) {
    return menuData.value;
  }
  
  const filterLower = filterText.value.toLowerCase();
  return filterMenuData(menuData.value, filterLower);
});

// 过滤菜单数据
const filterMenuData = (data: SysMenu[], filterText: string): SysMenu[] => {
  if (!filterText) return data;
  
  return data.reduce((acc: SysMenu[], item) => {
    // 检查当前节点是否匹配
    const isMatch = item.name?.toLowerCase().includes(filterText);
    
    // 递归处理子节点
    const filteredChildren = item.children ? filterMenuData(item.children, filterText) : [];
    
    // 如果当前节点匹配或者有子节点匹配，则保留该节点
    if (isMatch || filteredChildren.length > 0) {
      acc.push({
        ...item,
        children: filteredChildren
      });
    }
    
    return acc;
  }, []);
};

// 弹窗相关
const formVisible = ref(false);
const isEdit = ref(false);
const currentMenuData = ref<SysMenu>();

// 菜单选项
const menuSelectOptions = ref([]);

// 获取菜单数据
const getMenuData = async () => {
  loading.value = true;
  try {
    const res = await getMenuTree({});
    menuData.value = Array.isArray(res) ? res : [];
    // 为树形选择器添加"顶级菜单"选项
    menuSelectOptions.value = [{
      label: '根菜单',
      value: '0',
      children: transformToTreeOptions(Array.isArray(res) ? res : [])
    }];
    // 默认展开所有节点
    setTimeout(() => {
      expandAll(true);
    }, 100);
  } catch (error) {
    MessagePlugin.error('获取菜单数据失败');
  } finally {
    loading.value = false;
  }
};

// 转换菜单数据为树形选项
const transformToTreeOptions = (data: SysMenu[]): any[] => {
  return data.map(item => ({
    label: item.name,
    value: item.id || '',
    children: item.children ? transformToTreeOptions(item.children) : undefined,
  })).filter(item => item.label); // 过滤掉没有名称的项
};

// 刷新数据
const refresh = () => {
  getMenuData();
};

/**
 * 获取所有节点的ID
 * @param data - 菜单数据
 * @returns 节点ID数组
 */
const getAllNodeIds = (data: SysMenu[]): TreeNodeValue[] => {
  let ids: TreeNodeValue[] = [];
  
  const traverse = (nodes: SysMenu[]) => {
    for (const node of nodes) {
      ids.push(node.id!);
      if (node.children?.length) {
        traverse(node.children);
      }
    }
  };
  
  traverse(data);
  return ids;
};

// 展开/折叠所有节点
const expandAll = (expand: boolean) => {
  if (expand) {
    // 展开所有节点
    tableRef.value?.expandAll();
  } else {
    // 折叠所有节点
    tableRef.value?.foldAll();
  }
};

// 处理节点展开/折叠
const onExpandChange = (expanded: TreeNodeValue[], options: ExpandOptions<unknown>) => {
  expandedNodes.value = expanded;
};

// 过滤处理
const onFilter = () => {
  // 当过滤文本变化时，展开所有节点以确保能看到匹配的结果
  if (filterText.value) {
    setTimeout(() => {
      expandAll(true);
    }, 0);
  }
};

// 新增菜单
const handleAdd = () => {
  isEdit.value = false;
  currentMenuData.value = undefined;
  formVisible.value = true;
};

// 编辑菜单
const handleEdit = (row: SysMenu) => {
  isEdit.value = true;
  currentMenuData.value = row;
  formVisible.value = true;
};

// 新增子菜单
const handleAddChild = (row: SysMenu) => {
  isEdit.value = false;
  currentMenuData.value = {
    parentId: row.id
  } as SysMenu;
  formVisible.value = true;
};

// 删除菜单
const handleDelete = async (row: SysMenu) => {
  try {
    await deleteMenu([row.id!]);
    MessagePlugin.success('删除成功');
    refresh();
  } catch (error) {
    MessagePlugin.error('删除失败');
  }
};

// 显示状态变更
const handleVisibleChange = (record: SysMenu, newValue: string) => {
  const valueText = newValue === '1' ? '显示' : '隐藏';
  
  const dialog = DialogPlugin.confirm({
    header: '确认修改',
    body: `确定要将菜单"${record.name}"的显示状态修改为"${valueText}"吗？`,
    confirmBtn: '确认',
    cancelBtn: '取消',
    onConfirm: async () => {
      try {
        await updateMenu({ ...record, visible: newValue });
        MessagePlugin.success('显示状态修改成功');
        // 更新本地数据
        record.visible = newValue;
        // 关闭弹窗
        dialog.destroy();
      } catch (error) {
        MessagePlugin.error('显示状态修改失败');
        // 失败时也要关闭弹窗
        dialog.destroy();
      }
    },
    onCancel: () => {
      // 取消时关闭弹窗
      dialog.destroy();
    }
  });
};

// 菜单状态变更
const handleStatusChange = (record: SysMenu, newValue: string) => {
  const valueText = newValue === '1' ? '启用' : '禁用';
  
  const dialog = DialogPlugin.confirm({
    header: '确认修改',
    body: `确定要将菜单"${record.name}"的状态修改为"${valueText}"吗？`,
    confirmBtn: '确认',
    cancelBtn: '取消',
    onConfirm: async () => {
      try {
        await updateMenu({ ...record, status: newValue });
        MessagePlugin.success('菜单状态修改成功');
        // 更新本地数据
        record.status = newValue;
        // 关闭弹窗
        dialog.destroy();
      } catch (error) {
        MessagePlugin.error('菜单状态修改失败');
        // 失败时也要关闭弹窗
        dialog.destroy();
      }
    },
    onCancel: () => {
      // 取消时关闭弹窗
      dialog.destroy();
    }
  });
};

// 挂载时获取数据
onMounted(() => {
  refresh();
});
</script>

<style lang="less" scoped>
.table-container {
  background-color: var(--td-bg-color-container);
  border-radius: var(--td-radius-medium);
  min-height: 600px;
  padding: var(--td-comp-paddingTB-xxl) var(--td-comp-paddingLR-xxl);
}

.left-operation-container {
  display: flex;
  align-items: center;
  margin-bottom: var(--td-comp-margin-xxl);
  gap: var(--td-comp-margin-l);
}

.search-input {
  width: 360px;
}
</style>