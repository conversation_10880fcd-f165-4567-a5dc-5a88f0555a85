import { request } from '@/utils/request';
import { SysRole, RoleListResult, RolePageParams } from './model/roleModel';

enum Api {
  RoleList = '/system/sysRole/page',
  RoleInfo = '/system/sysRole',
  AddRole = '/system/sysRole/add',
  UpdateRole = '/system/sysRole/update',
  DeleteRole = '/system/sysRole',
  RoleSelectList = '/system/sysRole/list',
}

/**
 * 分页查询角色列表
 * @param params
 */
export const getRolePage = (params: RolePageParams) => {
  return request.post<RoleListResult>({
    url: Api.RoleList,
    params,
  });
};

/**
 * 获取角色详情
 * @param id
 */
export const getRoleInfo = (id: string) => {
  return request.get<SysRole>({
    url: `${Api.RoleInfo}/${id}`,
  });
};

/**
 * 添加角色
 * @param params
 */
export const addRole = (params: SysRole) => {
  return request.post({
    url: Api.AddRole,
    params,
  });
};

/**
 * 更新角色
 * @param params
 */
export const updateRole = (params: SysRole) => {
  return request.put({
    url: Api.UpdateRole,
    params,
  });
};

/**
 * 删除角色
 * @param ids
 */
export const deleteRole = (ids: string[]) => {
  return request.delete({
    url: Api.DeleteRole,
    params: ids,
  });
};

/**
 * 获取角色选择列表
 */
export const getRoleSelectList = () => {
  return request.post<SysRole[]>({
    url: Api.RoleSelectList,
  });
};