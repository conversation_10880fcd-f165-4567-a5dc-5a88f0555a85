import { request } from '@/utils/request';
import { SysConfig, ConfigListResult, ConfigPageParams } from './model/configModel';

enum Api {
  ConfigList = '/system/sysConfig/page',
  ConfigInfo = '/system/sysConfig',
  AddConfig = '/system/sysConfig/add',
  UpdateConfig = '/system/sysConfig/update',
  DeleteConfig = '/system/sysConfig',
  ConfigSelectList = '/system/sysConfig/list',
}

/**
 * 分页查询系统配置列表
 * @param params
 */
export const getConfigPage = (params: ConfigPageParams) => {
  return request.post<ConfigListResult>({
    url: Api.ConfigList,
    params,
  });
};

/**
 * 获取系统配置详情
 * @param id
 */
export const getConfigInfo = (id: string) => {
  return request.get<SysConfig>({
    url: `${Api.ConfigInfo}/${id}`,
  });
};

/**
 * 添加系统配置
 * @param params
 */
export const addConfig = (params: SysConfig) => {
  return request.post({
    url: Api.AddConfig,
    params,
  });
};

/**
 * 更新系统配置
 * @param params
 */
export const updateConfig = (params: SysConfig) => {
  return request.put({
    url: Api.UpdateConfig,
    params,
  });
};

/**
 * 删除系统配置
 * @param ids
 */
export const deleteConfig = (ids: string[]) => {
  return request.delete({
    url: Api.DeleteConfig,
    params: ids,
  });
};

/**
 * 获取系统配置选择列表
 */
export const getConfigSelectList = (params?: SysConfig) => {
  return request.post<SysConfig[]>({
    url: Api.ConfigSelectList,
    params: params || {},
  });
};