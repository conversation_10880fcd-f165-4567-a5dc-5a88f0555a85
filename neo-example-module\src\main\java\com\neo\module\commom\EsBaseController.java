package com.neo.module.commom;

import com.neo.common.BaseController;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@Slf4j
public class EsBaseController extends BaseController {

    /**
     * 获取当前页码
     * @param params 请求参数Map
     * @return 当前页码，默认为1
     */
    protected int getCurrentPage(Map<String, String> params) {
        if (params == null) {
            return 1;
        }
        String current = params.get("current");
        if (current == null || current.trim().isEmpty()) {
            return 1;
        }
        try {
            return Integer.parseInt(current);
        } catch (NumberFormatException e) {
            log.warn("Invalid current page parameter: {}, using default value 1", current);
            return 1;
        }
    }

    /**
     * 获取页面大小
     * @param params 请求参数Map
     * @return 页面大小，默认为10，最大为500
     */
    protected int getPageSize(Map<String, String> params) {
        if (params == null) {
            return 10;
        }
        String pageSize = params.get("pageSize");
        if (pageSize == null || pageSize.trim().isEmpty()) {
            return 10;
        }
        try {
            int size = Integer.parseInt(pageSize);
            // 限制最大查询数量为500条每页
            if (size > 500) {
                log.warn("Page size {} exceeds maximum limit 500, using 500", size);
                return 500;
            }
            if (size < 1) {
                log.warn("Page size {} is less than 1, using default value 10", size);
                return 10;
            }
            return size;
        } catch (NumberFormatException e) {
            log.warn("Invalid page size parameter: {}, using default value 10", pageSize);
            return 10;
        }
    }

}
