package com.neo.example;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.neo.annotation.DataScope;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 数据权限使用示例
 * 展示如何在Service中使用@DataScope注解实现数据权限过滤
 * 
 * <AUTHOR>
 */
@Service
public class DataScopeExampleService {

    /**
     * 示例1：基本使用 - 自动识别部门字段
     * 该方法会根据当前用户的数据权限范围自动过滤数据
     * 默认使用dept_id字段进行权限过滤
     */
    @DataScope
    public List<Object> getUserList() {
        // 这里的查询会自动添加数据权限过滤条件
        // 例如：SELECT * FROM sys_user WHERE dept_id = '当前用户部门ID'
        
        // 实际的查询逻辑...
        return null;
    }

    /**
     * 示例2：指定表别名 - 适用于多表关联查询
     * 当SQL涉及多张表时，需要指定表别名来明确字段归属
     */
    @DataScope(deptAlias = "u", userAlias = "u")
    public List<Object> getUserListWithJoin() {
        // 这里的查询会自动添加数据权限过滤条件
        // 例如：SELECT * FROM sys_user u LEFT JOIN sys_dept d ON u.dept_id = d.id 
        //       WHERE u.dept_id = '当前用户部门ID' OR u.create_by = '当前用户名'
        
        // 实际的查询逻辑...
        return null;
    }

    /**
     * 示例3：自定义权限字段 - 当权限字段不是dept_id时使用
     * 某些表可能使用其他字段作为部门关联字段
     */
    @DataScope(permission = "organization_id")
    public List<Object> getOrganizationData() {
        // 这里的查询会使用organization_id字段进行权限过滤
        // 例如：SELECT * FROM some_table WHERE organization_id = '当前用户部门ID'
        
        // 实际的查询逻辑...
        return null;
    }

    /**
     * 示例4：分页查询中的数据权限
     * 数据权限过滤同样适用于分页查询
     */
    @DataScope(deptAlias = "t")
    public IPage<Object> getPageData(Map<String, String> params) {
        // 创建分页对象
        Page<Object> page = new Page<>();
        // 设置分页参数...
        
        // 创建查询条件
        QueryWrapper<Object> wrapper = new QueryWrapper<>();
        // 添加其他查询条件...
        
        // 执行分页查询，数据权限会自动应用
        // 例如：SELECT * FROM some_table t WHERE t.dept_id IN (...) LIMIT 10
        
        return null;
    }

    /**
     * 示例5：复杂查询场景
     * 在复杂的业务查询中使用数据权限
     */
    @DataScope(deptAlias = "main", userAlias = "main", permission = "dept_id")
    public List<Object> getComplexData(String status, String type) {
        // 构建复杂查询条件
        QueryWrapper<Object> wrapper = new QueryWrapper<>();
        wrapper.eq("status", status)
               .eq("type", type)
               .orderByDesc("create_time");
        
        // 数据权限会自动添加到WHERE条件中
        // 最终SQL类似：
        // SELECT * FROM complex_table main 
        // WHERE status = ? AND type = ? 
        // AND (main.dept_id = '当前用户部门ID' OR main.create_by = '当前用户名')
        // ORDER BY create_time DESC
        
        return null;
    }

    /**
     * 示例6：不使用数据权限的方法
     * 没有@DataScope注解的方法不会应用数据权限过滤
     */
    public List<Object> getAllDataWithoutFilter() {
        // 这个方法不会应用任何数据权限过滤
        // 可以查询所有数据（需要其他权限控制机制保护）
        
        return null;
    }

    /*
     * 数据权限范围说明：
     * 
     * 1. 全部数据权限 (dataScope = "1")
     *    - 不添加任何过滤条件，可以查看所有数据
     *    - 适用于超级管理员等高权限角色
     * 
     * 2. 自定数据权限 (dataScope = "2") 
     *    - 根据角色配置的特定部门进行过滤
     *    - 可以灵活指定可访问的部门范围
     * 
     * 3. 本部门数据权限 (dataScope = "3")
     *    - 只能查看本部门的数据
     *    - 同时可以查看自己创建的数据
     * 
     * 4. 本部门及以下数据权限 (dataScope = "4")
     *    - 可以查看本部门及所有下级部门的数据  
     *    - 适用于部门管理者等角色
     */

}