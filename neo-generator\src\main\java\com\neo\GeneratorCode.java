package com.neo;

import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.DataSourceConfig;
import com.baomidou.mybatisplus.generator.config.GlobalConfig;
import com.baomidou.mybatisplus.generator.config.ITypeConvert;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.config.converts.MySqlTypeConvert;
import com.baomidou.mybatisplus.generator.config.rules.DbColumnType;
import com.baomidou.mybatisplus.generator.config.rules.IColumnType;
import com.baomidou.mybatisplus.generator.engine.VelocityTemplateEngine;
import com.neo.base.BaseEntity;

import java.util.Collections;

/**
 * 代码生成器
 */
public class GeneratorCode {

    public static void main(String[] args) {
        String property = System.getProperty("user.dir");

        String url = "************************************";
        String username = "root";
        String password = "Y3Icbz65MlKjEf2W";
        String tableName = "neo_phone_info";
        String author = "黄炜均";

        FastAutoGenerator.create(new DataSourceConfig.Builder(url, username, password)
                        .typeConvert(new ITypeConvert() {
                            @Override
                            public IColumnType processTypeConvert(GlobalConfig globalConfig, String fieldType) {
                                String t = fieldType.toLowerCase();
                                //类型转换 默认的LocalTime不支持数据字典会报错
                                if (t.contains("datetime")) {
                                    return DbColumnType.DATE;
                                }
                                //其它字段采用默认转换（非mysql数据库可以使用其它默认的数据库转换器）
                                return new MySqlTypeConvert().processTypeConvert(globalConfig, fieldType);
                            }
                        })
                )
                .globalConfig(builder -> {
                    builder.author(author)       // 设置作者
                            .fileOverride()     // 覆盖已生成文件
                            .outputDir(property + "/code/src/main/java")  // 指定输出目录
                    ;
                })
                .packageConfig(builder -> {
                    builder.parent("com.neo")    // 设置父包名
                            .moduleName("module")   // 设置父包模块名
                            .pathInfo(Collections.singletonMap(OutputFile.mapperXml, property + "/code/src/main/resources/mapper/xml"))// 设置mapperXml生成路径
                    ;
                })
                .templateConfig(builder -> {
                    builder.entity("templates/entity.java")
                            .service("templates/service.java")
                            .serviceImpl("templates/serviceImpl.java")
                            .mapper("templates/mapper.java")
                            .mapperXml("templates/mapper.xml")
                            .controller("templates/controller.java")
                            .build();
                })
                .strategyConfig(builder -> {
                    builder.addInclude(tableName)          // 设置需要生成的表名
                            .entityBuilder()
                            .enableRemoveIsPrefix() //开启 Boolean 类型字段移除 is 前缀
                            .enableLombok()         //开启 lombok 模型
                            .disableSerialVersionUID()
                            .enableChainModel()     //开启链式模型
                            .versionColumnName("version")
                            .enableTableFieldAnnotation() //开启生成实体时生成字段注解
                            .versionPropertyName("version")
                            .addSuperEntityColumns("is_deleted", "create_time", "create_by", "update_by", "update_time")
                            .superClass(BaseEntity.class)
                            .serviceBuilder()
                            .formatServiceFileName("%sService")
                            .formatServiceImplFileName("%sServiceImpl")
                            .mapperBuilder()
                            .enableBaseColumnList()
                            .enableBaseResultMap()
                            .controllerBuilder()
                            .enableRestStyle();
                })
                .templateEngine(new VelocityTemplateEngine()) // 使用Freemarker引擎模板，默认的是Velocity引擎模板
                .execute();

    }
}
