package com.neo.module.controller;

import com.neo.model.Result;
import com.neo.module.model.LoginModel;
import com.neo.module.service.CaptchaService;
import com.neo.module.service.LoginService;
import com.neo.service.TokenService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

@Slf4j
@RestController
public class LoginController {

    @Resource
    private LoginService loginService;

    @Resource
    private TokenService tokenService;

    @Resource
    private CaptchaService captchaService;

    @PostMapping("/login")
    public Result<?> login(@RequestBody LoginModel loginModel){
        Map<String, Object> login = loginService.login(loginModel);
        return Result.ok(login);
    }

    @PostMapping("/logout")
    public Result<?> logout(){
        loginService.logout();
        return Result.ok();
    }

    @GetMapping("/captcha")
    public Result<?> captcha(){
        return captchaService.createCode();
    }

}