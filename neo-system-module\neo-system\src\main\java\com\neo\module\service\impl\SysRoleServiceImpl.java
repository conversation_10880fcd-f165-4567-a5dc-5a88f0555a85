package com.neo.module.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.common.QueryGenerator;
import com.neo.module.entity.SysRole;
import com.neo.module.entity.SysRoleDept;
import com.neo.module.entity.SysRoleMenu;
import com.neo.module.entity.SysUserRole;
import com.neo.module.entity.vo.SysRoleVO;
import com.neo.module.mapper.SysRoleDeptMapper;
import com.neo.module.mapper.SysRoleMapper;
import com.neo.module.mapper.SysRoleMenuMapper;
import com.neo.module.mapper.SysUserRoleMapper;
import com.neo.module.service.SysRoleService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 角色信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-25
 */
@Service
public class SysRoleServiceImpl extends ServiceImpl<SysRoleMapper, SysRole> implements SysRoleService {

    @Resource
    private SysRoleMapper sysRoleMapper;
    @Resource
    private SysUserRoleMapper sysUserRoleMapper;
    @Resource
    private SysRoleMenuMapper sysRoleMenuMapper;
    @Resource
    private SysRoleDeptMapper sysRoleDeptMapper;

    @Override
    public IPage<SysRole> page(Map<String, String> params, QueryWrapper<SysRole> queryWrapper) {
        String value;
        queryWrapper
                .eq(StringUtils.isNotBlank((value = params.get("roleName"))), "role_name", value)
                .eq(StringUtils.isNotBlank((value = params.get("roleKey"))), "role_key", value)
                .eq(StringUtils.isNotBlank((value = params.get("roleSort"))), "role_sort", value)
                .eq(StringUtils.isNotBlank((value = params.get("dataScope"))), "data_scope", value)
                .eq(StringUtils.isNotBlank((value = params.get("menuRelation"))), "menu_relation", value)
                .eq(StringUtils.isNotBlank((value = params.get("deptRelation"))), "dept_relation", value)
                .eq(StringUtils.isNotBlank((value = params.get("status"))), "status", value)
                .eq(StringUtils.isNotBlank((value = params.get("remark"))), "remark", value)
        ;
        IPage<SysRole> page = sysRoleMapper.selectPage(QueryGenerator.initPage(params), queryWrapper);
        return page;
    }

    @Override
    public List<SysRole> list(SysRole sysRole) {
        QueryWrapper<SysRole> queryWrapper = new QueryWrapper<>(sysRole);
        return sysRoleMapper.selectList(queryWrapper);
    }

    @Override
    public List<String> getRolesByUserId(String id) {
        return sysUserRoleMapper.selectRolesByUserId(id);
    }

    @Override
    public List<String> getRolesIdsByUserId(String id) {
        List<SysUserRole> sysUserRoles = sysUserRoleMapper.selectList(new LambdaQueryWrapper<SysUserRole>().eq(SysUserRole::getUserId, id));
        return sysUserRoles.stream().map(SysUserRole::getRoleId).collect(Collectors.toList());
    }

    @Override
    public SysRoleVO getRoleById(String id) {
        SysRole sysRole = this.getById(id);
        if (sysRole == null){
            return null;
        }
        SysRoleVO sysRoleVO = new SysRoleVO();
        BeanUtils.copyProperties(sysRole, sysRoleVO);
        sysRoleVO.setMenuIds(sysRoleMenuMapper.selectList(new LambdaQueryWrapper<SysRoleMenu>().eq(SysRoleMenu::getRoleId, id))
                .stream().map(SysRoleMenu::getMenuId).collect(Collectors.toList()));
        sysRoleVO.setDeptIds(sysRoleDeptMapper.selectList(new LambdaQueryWrapper<SysRoleDept>().eq(SysRoleDept::getRoleId, id))
                .stream().map(SysRoleDept::getDeptId).collect(Collectors.toList()));
        return sysRoleVO;
    }

    @Override
    public Boolean updateRole(SysRoleVO sysRoleVO) {
        SysRole sysRole = new SysRole();
        sysRole.setId(sysRoleVO.getId());
        sysRole.setRoleName(sysRoleVO.getRoleName());
        sysRole.setRoleKey(sysRoleVO.getRoleKey());
        sysRole.setRoleSort(sysRoleVO.getRoleSort());
        sysRole.setDataScope(sysRoleVO.getDataScope());
        sysRole.setMenuRelation(sysRoleVO.getMenuRelation());
        sysRole.setDeptRelation(sysRoleVO.getDeptRelation());
        sysRole.setStatus(sysRoleVO.getStatus());
        sysRole.setRemark(sysRoleVO.getRemark());

        List<String> menuIds = sysRoleVO.getMenuIds();
        if (menuIds != null && !menuIds.isEmpty()){
            sysRoleMenuMapper.delete(new LambdaQueryWrapper<SysRoleMenu>().eq(SysRoleMenu::getRoleId, sysRoleVO.getId()));
            for (String menuId : menuIds) {
                SysRoleMenu sysRoleMenu = new SysRoleMenu();
                sysRoleMenu.setRoleId(sysRoleVO.getId());
                sysRoleMenu.setMenuId(menuId);
                sysRoleMenuMapper.insert(sysRoleMenu);
            }
        }

        List<String> deptIds = sysRoleVO.getDeptIds();
        if (deptIds != null && !deptIds.isEmpty()){
            sysRoleDeptMapper.delete(new LambdaQueryWrapper<SysRoleDept>().eq(SysRoleDept::getRoleId, sysRoleVO.getId()));
            for (String deptId : deptIds) {
                SysRoleDept sysRoleDept = new SysRoleDept();
                sysRoleDept.setRoleId(sysRoleVO.getId());
                sysRoleDept.setDeptId(deptId);
                sysRoleDeptMapper.insert(sysRoleDept);
            }
        }

        return this.updateById(sysRole);
    }

}
