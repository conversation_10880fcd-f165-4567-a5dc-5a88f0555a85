<template>
  <div class="es-phone-info-management-container">
    <!-- 页面标题区域 -->
    <div class="page-header">
      <div class="page-title">
        <h2>ES手机信息管理</h2>
        <p class="page-description">基于Elasticsearch的手机信息检索与管理</p>
      </div>
      <div class="index-manager-wrapper">
        <IndexManager @index-changed="onIndexChanged" />
      </div>
    </div>

    <!-- 筛选表单 -->
    <t-card class="filter-card" :bordered="false">
      <t-form :data="searchFormState" :label-width="80" colon @reset="onReset" @submit="onSubmit">
        <!-- 主要筛选条件 -->
        <t-row>
          <t-col :span="10">
            <t-row :gutter="[24, 24]">
              <t-col :span="4">
                <t-form-item label="关键词搜索" name="keyword">
                  <t-input
                    v-model="searchFormState.keyword"
                    class="form-item-content"
                    type="search"
                    placeholder="搜索品牌、型号、处理器等"
                    clearable
                  />
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="手机品牌" name="brand">
                  <t-input
                    v-model="searchFormState.brand"
                    class="form-item-content"
                    type="search"
                    placeholder="请输入手机品牌"
                    clearable
                  />
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="操作系统" name="osType">
                  <t-select
                    v-model="searchFormState.osType"
                    class="form-item-content"
                    :options="osTypeOptions"
                    placeholder="请选择操作系统"
                    clearable
                  />
                </t-form-item>
              </t-col>
            </t-row>
          </t-col>

          <t-col :span="2" class="operation-container">
            <t-button theme="primary" type="submit">
              搜索
            </t-button>
            <t-button type="reset" variant="base" theme="default">
              重置
            </t-button>
            <t-button
              variant="outline"
              theme="default"
              @click="toggleAdvancedSearch"
              :style="{ marginLeft: 'var(--td-comp-margin-s)' }"
            >
              {{ showAdvancedSearch ? '收起' : '高级' }}
              <template #suffix>
                <t-icon :name="showAdvancedSearch ? 'chevron-up' : 'chevron-down'" />
              </template>
            </t-button>
          </t-col>
        </t-row>

        <!-- 高级筛选条件 -->
        <t-collapse-transition>
          <div v-show="showAdvancedSearch" class="advanced-search">
            <t-divider />
            <t-row :gutter="[24, 24]">
              <t-col :span="6">
                <t-form-item label="价格范围" name="priceRange">
                  <t-range-input
                    v-model="priceRange"
                    class="form-item-content"
                    placeholder="最低价格-最高价格"
                    clearable
                  />
                </t-form-item>
              </t-col>
              <t-col :span="6">
                <t-form-item label="内存范围(GB)" name="memoryRange">
                  <t-range-input
                    v-model="memoryRange"
                    class="form-item-content"
                    placeholder="最小内存-最大内存"
                    clearable
                  />
                </t-form-item>
              </t-col>
              <t-col :span="6">
                <t-form-item label="5G支持" name="is5gSupported">
                  <t-select
                    v-model="searchFormState.is5gSupported"
                    class="form-item-content"
                    :options="supportOptions"
                    placeholder="请选择5G支持"
                    clearable
                  />
                </t-form-item>
              </t-col>
            </t-row>
          </div>
        </t-collapse-transition>
      </t-form>
    </t-card>

    <!-- 表格容器 -->
    <t-card class="table-card" :bordered="false">
      <div class="table-header">
        <div class="left-operation-container">
          <t-button @click="handleAdd">新增手机信息</t-button>
          <t-button theme="default" variant="outline" @click="handleRefresh">
            <template #icon>
              <t-icon name="refresh" />
            </template>
            刷新
          </t-button>
        </div>
      </div>

      <t-table
        :data="paginationData.dataSource.value"
        :columns="columns"
        :row-key="rowKey"
        vertical-align="top"
        :hover="true"
        :pagination="paginationData.tableConfig.value"
        :loading="paginationData.loading.value"
        @page-change="(pageInfo: any) => paginationData.handlePageChange(pageInfo, loadEsPhoneInfoData, searchFormState)"
      >
        <template #price="{ row }">
          <span>¥{{ row.price?.toLocaleString() || '-' }}</span>
        </template>

        <template #is5gSupported="{ row }">
          <t-tag v-if="row.is5gSupported === 'Y'" theme="success" variant="light">
            支持
          </t-tag>
          <t-tag v-else theme="default" variant="light">
            不支持
          </t-tag>
        </template>

        <template #op="slotProps">
          <t-space>
            <t-link theme="primary" @click="handleEdit(slotProps.row)">编辑</t-link>
            <t-popconfirm
              content="确定要删除吗？"
              @confirm="handleDelete(slotProps.row)"
            >
              <t-link theme="danger">删除</t-link>
            </t-popconfirm>
          </t-space>
        </template>
      </t-table>
    </t-card>

    <!-- 手机信息编辑弹窗 -->
    <EditModel
      v-model:visible="formVisible"
      :is-edit="isEdit"
      :phone-data="editPhoneData"
      @success="onFormSuccess"
    />
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted, watch } from 'vue';
  import {
    getEsPhoneInfoPage,
    deleteEsPhoneInfo,
    getEsPhoneInfoById,
  } from '@/api/esPhoneInfo';
  import { EsPhoneInfo, EsPhoneInfoPageParams } from '@/api/model/esPhoneInfoModel';
  import { usePagination, CommonPageParams, CommonPageResult } from '@/hooks';
  import EditModel from './components/EditModel.vue';
  import IndexManager from './components/IndexManager.vue';

  // 选项配置
  const osTypeOptions = [
    { value: 'Android', label: 'Android' },
    { value: 'iOS', label: 'iOS' },
    { value: 'HarmonyOS', label: 'HarmonyOS' },
  ];

  const supportOptions = [
    { value: 'Y', label: '支持' },
    { value: 'N', label: '不支持' },
  ];

  // 定义表格列
  const columns = [
    {
      title: '品牌',
      colKey: 'brand',
      width: 100,
    },
    {
      title: '型号',
      colKey: 'model',
      width: 150,
    },
    {
      title: '处理器',
      colKey: 'cpu',
      width: 120,
    },
    {
      title: '内存(GB)',
      colKey: 'memorySize',
      width: 80,
    },
    {
      title: '存储(GB)',
      colKey: 'storageSize',
      width: 80,
    },
    {
      title: '屏幕尺寸',
      colKey: 'screenSize',
      width: 80,
    },
    {
      title: '操作系统',
      colKey: 'osType',
      width: 80,
    },
    {
      title: '价格',
      colKey: 'price',
      width: 100,
    },
    {
      title: '5G支持',
      colKey: 'is5gSupported',
      width: 80,
    },
    {
      title: '操作',
      colKey: 'op',
      width: 150,
    },
  ];

  // 使用通用分页 hook
  const paginationData = usePagination<EsPhoneInfo>({
    defaultCurrent: 1,
    defaultPageSize: 10,
  });

  // 价格范围
  const priceRange = ref<[string, string]>(['', '']);
  // 内存范围
  const memoryRange = ref<[string, string]>(['', '']);

  // 搜索表单
  const searchFormState = reactive<Omit<EsPhoneInfoPageParams, 'current' | 'pageSize'>>({
    keyword: '',
    brand: '',
    osType: '',
    is5gSupported: '',
    priceMin: undefined,
    priceMax: undefined,
    memorySizeMin: undefined,
    memorySizeMax: undefined,
  });

  // 监听价格范围变化
  watch(priceRange, (newVal) => {
    searchFormState.priceMin = newVal[0] ? Number(newVal[0]) : undefined;
    searchFormState.priceMax = newVal[1] ? Number(newVal[1]) : undefined;
  });

  // 监听内存范围变化
  watch(memoryRange, (newVal) => {
    searchFormState.memorySizeMin = newVal[0] ? Number(newVal[0]) : undefined;
    searchFormState.memorySizeMax = newVal[1] ? Number(newVal[1]) : undefined;
  });

  // 对话框相关
  const formVisible = ref(false);
  const isEdit = ref(false);
  const editPhoneData = ref<EsPhoneInfo>();
  const loading = ref(false);

  // 高级搜索状态
  const showAdvancedSearch = ref(false);

  // 切换高级搜索显示状态
  const toggleAdvancedSearch = () => {
    showAdvancedSearch.value = !showAdvancedSearch.value;
  };

  // 数据加载函数
  const loadEsPhoneInfoData = async (params: CommonPageParams): Promise<CommonPageResult<EsPhoneInfo>> => {
    const res = await getEsPhoneInfoPage(params as EsPhoneInfoPageParams);
    return {
      records: res.records,
      total: res.total,
      current: res.current,
      size: params.pageSize || paginationData.tableConfig.value?.pageSize || 10,
    };
  };

  // 查询
  const searchQuery = () => {
    paginationData.resetToFirstPage(loadEsPhoneInfoData, searchFormState);
  };

  // 表单提交
  const onSubmit = () => {
    searchQuery();
  };

  // 表单重置
  const onReset = () => {
    Object.keys(searchFormState).forEach(key => {
      (searchFormState as any)[key] = key.includes('Min') || key.includes('Max') ? undefined : '';
    });
    priceRange.value = ['', ''];
    memoryRange.value = ['', ''];
    searchQuery();
  };

  const rowKey = 'id';

  // 新增
  const handleAdd = () => {
    isEdit.value = false;
    editPhoneData.value = undefined;
    formVisible.value = true;
  };

  // 编辑
  const handleEdit = async (record: EsPhoneInfo) => {
    if (!record.id) {
      console.error('手机信息ID不存在');
      return;
    }

    loading.value = true;
    try {
      // 从后端获取完整的手机信息
      const phoneInfo = await getEsPhoneInfoById(record.id);
      isEdit.value = true;
      editPhoneData.value = phoneInfo;
      formVisible.value = true;
    } catch (error) {
      console.error('获取手机信息失败:', error);
    } finally {
      loading.value = false;
    }
  };

  // 删除
  const handleDelete = async (record: EsPhoneInfo) => {
    try {
      await deleteEsPhoneInfo([record.id || '']);
      searchQuery();
    } catch (error) {
      console.error('删除失败:', error);
    }
  };

  // 刷新
  const handleRefresh = () => {
    paginationData.refreshData(loadEsPhoneInfoData, searchFormState);
  };

  // 表单操作成功回调
  const onFormSuccess = () => {
    paginationData.refreshData(loadEsPhoneInfoData, searchFormState);
  };

  // 索引变化回调
  const onIndexChanged = () => {
    paginationData.refreshData(loadEsPhoneInfoData, searchFormState);
  };

  onMounted(() => {
    paginationData.loadData(paginationData.buildPageParams(searchFormState), loadEsPhoneInfoData);
  });
</script>

<style lang="less" scoped>
.es-phone-info-management-container {
  background-color: var(--td-bg-color-container);
  padding: var(--td-comp-paddingTB-xxl) var(--td-comp-paddingLR-xxl);
  border-radius: var(--td-radius-medium);

  // 页面标题区域
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--td-comp-margin-xxl);
    padding-bottom: var(--td-comp-paddingTB-xl);
    border-bottom: 1px solid var(--td-border-level-1-color);

    .page-title {
      h2 {
        margin: 0 0 var(--td-comp-margin-s) 0;
        font-size: 24px;
        font-weight: 600;
        color: var(--td-text-color-primary);
        line-height: 1.4;
      }

      .page-description {
        margin: 0;
        font-size: 14px;
        color: var(--td-text-color-secondary);
        line-height: 1.5;
      }
    }

    .index-manager-wrapper {
      flex-shrink: 0;
    }
  }

  // 筛选卡片
  .filter-card {
    margin-bottom: var(--td-comp-margin-xxl);

    // 高级搜索区域
    .advanced-search {
      padding-top: var(--td-comp-paddingTB-l);
    }
  }

  // 表格卡片
  .table-card {
    .table-header {
      margin-bottom: var(--td-comp-margin-xl);

      .left-operation-container {
        display: flex;
        align-items: center;
        gap: var(--td-comp-margin-l);
      }
    }
  }
}

// 表单项样式
.form-item-content {
  width: 100%;
}

// 操作按钮容器
.operation-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: var(--td-comp-margin-s);
}
</style>
