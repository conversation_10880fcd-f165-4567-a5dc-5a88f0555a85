package com.neo.module.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.neo.aspect.annontation.PrePermissions;
import com.neo.model.Result;
import com.neo.common.QueryGenerator;
import com.neo.module.entity.SysLoginLog;
import com.neo.module.service.SysLoginLogService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 登录日志
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
@RestController
@RequestMapping("/sysLoginLog")
public class SysLoginLogController {
    @Resource
    private SysLoginLogService sysLoginLogService;

    /**
     * 分页查询
     *
     * @param params
     * @return
     */
    @PrePermissions("system:sysLoginLog:list")
    @PostMapping("/page")
    public Result<?> page(@RequestBody Map<String, String> params) {
        QueryWrapper<SysLoginLog> queryWrapper = QueryGenerator.initQueryWrapper(SysLoginLog.class, params);
        return Result.ok(sysLoginLogService.page(params, queryWrapper));
    }

    /**
     * 查询
     *
     * @param id
     * @return
     */
    @PrePermissions("system:sysLoginLog:get")
    @GetMapping("/{id}")
    public Result<?> get(@PathVariable String id) {
        SysLoginLog sysLoginLog = sysLoginLogService.getById(id);
        return Result.ok(sysLoginLog);
    }

    /**
     * 列表
     *
     * @param sysLoginLog
     * @return
     */
    @PrePermissions("system:sysLoginLog:list")
    @PostMapping("/list")
    public Result<?> list(@RequestBody SysLoginLog sysLoginLog) {
        List<SysLoginLog> list = sysLoginLogService.list(sysLoginLog);
        return Result.ok(list);
    }

}

