<template>
  <div class="table-tree-container">
    <div class="list-tree-wrapper">
      <div class="list-tree-operator">
        <t-input v-model="filterText" placeholder="请输入部门名称" @change="onInput">
          <template #suffix-icon>
            <search-icon size="var(--td-comp-size-xxxs)" />
          </template>
        </t-input>
        <t-tree :data="deptTreeData" activable hover expand-on-click-node :default-expanded="expanded" :filter="filterByText"
          @click="onTreeClick" 
          :keys="{ value: 'id', label:'name', children: 'children' }"
          />
      </div>
      <div class="list-tree-content">
        <t-card class="list-card-container" :bordered="false">
          <t-form :data="filterFormData" :label-width="80" colon @reset="onFilterReset" @submit="onFilterSubmit">
            <t-row>
              <t-col :span="10">
                <t-row :gutter="[24, 24]">
                  <t-col :span="4">
                    <t-form-item label="用户名称" name="userName">
                      <t-input
                        v-model="filterFormData.userName"
                        class="form-item-content"
                        type="search"
                        placeholder="请输入用户名称"
                        :style="{ minWidth: '134px' }"
                      />
                    </t-form-item>
                  </t-col>
                  <t-col :span="4">
                    <t-form-item label="用户编码" name="userCode">
                      <t-input
                        v-model="filterFormData.userCode"
                        class="form-item-content"
                        placeholder="请输入用户编码"
                        :style="{ minWidth: '134px' }"
                      />
                    </t-form-item>
                  </t-col>
                  <t-col :span="4">
                    <t-form-item label="手机号码" name="phoneNumber">
                      <t-input
                        v-model="filterFormData.phoneNumber"
                        class="form-item-content"
                        placeholder="请输入手机号码"
                        :style="{ minWidth: '134px' }"
                      />
                    </t-form-item>
                  </t-col>
                  <t-col :span="4">
                    <t-form-item label="状态" name="status">
                      <t-select
                        v-model="filterFormData.status"
                        class="form-item-content"
                        :options="STATUS_OPTIONS"
                        placeholder="请选择状态"
                        clearable
                      />
                    </t-form-item>
                  </t-col>
                  <t-col :span="5">
                    <t-form-item label="创建时间" name="createTime">
                      <t-date-range-picker
                        v-model="filterFormData.createTime"
                        class="form-item-content"
                        clearable
                      />
                    </t-form-item>
                  </t-col>
                </t-row>
              </t-col>

              <t-col :span="2" class="operation-container">
                <t-button theme="primary" type="submit" :style="{ marginLeft: 'var(--td-comp-margin-s)' }">
                  查询
                </t-button>
                <t-button type="reset" variant="base" theme="default">重置</t-button>
              </t-col>
            </t-row>
          </t-form>

          <t-row justify="space-between">
            <div class="left-operation-container">
              <t-button @click="handleAddUser">新增用户</t-button>
              <t-button 
                variant="base" 
                theme="default" 
                :disabled="!selectedRowKeys.length"
                @click="handleBatchDelete"
              >
                批量删除
              </t-button>
              <p v-if="!!selectedRowKeys.length" class="selected-count">
                已选择 {{ selectedRowKeys.length }} 项
              </p>
            </div>
          </t-row>

          <t-table :data="data" :columns="COLUMNS" :row-key="rowKey" vertical-align="top" :hover="true"
            :pagination="paginationData.tableConfig.value" :selected-row-keys="selectedRowKeys" :loading="dataLoading"
            :header-affixed-top="headerAffixedTop" @page-change="rehandlePageChange" @change="rehandleChange"
            @select-change="(value: (string | number)[]) => rehandleSelectChange(value)">
            <template #status="{ row }">
              <t-tag v-if="row.status === '1'" theme="success" variant="light">
                正常
              </t-tag>
              <t-tag v-if="row.status === '0'" theme="danger" variant="light">
                停用
              </t-tag>
            </template>

            <template #gender="{ row }">
              <p v-if="row.gender === '1'">男</p>
              <p v-if="row.gender === '2'">女</p>
              <p v-if="row.gender === '0'">未知</p>
            </template>

            <template #op="slotProps">
              <t-space>
                <t-link theme="primary" @click="handleEdit(slotProps.row)">编辑</t-link>
                <t-link theme="danger" @click="handleDelete(slotProps.row)">删除</t-link>
              </t-space>
            </template>
          </t-table>
        </t-card>

        <!-- 用户编辑弹窗 -->
        <EditModel 
          v-model:visible="formVisible" 
          :is-edit="isEdit" 
          :user-data="editUserData"
          :dept-tree-data="deptTreeData"
          @success="onFormSuccess"
        />

        <t-dialog v-model:visible="confirmVisible" header="确认删除当前用户？" :body="confirmBody" :on-cancel="onCancelDelete"
          @confirm="onConfirmDelete" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { SearchIcon } from 'tdesign-icons-vue-next';
import type { PrimaryTableCol, TableRowData, TreeNodeModel, TreeProps } from 'tdesign-vue-next';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import { computed, onMounted, ref } from 'vue';

import { getDeptTree } from '@/api/dept';
import { getUserPage, getUserById, deleteUser } from '@/api/user';
import type { UserPageParams } from '@/api/model/userModel';
import { usePagination, CommonPageParams, CommonPageResult } from '@/hooks';
import { prefix } from '@/config/global';
import { useSettingStore } from '@/store';
import EditModel from './components/EditModel.vue';

defineOptions({
  name: 'ListUser',
});

const store = useSettingStore();

const COLUMNS: PrimaryTableCol<TableRowData>[] = [
  { colKey: 'row-select', type: 'multiple', width: 64, fixed: 'left' },
  {
    title: '用户账号',
    align: 'left',
    width: 150,
    colKey: 'userName',
    fixed: 'left',
  },
  {
    title: '用户昵称',
    width: 150,
    colKey: 'nickName'
  },
  {
    title: '邮箱',
    width: 200,
    ellipsis: true,
    colKey: 'email'
  },
  {
    title: '手机号',
    width: 150,
    colKey: 'phoneNumber'
  },
  {
    title: '性别',
    width: 80,
    colKey: 'gender',
    align: 'center'
  },
  {
    title: '状态',
    width: 100,
    colKey: 'status',
    align: 'center'
  },
  {
    title: '创建时间',
    width: 200,
    ellipsis: true,
    colKey: 'createTime',
  },
  {
    title: '操作',
    align: 'left',
    fixed: 'right',
    width: 160,
    colKey: 'op',
  },
];

const searchValue = ref('');
const filterText = ref('');
const filterByText = ref();
const deptTreeData = ref([]);
const expanded = ref([]);
const selectedDeptId = ref('');

// 添加筛选表单相关数据
const STATUS_OPTIONS = [
  { label: '正常', value: '1' },
  { label: '停用', value: '0' },
];

// 筛选表单
const filterFormData = ref({
  userName: '',
  userCode: '',
  phoneNumber: '',
  status: '',
  createTime: []
});

// 使用通用分页 hook
const paginationData = usePagination<any>({
  defaultCurrent: 1,
  defaultPageSize: 20,
  paramType: 'default' // 使用 current/size 参数
});

// 将原来的 data 和 pagination 替换为通用分页数据
const data = paginationData.dataSource;
const pagination = paginationData.pagination;
const dataLoading = paginationData.loading;
// 数据加载函数
const loadUserData = async (params: CommonPageParams): Promise<CommonPageResult<any>> => {
  const res = await getUserPage(params);
  return {
    records: res.records,
    total: res.total,
    current: res.current,
    size: res.size,
  };
};

const fetchData = async () => {
  const params = {
    userName: searchValue.value,
    deptId: selectedDeptId.value,
    // 添加筛选参数
    filterUserName: filterFormData.value.userName,
    filterUserCode: filterFormData.value.userCode,
    filterPhoneNumber: filterFormData.value.phoneNumber,
    filterStatus: filterFormData.value.status,
    createBeginTime: filterFormData.value.createTime && filterFormData.value.createTime.length > 0 ? filterFormData.value.createTime[0] : null,
    createendTime: filterFormData.value.createTime && filterFormData.value.createTime.length > 1 ? filterFormData.value.createTime[1] : null
  };
  await paginationData.refreshData(loadUserData, params);
};

// 筛选表单相关方法
const onFilterSubmit = () => {
  paginationData.pagination.current = 1;
  fetchData();
};

const onFilterReset = () => {
  filterFormData.value = {
    userName: '',
    userCode: '',
    phoneNumber: '',
    status: '',
    createTime: []
  };
  paginationData.pagination.current = 1;
  fetchData();
};

const deleteIdx = ref(-1);
const confirmBody = computed(() => {
  if (deleteIdx.value > -1) {
    const { userName } = data.value[deleteIdx.value];
    return `删除后，用户 ${userName} 的所有信息将被清空，且无法恢复`;
  }
  return '';
});

const fetchDeptTree = async () => {
  try {
    const res = await getDeptTree();
    deptTreeData.value = res;
    // 默认展开所有节点
    expanded.value = getAllNodeIds(deptTreeData.value);
  } catch (e) {
    console.log(e);
  }
};

// 获取所有节点 ID 用于默认展开
const getAllNodeIds = (data: any[]) => {
  let ids: any[] = [];
  data.forEach(item => {
    ids.push(item.value);
    if (item.children && item.children.length > 0) {
      ids = ids.concat(getAllNodeIds(item.children));
    }
  });
  return ids;
};

onMounted(() => {
  fetchData();
  fetchDeptTree();
});

const confirmVisible = ref(false);
const selectedRowKeys = ref<(string | number)[]>([]);

const resetIdx = () => {
  deleteIdx.value = -1;
};

const onConfirmDelete = () => {
  // 发起删除请求
  const selectedUser = data.value[deleteIdx.value];
  deleteUser([selectedUser.id]).then(() => {
    MessagePlugin.success('删除成功');
    fetchData();
  }).catch((e) => {
    MessagePlugin.error(e.message || '删除失败');
  }).finally(() => {
    confirmVisible.value = false;
    resetIdx();
  });
};

const onCancelDelete = () => {
  resetIdx();
};

const rowKey = 'id';

const rehandleSelectChange = (val: (string | number)[]) => {
  selectedRowKeys.value = val;
};

const onInput = () => {
  filterByText.value = (node: TreeNodeModel) => {
    return node.label.includes(filterText.value);
  };
};

const onTreeClick = (context: any) => {
  console.info('context:', context);
  
  // 从context中获取节点信息
  const { node } = context;
  if (node && node.data) {
    // 设置选中的部门ID
    selectedDeptId.value = node.data.id || '';
    console.info('选中部门ID:', selectedDeptId.value);
  }
  
  // 重置分页到第一页
  paginationData.pagination.current = 1;
  
  // 刷新数据
  fetchData();
};

const rehandlePageChange = (curr: unknown, pageInfo: any) => {
  const params = {
    userName: searchValue.value,
    deptId: selectedDeptId.value,
    // 添加筛选参数
    filterUserName: filterFormData.value.userName,
    filterUserCode: filterFormData.value.userCode,
    filterPhoneNumber: filterFormData.value.phoneNumber,
    filterStatus: filterFormData.value.status,
    createBeginTime: filterFormData.value.createTime && filterFormData.value.createTime.length > 0 ? filterFormData.value.createTime[0] : null,
    createendTime: filterFormData.value.createTime && filterFormData.value.createTime.length > 1 ? filterFormData.value.createTime[1] : null
  };
  paginationData.handlePageChange(pageInfo, loadUserData, params);
};

const rehandleChange = (changeParams: unknown, triggerAndData: unknown) => {
  console.log('统一Change', changeParams, triggerAndData);
};

// 弹窗相关
const formVisible = ref(false);
const isEdit = ref(false);
const editUserData = ref();

const handleAddUser = () => {
  isEdit.value = false;
  formVisible.value = true;
};

const handleEdit = async (row: any) => {
  try {
    // 在打开弹窗前先获取用户详情
    const userDetail = await getUserById(String(row.id));
    
    // 设置编辑状态和用户数据
    isEdit.value = true;
    editUserData.value = userDetail;
    
    // 打开弹窗
    formVisible.value = true;
  } catch (e) {
    console.error('获取用户详情失败:', e);
    MessagePlugin.error('获取用户详情失败，请重试');
  }
};

const handleDelete = (row: any) => {
  const index = data.value.findIndex(item => item.id === row.id);
  deleteIdx.value = index;
  confirmVisible.value = true;
};

// 批量删除
const handleBatchDelete = () => {
  if (selectedRowKeys.value.length === 0) {
    MessagePlugin.warning('请选择要删除的用户');
    return;
  }
  
  const userNames = data.value
    .filter(item => selectedRowKeys.value.includes(item.id))
    .map(item => item.userName)
    .join('、');
  
  const message = `确认删除用户：${userNames}？删除后将无法恢复。`;
  
  const dialog = DialogPlugin.confirm({
    header: '批量删除确认',
    body: message,
    confirmBtn: '删除',
    cancelBtn: '取消',
    onConfirm: () => {
      deleteUser(selectedRowKeys.value.map(String)).then(() => {
        MessagePlugin.success('批量删除成功');
        selectedRowKeys.value = [];
        fetchData();
      }).catch((e) => {
        MessagePlugin.error(e.message || '批量删除失败');
      });
      dialog.destroy();
    }
  });
};

// 弹窗成功回调
const onFormSuccess = () => {
  fetchData();
};

const headerAffixedTop = computed(
  () =>
    ({
      offsetTop: store.isUseTabsRouter ? 48 : 0,
      container: `.${prefix}-layout`,
    }) as any,
);
</script>

<style lang="less" scoped>
.table-tree-container {
  background-color: var(--td-bg-color-container);
  border-radius: var(--td-radius-medium);
  min-height: 600px;

  .t-tree {
    margin-top: var(--td-comp-margin-xxl);
  }
}

.list-tree-wrapper {
  overflow-y: hidden;
}

.list-tree-operator {
  width: 280px;
  float: left;
  padding: var(--td-comp-paddingTB-xxl) var(--td-comp-paddingLR-xxl);
}

.list-tree-content {
  border-left: 1px solid var(--td-border-level-1-color);
  overflow: auto;
  padding: var(--td-comp-paddingTB-xxl) var(--td-comp-paddingLR-xxl);
}

.left-operation-container {
  display: flex;
  align-items: center;
  margin: var(--td-comp-margin-xxl) 0;
  gap: 12px;

  .selected-count {
    display: inline-block;
    margin: 0;
    padding: 4px 12px;
    background-color: var(--td-brand-color-light);
    color: var(--td-brand-color);
    border-radius: var(--td-radius-small);
    font-size: 12px;
    font-weight: 500;
  }
}

.search-input {
  width: 360px;
}

// 添加筛选表单样式
.form-item-content {
  width: 100%;
}

.operation-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
</style>