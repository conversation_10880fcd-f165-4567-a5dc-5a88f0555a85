import { describe, it, expect, vi } from 'vitest';
import { usePagination } from '../usePagination';
import type { CommonPageResult } from '../usePagination';

describe('usePagination', () => {
  it('should initialize with default values', () => {
    const { pagination, dataSource, loading } = usePagination();
    
    expect(pagination.current).toBe(1);
    expect(pagination.pageSize).toBe(10);
    expect(pagination.total).toBe(0);
    expect(dataSource.value).toEqual([]);
    expect(loading.value).toBe(false);
  });

  it('should build correct params for default type', () => {
    const { buildPageParams } = usePagination({ paramType: 'default' });
    
    const params = buildPageParams({ name: 'test' });
    
    expect(params).toEqual({
      current: 1,
      size: 10,
      name: 'test'
    });
  });

  it('should build correct params for legacy type', () => {
    const { buildPageParams } = usePagination({ paramType: 'legacy' });
    
    const params = buildPageParams({ name: 'test' });
    
    expect(params).toEqual({
      page: 1,
      pageSize: 10,
      name: 'test'
    });
  });

  it('should handle page response correctly', () => {
    const { handlePageResponse, dataSource, pagination } = usePagination();
    
    const mockResponse: CommonPageResult<any> = {
      records: [{ id: 1, name: 'test' }],
      total: 100,
      current: 2,
      size: 20
    };
    
    handlePageResponse(mockResponse);
    
    expect(dataSource.value).toEqual([{ id: 1, name: 'test' }]);
    expect(pagination.total).toBe(100);
    expect(pagination.current).toBe(2);
    expect(pagination.pageSize).toBe(20);
  });

  it('should handle load data correctly', async () => {
    const { loadData, dataSource, pagination, loading } = usePagination();
    
    const mockLoadFn = vi.fn().mockResolvedValue({
      records: [{ id: 1 }],
      total: 50,
      current: 1,
      size: 10
    });
    
    await loadData({ current: 1, size: 10 }, mockLoadFn);
    
    expect(mockLoadFn).toHaveBeenCalledWith({ current: 1, size: 10 });
    expect(dataSource.value).toEqual([{ id: 1 }]);
    expect(pagination.total).toBe(50);
    expect(loading.value).toBe(false);
  });

  it('should handle errors gracefully', async () => {
    const { loadData, dataSource, pagination, loading } = usePagination();
    
    const mockLoadFn = vi.fn().mockRejectedValue(new Error('API Error'));
    
    // 模拟 console.error
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    
    await loadData({ current: 1, size: 10 }, mockLoadFn);
    
    expect(dataSource.value).toEqual([]);
    expect(pagination.total).toBe(0);
    expect(loading.value).toBe(false);
    expect(consoleSpy).toHaveBeenCalledWith('加载数据失败:', expect.any(Error));
    
    consoleSpy.mockRestore();
  });

  it('should generate correct table config', () => {
    const { tableConfig, pagination } = usePagination({
      defaultCurrent: 2,
      defaultPageSize: 20
    });
    
    const config = tableConfig.value;
    
    expect(config).toEqual({
      current: 2,
      pageSize: 20,
      total: 0,
      showJumper: true,
      showSizer: true,
      pageSizeOptions: [10, 20, 50, 100]
    });
  });
});