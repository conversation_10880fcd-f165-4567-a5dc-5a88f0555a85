package com.neo.module.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.neo.module.entity.SysRole;
import com.neo.module.entity.vo.SysRoleVO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 角色信息表 服务类
 *
 * <AUTHOR>
 * @since 2023-03-25
 */
public interface SysRoleService extends IService<SysRole> {

    IPage<SysRole> page(Map<String, String> params, QueryWrapper<SysRole> queryWrapper);

    List<SysRole> list(SysRole sysRole);

    List<String> getRolesByUserId(String id);

    List<String> getRolesIdsByUserId(String id);

    SysRoleVO getRoleById(String id);

    Boolean updateRole(SysRoleVO sysRoleVO);

}
